This project contains code that is used in the training of models.

The basic idea goes something like this:
1. For a given model, we will run:
	```
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml
	```
 	from the model's package directory (this is one of the subfolders under `models`)
2. By running from the package directory, the `async_trainer` will identify the repo, check that
that it is clean and build an image (note that each package has its own repo)
   1. For more info on how the image gets built, see `build_docker_image` in async_trainer.py
   1. The image will get tagged with the branch's short `SHA`
3. We assume that all packages have a `trainin/__main__.py` script that takes NO ARGUMENTS
   1. Currently these have a hardcoded `DEFAULT_CONFIG_MODULE` that can be overwritten
   2. We overwrite it when we run the local trainer (when running async-trainer the default is used)
   3. In this way, all packages work in the same way. It would be possible to change async_trainer
 to accept another optional config (the one corresponding to the model we are training and be able to run
`async-trainer` with custom configs, but not sure if this is the direction we all want to go)
