import json
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock, patch
from zoneinfo import ZoneInfo

from google.cloud.storage import Blob
from google.cloud.storage import Client as GCSClient

from training_utils.model_storage import ModelStorage

UTC = ZoneInfo("UTC")


class TestModelStorage:
    gcs_client = MagicMock(GCSClient)
    model_type = "test"
    model_identifier = "my_model"
    model_storage = ModelStorage(gcs_client, model_type, model_identifier)

    @patch("training_utils.model_storage.datetime")
    def test__local_filename(self, patched_datetime: MagicMock) -> None:
        patched_datetime.utcnow.return_value = datetime(2024, 1, 1, 9, tzinfo=UTC)
        computed = self.model_storage._local_model_filename(serialization_package_name="dill")
        expected = "test_my_model_2024-01-01T09:00:00+00:00.dill"
        assert computed == expected

    def test_file_and_paths_names(self) -> None:
        assert self.model_storage._gcs_folder_location() == "test/my_model"
        assert self.model_storage._gcs_file_location("test_file") == "test/my_model/test_file"
        assert self.model_storage._local_folder_location() == Path("/tmp/test/my_model")

    @patch.object(Blob, "download_as_string")
    def test_load_config_file(self, mocked_blob: MagicMock) -> None:
        """Test that when ModelStorage has no json_config we can correctly load it from storage"""
        dummy_config = {"a": 1, "b": "c"}
        assert self.model_storage.json_config == {}
        mocked_blob.return_value = json.dumps(dummy_config)
        self.model_storage.load_config_file()
        assert self.model_storage.json_config == dummy_config
