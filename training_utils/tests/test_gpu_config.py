import unittest
from unittest.mock import MagicMock, patch

from training_utils.utils import configure_gpu


class TestGPUConfig(unittest.TestCase):
    """Test the GPU configuration for PyTensor."""

    @patch("os.environ")
    def test_gpu_configuration_enabled(self, mock_environ: MagicMock) -> None:
        """Test that GPU configuration is correctly set when enabled."""
        # Configure PyTensor to use GPU
        configure_gpu(use_gpu=True)

        # Check that the environment variable was set correctly
        mock_environ.__setitem__.assert_called_with("PYTENSOR_FLAGS", "device=cuda,floatX=float32")

    @patch("os.environ")
    def test_gpu_configuration_disabled(self, mock_environ: MagicMock) -> None:
        """Test that GPU configuration is set to CPU when disabled."""
        # Configure PyTensor to use CPU
        configure_gpu(use_gpu=False)

        # Check that the environment variable was set correctly
        mock_environ.__setitem__.assert_called_with("PYTENSOR_FLAGS", "device=cpu")


if __name__ == "__main__":
    unittest.main()
