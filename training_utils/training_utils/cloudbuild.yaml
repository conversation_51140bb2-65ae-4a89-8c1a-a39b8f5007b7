steps:
  # Create a Docker builder instance.
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'buildx',
        'create',
        '--name', 'builder',
        '--driver', 'docker-container',
        # `network=cloudbuild` is required in order to allow the container to access the GCP
        # metadata service at *************** so that it can authenticate as the project's Cloud
        # Build service account.
        '--driver-opt', 'network=cloudbuild',
        '--use',
      ]

  # Run the build and publish it to the specified container regsitry.
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'buildx',
        'build',
        # Add this explicit host entry to allow the Google Auth service to resolve
        # metadata.google.internal for authentication purposes.
        '--add-host', 'metadata.google.internal:***************',
        # Use Artifact Registry as a container cache
        '--cache-to', 'type=registry,ref=${_GCP_REPOSITORY}/${_PROJECT},mode=max',
        '--cache-from', 'type=registry,ref=${_GCP_REPOSITORY}/${_PROJECT}',
        '-t', '${_GCP_REPOSITORY}/${_PROJECT}:${_VERSION}',
        '--output', 'type=image,name=${_GCP_REPOSITORY}/${_PROJECT},push=true',
        '--file', '${_DOCKERFILE}',
        '--build-arg', 'PROJECT_DIR=${_PROJECT_DIR}',
        '--build-arg', 'PROJECT_NAME=${_PROJECT_NAME}',
        '.',
      ]
substitutions:
    _VERSION: version
    _PROJECT: this-repo
    _GCP_REPOSITORY: gcp-repository
    _DOCKERFILE: ./Dockerfile
    _PROJECT_DIR: this-project
    _PROJECT_NAME: project-name
options:
  logging: CLOUD_LOGGING_ONLY