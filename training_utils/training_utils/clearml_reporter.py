from typing import Any

import matplotlib.image as mpimg
import matplotlib.pyplot as plt
import pandas as pd
from clearml import Task
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import BaseModel


class ClearMLBaseModel(BaseModel):
    project_name: str
    task_name: str
    tags: dict[str, str]
    offline_mode: bool = False
    # Set the initial time (seconds) to wait for iteration reporting to be used as x-axis for the resource monitoring, if timeout exceeds then reverts to seconds_from_start
    wait_for_first_iteration_to_start_sec: int | None = None


def _get_secret_manager_secret(client: SecretManagerServiceClient, secret_name: str) -> str:
    response = client.access_secret_version(request={"name": secret_name})
    return response.payload.data.decode()


def get_clearml_task(
    secret_manager_client: SecretManagerServiceClient,
    clearml_config: ClearMLBaseModel,
) -> Task:
    # Create a ClearML reporter to pass to the trainer using ClearML credentials stored in GCP
    clearml_key = _get_secret_manager_secret(
        secret_manager_client,
        "projects/21999772831/secrets/prod-clearml-self-hosted-api-key/versions/latest",
    )
    clearml_secret = _get_secret_manager_secret(
        secret_manager_client,
        "projects/21999772831/secrets/prod-clearml-self-hosted-api-secret/versions/latest",
    )
    Task.set_credentials(
        api_host="https://clearml.internal.apella.io:8008",
        web_host="https://clearml.internal.apella.io",
        files_host="https://clearml.internal.apella.io:8081",
        key=clearml_key,
        secret=clearml_secret,
    )

    project_name = clearml_config.project_name
    task_name = clearml_config.task_name
    data_tags = [f"{key}: {value}" for key, value in clearml_config.tags.items()]

    auto_resource_monitoring: bool | dict[str, int] = True
    if clearml_config.wait_for_first_iteration_to_start_sec:
        auto_resource_monitoring = {
            "wait_for_first_iteration_to_start_sec": clearml_config.wait_for_first_iteration_to_start_sec,
            "max_wait_for_first_iteration_to_start_sec": 3600,
        }

    Task.set_offline(clearml_config.offline_mode)
    clearml_task = Task.init(
        project_name=project_name,
        task_name=task_name,
        tags=data_tags,
        auto_resource_monitoring=auto_resource_monitoring,
    )
    return clearml_task


class ClearMLReporter:
    """
    Used to report training evaluation results to ClearML
    """

    def __init__(
        self,
        training_config: dict[str, Any],
        clearml_task: Task,
    ):
        self.clearml_task = clearml_task
        self.clearml_task.connect(training_config, name="Training Config")

    def report_dataframe_as_table(
        self, dataframe: pd.DataFrame, report_group: str, table_name: str
    ) -> None:
        self.clearml_task.get_logger().report_table(report_group, table_name, table_plot=dataframe)
        return

    def add_figure_to_report(self, figure_title: str, figure, report_image=False) -> None:  # type: ignore
        # ignoring typing here because "figure" is actually the matplotlib.pyplot module
        self.clearml_task.logger.report_matplotlib_figure(
            title=figure_title,
            series="Plots generated by fit",
            figure=figure,
            report_image=report_image,
        )
        return

    def report_series_as_scalars(self, s: pd.Series, title: str, epoch: int) -> None:
        """Report each value as its own trace, x axis will be epochs, the traces are
        named after the series index
        """
        for name, value in s.items():
            self.clearml_task.get_logger().report_scalar(title, name, value, epoch)

    def log_image_as_plot(self, title: str, plot_path: str) -> None:
        """
        Logs an image as a plot in the plot section of ClearML.
        """
        img = mpimg.imread(plot_path)
        fig = plt.figure()
        ax = fig.add_axes(
            (0.0, 0.0, 1.0, 1.0), frameon=False, aspect="auto", xticks=[], yticks=[]
        )  # no ticks
        ax.imshow(img)

        self.clearml_task.get_logger().report_matplotlib_figure(
            title=title, series="", figure=fig, report_interactive=False
        )
