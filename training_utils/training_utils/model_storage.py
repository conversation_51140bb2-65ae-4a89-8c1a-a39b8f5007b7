import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Any

import cloudpickle
import dill
import joblib
from google.cloud.storage import Blob
from google.cloud.storage import Client as GCSClient

from training_utils.model_protocol import BayesianModelProtocol, ModelProtocol

# Google storage
BUCKET_NAME = "prod-inference-models"
CONFIG_FILE_NAME = "config_file.json"
LOCAL_DATASETS_DIR = "/tmp/"
VALID_SERIALIZATION_PACKAGE_NAMES = ["joblib", "dill", "cloudpickle"]


class ModelStorage:
    def __init__(self, client: GCSClient, model_type: str, model_identifier: str):
        self.client = client
        self.model_identifier = model_identifier
        self.model_type = model_type
        self.bucket = client.bucket(BUCKET_NAME)
        self.json_config: dict[str, Any] = {}
        self.models_downloaded = False

    def __str__(self) -> str:
        return f"ModelStorage(model_type={self.model_type}, model_identifier={self.model_identifier}, bucket={self.bucket}). GCS Directory: {self._gcs_folder_location()}"

    def upload_to_google_storage(
        self,
        model: ModelProtocol | None = None,
        serialization_package_name: str = "joblib",
        filename: str | None = None,
    ) -> None:
        """
        Method to upload the specified folder location to GCS
        """
        if model is not None:
            self._serialize_model_locally(model, serialization_package_name, filename=filename)

        local_path = self._local_folder_location()
        assert os.path.isdir(local_path)
        for local_file in self._local_files_in_folder():
            if not os.path.isfile(local_file):
                # there should be no subdirectories
                continue
            gcs_file_name = self._gcs_file_location(os.path.basename(local_file))
            gcs_blob = self.bucket.blob(gcs_file_name)
            gcs_blob.upload_from_filename(local_file)

    def load_model(self, serialization_package_name: str = "joblib") -> BayesianModelProtocol:
        """This method loads an individual model"""
        assert serialization_package_name in VALID_SERIALIZATION_PACKAGE_NAMES
        if self.models_downloaded is False:
            self._download_from_google_storage()

        extension = (
            "pkl" if serialization_package_name == "cloudpickle" else serialization_package_name
        )
        local_paths = list(self._local_folder_location().glob(f"*.{extension}"))
        assert len(local_paths) == 1, (
            f"Expected to find exactly one model file, found {len(local_paths)}"
        )
        local_path = local_paths[0]

        if serialization_package_name == "joblib":
            return joblib.load(local_path)
        elif serialization_package_name == "dill":
            with open(local_path, "rb") as io:
                return dill.load(io)
        elif serialization_package_name == "cloudpickle":
            with open(local_path, "rb") as io:
                return cloudpickle.load(io)
        else:
            raise ValueError("Serialization Package Name must be joblib, dill, or cloudpickle.")

    def load_models(self, serialization_package_name: str = "joblib") -> dict[str, Any]:
        """This method loads individual models into a dict.
        Consider changing the logic such that the model is a dict (or whatever we want internally
        to represent all these models) and then this method is not needed anymore. We just
        load_model"""
        assert serialization_package_name in VALID_SERIALIZATION_PACKAGE_NAMES
        if self.models_downloaded is False:
            self._download_from_google_storage()

        fit_models: dict[str, Any] = {}
        for location in self.json_config["training_config"]["locations_to_fit"]:
            filename = f"{self.model_type}_{location}.{serialization_package_name}"
            local_path = self._get_local_data_file_location(filename)

            try:
                if serialization_package_name == "joblib":
                    fit_models[location] = joblib.load(local_path)
                elif serialization_package_name == "dill":
                    with open(local_path, "rb") as io:
                        fit_models[location] = dill.load(io)
                elif serialization_package_name == "cloudpickle":
                    with open(local_path, "rb") as io:
                        fit_models[location] = cloudpickle.load(io)
            except Exception as e:
                print(f"Error loading model {local_path}: {e}")
                continue

        return fit_models

    def load_config_file(self) -> None:
        if not self.json_config:
            # just load the config file
            gcs_directory = self._gcs_folder_location()
            blob = Blob(name=f"{gcs_directory}/{CONFIG_FILE_NAME}", bucket=self.bucket)
            config_str = blob.download_as_string()

            json_config = json.loads(config_str)
            self.json_config = json_config

    def load_nested_models(
        self,
        parent_variable_list: list[str],
        nesting_variable_list: list[str],
        serialization_package_name: str = "joblib",
    ) -> dict[str, Any]:
        """
        Iterates through nested models, mapping each loaded model to a location.
        Consider refactoring the model

        returns: dict mapping parent_variable_list to models
        """
        assert serialization_package_name in VALID_SERIALIZATION_PACKAGE_NAMES
        if self.models_downloaded is False:
            self._download_from_google_storage()

        fit_models: dict[str, Any] = {}
        for location in parent_variable_list:
            fit_models[location] = {}
            for nested_variable in nesting_variable_list:
                filename = (
                    f"{self.model_type}_{location}_{nested_variable}.{serialization_package_name}"
                )
                local_path = self._get_local_data_file_location(filename)

                try:
                    if serialization_package_name == "joblib":
                        fit_models[location][nested_variable] = joblib.load(local_path)
                    elif serialization_package_name == "dill":
                        with open(local_path, "rb") as io:
                            fit_models[location][nested_variable] = dill.load(io)
                    elif serialization_package_name == "cloudpickle":
                        with open(local_path, "rb") as io:
                            fit_models[location][nested_variable] = cloudpickle.load(io)
                except Exception as e:
                    print(f"Error loading model {local_path}: {e}")
                    continue

        return fit_models

    def rm_local_folder(self) -> None:
        """
        Remove the local directory
        """
        local_path = self._local_folder_location()
        if local_path.is_dir():
            shutil.rmtree(local_path)

    def _download_from_google_storage(self) -> None:
        """
        Method to download model from GCS locally
        """
        local_directory = self._local_folder_location()
        # create the directory if needed
        local_directory.mkdir(parents=True, exist_ok=True)
        # download the data
        gcs_directory = self._gcs_folder_location()
        # Getting all the blobs in the model folder. The "/" is added to prevent a mixup with other models that share the given prefix
        blobs = list(self.client.list_blobs(self.bucket, prefix=gcs_directory + "/"))

        for i, blob in enumerate(blobs, 1):
            if i % 100 == 0:
                print(f"Downloading progress: {i}/{len(blobs)}")
            filename = blob.name.split("/")[-1]
            local_file = self._local_folder_location() / filename
            blob.download_to_filename(str(local_file))

            if local_file.name == CONFIG_FILE_NAME:
                with open(local_file, "r") as config_file:
                    json_config = json.load(config_file)
                    self.json_config = json_config

        self.models_downloaded = True

    def _gcs_folder_location(self) -> str:
        return f"{self.model_type}/{self.model_identifier}"

    def _gcs_file_location(self, filename: str) -> str:
        return f"{self._gcs_folder_location()}/{filename}"

    def _local_folder_location(self) -> Path:
        return Path(f"{LOCAL_DATASETS_DIR}/{self.model_type}/{self.model_identifier}")

    def _local_files_in_folder(self) -> list[Path]:
        return [f for f in self._local_folder_location().iterdir()]

    def _get_local_data_file_location(self, file_name: str) -> Path:
        """
        Gets the path to the relevant folder on the local filesystem
        """
        return Path(f"{self._local_folder_location()}/{file_name}")

    def _local_model_filename(self, serialization_package_name: str) -> str:
        assert serialization_package_name in VALID_SERIALIZATION_PACKAGE_NAMES, (
            f"serialization_package_name {serialization_package_name} not supported, has to be either one of {VALID_SERIALIZATION_PACKAGE_NAMES}"
        )
        serialization_package_name = (
            "pkl" if serialization_package_name == "cloudpickle" else serialization_package_name
        )
        return f"{self.model_type}_{self.model_identifier}_{datetime.utcnow().isoformat()}.{serialization_package_name}"

    def _serialize_model_locally(
        self,
        model: ModelProtocol,
        serialization_package_name: str,
        overwrite: bool = True,
        filename: str | None = None,
    ) -> None:
        assert serialization_package_name in VALID_SERIALIZATION_PACKAGE_NAMES
        if filename is None:
            filename = self._local_model_filename(serialization_package_name)
        local_path = self._get_local_data_file_location(filename)
        print("about to make local directory")
        local_path.parent.mkdir(parents=True, exist_ok=True)
        print(f"directory {local_path} exists:", local_path.parent.is_dir())
        if not overwrite and local_path.exists():
            raise FileExistsError(f"File {local_path} already exists")
        if serialization_package_name == "dill":
            with open(local_path, "wb") as io:
                dill.dump(model, io)
        elif serialization_package_name == "joblib":
            with open(local_path, "wb") as io:
                joblib.dump(model, io)
        elif serialization_package_name == "cloudpickle":
            with open(local_path, "wb") as io:
                cloudpickle.dump(model, io)

    def find_raw_local_path(self, file_name: str) -> Path:
        """
        Finds the path to a file in the local directory.
        """
        if self.models_downloaded is False:
            self._download_from_google_storage()

        local_path = self._get_local_data_file_location(file_name)
        if not local_path.exists():
            raise FileNotFoundError(f"File {local_path} does not exist")
        return local_path
