from typing import Protocol

import numpy as np
import pandas as pd
from pandera.typing import Series


class ProbabilityModelProtocol(Protocol):
    """
    This is the protocol for all models that need to predict probabilities
    """

    def fit(self, X: pd.DataFrame, y: Series[bool]) -> None: ...

    def predict_proba(self, X: pd.DataFrame) -> pd.DataFrame: ...


class ClassificationModelProtocol(Protocol):
    """
    This is the protocol for all models that need to predict classes
    """

    def fit(self, X: pd.DataFrame, y: Series[bool]) -> None: ...

    def predict(self, X: pd.DataFrame) -> pd.DataFrame: ...


class BayesianModelProtocol(Protocol):
    """
    This is the protocol for all Bayesian models to generate prediction arrays
    """

    def predict(self, X: pd.DataFrame) -> np.typing.NDArray[np.float64]: ...

    # this method could be folded back into the model without requiring it in the protocol
    def preprocess_data(self, X: pd.DataFrame) -> pd.DataFrame: ...

    def predict_from_inference_dict(self, inference_dict: dict[str, str]) -> list[float]: ...


ModelProtocol = ProbabilityModelProtocol | ClassificationModelProtocol | BayesianModelProtocol
