FROM ubuntu:22.04 AS base

RUN apt-get update && \
    apt-get -y upgrade && \
    apt-get install -y --no-install-recommends \
    # this section can be removed once the ubuntu version is updated and has libexpat-dev >= 2.6.3 ==========
    ca-certificates \
    # for building from source
    build-essential \
    # for getting libexpat binary
    wget \
    # =============
    # for the entrypoint
    dumb-init \
    # install python3.10
    python3.10 \
    python3.10-distutils \
    python3.10-dev \
    # fix the libGL.so.1 error
    ffmpeg libsm6 libxext6 \
    # misc
    git \
    curl && \
    # Clean up cache and temporary files
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    # the following line can be removed once the ubuntu version is updated to include libexpat-dev >= 2.6.3
    && update-ca-certificates


# this section can be removed once the ubuntu version is updated and has libexpat-dev >= 2.6.3 ==========
# Set the libexpat version
ENV EXPAT_VERSION=2.6.3

# Download, compile and install libexpat
RUN wget https://github.com/libexpat/libexpat/releases/download/R_$(echo $EXPAT_VERSION | tr . _)/expat-${EXPAT_VERSION}.tar.gz \
    && tar xf expat-${EXPAT_VERSION}.tar.gz \
    && cd expat-${EXPAT_VERSION} \
    && ./configure \
    && make \
    && make install \
    && cd .. \
    && rm -rf expat-${EXPAT_VERSION} expat-${EXPAT_VERSION}.tar.gz
# ==================

# Update shared library cache
RUN ldconfig

# Install poetry
ENV POETRY_VERSION=1.5.1
RUN curl -sSL https://install.python-poetry.org | python3.10 - --version ${POETRY_VERSION}
ENV PATH="$PATH:/root/.local/bin"
RUN poetry self add keyrings.google-artifactregistry-auth

# Set up arguments -- these are passed in as part of cloudbuild
ARG PROJECT_DIR
ARG PROJECT_NAME
# NOTE: Do not delete this environment variable, it is used in the training script
ENV MODULE_TO_RUN=$PROJECT_NAME

# Set up the working dir under /app
ENV APP_HOME /app
WORKDIR $APP_HOME

# Copy the code to the container image
# Note: we are copying the whole repo. -- This also copies the pyproject.toml and poetry.locks.
COPY . .

# If we decide to only code the relevant files, we will need to something along the lines of the following
# COPY ${PROJECT_DIR}/. ./
# COPY training_utils/ .


# this ensures when we do poetry install, we're just installing the relevant libraries for this project.
WORKDIR $PROJECT_DIR

# Install dependencies
# Note: if we set tool.poetry values in the pyproject.toml, we can remove the --no-root from here
RUN poetry env use 3.10
RUN mkdir -p /home/<USER>/.config/gcloud

RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/home/<USER>/.config/gcloud/application_default_credentials.json \
  poetry install --only main,training --no-interaction --no-root

ENV GOOGLE_CLOUD_PROJECT=prod-ml-2fc132
ENV PYTHONUNBUFFERED=1

ENTRYPOINT ["dumb-init", "/app/training_utils/run_module_script.sh"]