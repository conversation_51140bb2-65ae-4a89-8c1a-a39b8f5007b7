import os
from pathlib import Path

import google
from git import <PERSON>o
from google.cloud.storage import Client as GCSClient
from tenacity import retry, stop_after_delay, wait_fixed


def get_repo_info() -> Repo:
    def find_project_root() -> Path:
        current = Path(os.getcwd())
        while current != Path("/"):
            if (current / ".git").is_dir():
                return current

            current = current.parent
        raise FileNotFoundError

    home = find_project_root()
    return Repo(home)


@retry(wait=wait_fixed(5), stop=stop_after_delay(60))
def ensure_gcp_auth_is_ready() -> None:
    """
    Sometimes we fail to get good credentials if we make a request too quickly upon this
    job starting. This method looks like it does nothing, but it waits until GCP auth credentials
    are ready.
    """
    # First, we get the default credentials, which should throw an error if they are invalid.
    credentials, project_id = google.auth.default()
    # But for some reason, they are still unresolved.  I think it is because the library knows
    # this is a compute instance, but it hasn't fetched the data from the metadata server to
    # get the actual client token and secret yet.  We need to force it resolve, so we now call
    # get_service_account_email, which seems to have an internal retry that actually works, and
    # so it takes up to 8 seconds to get the correct email.  At that point we can return because
    # we know that the credentials are ready.
    GCSClient(credentials=credentials).get_service_account_email()


def configure_gpu(use_gpu: bool = True) -> None:
    """
    Configure PyTensor to use GPU.

    This function must be called before any other PyTensor imports.

    Parameters
    ----------
    use_gpu : bool, optional
        Whether to use GPU, by default True
    """
    if use_gpu:
        # Set environment variables for PyTensor configuration
        # These will be read when PyTensor is imported
        os.environ["PYTENSOR_FLAGS"] = "device=cuda,floatX=float32"
        print("PyTensor configured to use GPU")
    else:
        # Use CPU configuration
        os.environ["PYTENSOR_FLAGS"] = "device=cpu"
        print("PyTensor configured to use CPU")
