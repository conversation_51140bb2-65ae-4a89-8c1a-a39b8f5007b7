[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
   # Trust black to get line length right. Without this, there are cases where black won't reflow a
     # line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]

[tool.mypy]
disallow_untyped_defs = true

[[tool.mypy.overrides]]
module = [
]
ignore_missing_imports = true


[tool.poetry]
name = "serving_utils"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
python-json-logger = "^2.0.7"
google-cloud-secret-manager = "^2.20.2"
fastapi = {extras = ["standard"], version = "^0.115.0"}
lib-python-logging = "^1.0.1"
pydantic = "^2.8.2"
opentelemetry-instrumentation-requests = "^0.47b0"
opentelemetry-propagator-b3 = "^1.26.0"
opentelemetry-instrumentation-aiohttp-client = "^0.47b0"
opentelemetry-instrumentation-fastapi = "0.47b0"
opentelemetry-api = "^1.26.0"
opentelemetry-sdk = "^1.26.0"
opentelemetry-exporter-otlp = "^1.26.0"
prometheus-fastapi-instrumentator = "^7.0.0"
aiohttp = "^3.11.18"


[tool.poetry.group.dev.dependencies]
mypy = "^1.8.0"
pytest = "^7.4.4"
pytest-cov = "^4.1.0"
ruff = "^0.1.14"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
