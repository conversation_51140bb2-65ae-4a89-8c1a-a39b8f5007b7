import re

from google.cloud.secretmanager_v1 import SecretManagerServiceClient
from pydantic import BaseModel, ConfigDict

import serving_utils.config as config


def get_secret_manager_secret(
    client: SecretManagerServiceClient, project_id: str, secret_name: str
) -> str:
    secret_id = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
    response = client.access_secret_version(name=secret_id)
    return response.payload.data.decode()


class VersionedModel(BaseModel):
    version: str
    service_version: str
    model_version: str

    # See https://docs.pydantic.dev/latest/api/config/#pydantic.config.ConfigDict.protected_namespaces
    model_config = ConfigDict(protected_namespaces=())


def get_service_version() -> str:
    version = config.get_project_version()
    try:
        matches = re.match(r".*(?P<sem_ver>v\d+.\d+.\d+)$", version)
        if matches:
            return matches.group("sem_ver")
    except Exception:
        pass

    return "UNKNOWN_SERVICE_VERSION"
