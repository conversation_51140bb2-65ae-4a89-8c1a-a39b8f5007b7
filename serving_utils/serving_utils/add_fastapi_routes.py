from pathlib import Path

from fastapi import APIRouter, FastAPI


def add_fastapi_routes(api: FastAPI, service_name: str, path: str = "app/endpoints") -> None:
    """
    Dynamically adds routes to a FastAPI application by scanning for API routers and service classes.

    This function performs two main tasks:
    1. Finds and includes any APIRouter instances defined in the modules
    2. Instantiates the specified service class and includes its router

    Args:
        api: The FastAPI application to add routes to
        path: The path to the directory containing the service classes
        service_name: The name of the service class to instantiate
    """
    # Find all Python files in the directory and subdirectories
    base_path = Path(path)
    local_modules = base_path.rglob("*.py")

    for module_file in local_modules:
        # Skip __init__.py files
        if module_file.is_file() and module_file.name != "__init__.py":
            module_stem = module_file.stem  # Module name without .py

            # Convert file path to module path format (relative to base_path)
            relative_path = module_file.relative_to(base_path)
            module_path_parts = list(relative_path.parts[:-1]) + [module_stem]
            module_path = ".".join(module_path_parts)

            # Import the module from app.endpoints package
            full_import_path = f"app.endpoints.{module_path}"

            try:
                module = __import__(full_import_path, fromlist=[module_stem])
            except (ImportError, AttributeError) as e:
                print(f"Error importing module {full_import_path}: {e}")
                continue

            # Inspect module attributes
            for attr_name in dir(module):
                attribute = getattr(module, attr_name)
                # Add any APIRouter instances to the FastAPI app
                if isinstance(attribute, APIRouter):
                    api.include_router(attribute)

                # If the attribute matches the service name, instantiate it and add its router
                if attr_name == service_name:
                    service = attribute()
                    api.include_router(service.router)
