import asyncio
import logging
from typing import Callable

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse

from serving_utils.config import get_request_timeout_seconds

logger = logging.getLogger(__name__)

REQUEST_TIMEOUT_SECONDS = get_request_timeout_seconds()
logger.info(f"Request timeout set to {REQUEST_TIMEOUT_SECONDS} seconds")


async def timeout_middleware(request: Request, call_next: Callable) -> Response:
    """
    Middleware that enforces a timeout limit on request processing.

    Wraps the request handling in an asyncio task with a configurable timeout.
    If the request processing exceeds the timeout, cancels the task and returns
    a 504 Gateway Timeout response. For other exceptions, returns a 500 Internal
    Server Error response.

    The timeout duration is configurable via the REQUEST_TIMEOUT_SECONDS environment
    variable (default: 10 seconds).

    Args:
        request: The incoming FastAPI request
        call_next: The next middleware or route handler in the chain

    Returns:
        Response: The response from the downstream handlers or an error response
    """
    task = None
    try:
        task = asyncio.create_task(call_next(request))
        return await asyncio.wait_for(task, timeout=REQUEST_TIMEOUT_SECONDS)
    except asyncio.TimeoutError:
        logger.warning(
            f"Request timed out after {REQUEST_TIMEOUT_SECONDS} seconds: {request.method} {request.url.path}"
        )
        if task:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.debug(
                    f"Request task cancelled successfully: {request.method} {request.url.path}"
                )
            except Exception as e:
                logger.error(f"Error waiting for cancelled task: {e}", exc_info=True)

        return JSONResponse(
            {"detail": "Request processing time exceeded limit"},
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
        )
    except Exception as e:
        logger.error(
            f"An error occurred processing request {request.method} {request.url.path}: {e}",
            exc_info=True,
        )
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            except Exception as cancel_exc:
                logger.error(
                    f"Error during task cancellation after exception: {cancel_exc}",
                    exc_info=True,
                )

        return JSONResponse(
            {"detail": "Internal Server Error"},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
