import logging

from opentelemetry import trace
from opentelemetry.trace import INVALID_SPAN_CONTEXT


class TraceIdFilter(logging.Filter):
    """Logging filter that adds trace and span IDs to the log record's json_fields."""

    def filter(self, record: logging.LogRecord) -> bool:
        span = trace.get_current_span()
        span_context = span.get_span_context()

        if span_context != INVALID_SPAN_CONTEXT and span_context.is_valid:
            trace_id = trace.format_trace_id(span_context.trace_id)
            span_id = trace.format_span_id(span_context.span_id)
            sampled = span_context.trace_flags.sampled

            record.traceID = trace_id
            record.spanID = span_id
            record.traceSampled = sampled
            setattr(record, "dd.trace_id", trace_id)
            setattr(record, "dd.span_id", span_id)

        return True
