import logging
import os
import re

from lib_python_logging.custom_json_formatter import CustomJsonFormatter
from opentelemetry import trace
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from pydantic import BaseModel, ConfigDict

import serving_utils.config as config
from serving_utils.trace_id_filter import TraceIdFilter

# the smallest bin size for duration metrics
_MIN_DURATION_BIN_S = 0.05
# the factor used to scale subsequent bins. e.g., next_bin = 1.5 * previous_bin
_BIN_SCALE_FACTOR = 1.5


def setup_json_logger(log_level: int = logging.INFO) -> logging.Logger:
    """Setup a logger with a JSON formatter for logging in Bentoml services. It does not modify the root logger to prevent conflicts with other BentoML's logging configuration, but it sets BentoML's logger to use the JSON formatter."""
    logger = logging.getLogger("bentoml")
    # Clear existing handlers/filters if any, to avoid duplicates on reload
    logger.handlers.clear()
    logger.filters.clear()  # Also clear logger-level filters if necessary

    logger.setLevel(log_level)

    json_log_handler = logging.StreamHandler()
    json_formatter = CustomJsonFormatter()
    json_log_handler.setFormatter(json_formatter)

    # Add the filter to inject trace/span IDs
    trace_filter = TraceIdFilter()
    json_log_handler.addFilter(trace_filter)

    logger.addHandler(json_log_handler)
    # Prevent logs from propagating to the root logger if it has handlers
    logger.propagate = False

    return logger


def get_number_of_workers() -> int | None:
    if os.environ.get("BENTOML_NUM_WORKERS"):
        return int(os.environ["BENTOML_NUM_WORKERS"])
    return None


def instrument_tracing() -> None:
    RequestsInstrumentor().instrument()
    AioHttpClientInstrumentor().instrument()


# TODO: understand why the AioHttpClientInstrumentor isn't properly instrumenting requests
# This method is only necessary as the AioHttpClientInstrumentor isn't
# properly instrumenting all aiohttp requests for some reason.
# After a lot of manual testing, this approach works, is slightly annoying
# and can be migrated appropraitely later.
def get_request_tracing_headers() -> dict[str, str]:
    return {
        "x-b3-traceid": hex(trace.get_current_span().get_span_context().trace_id)[2:],
        "x-b3-spanid": hex(trace.get_current_span().get_span_context().span_id)[2:],
        "x-b3-sampled": "1"
        if trace.get_current_span().get_span_context().trace_flags.sampled
        else "0",
    }


class VersionedModel(BaseModel):
    version: str
    service_version: str
    model_version: str

    # See https://docs.pydantic.dev/latest/api/config/#pydantic.config.ConfigDict.protected_namespaces
    model_config = ConfigDict(protected_namespaces=())


def get_service_version() -> str:
    version = config.get_project_version()
    try:
        matches = re.match(r".*(?P<sem_ver>v\d+.\d+.\d+)$", version)
        if matches:
            return matches.group("sem_ver")
    except Exception:
        pass

    return "UNKNOWN_SERVICE_VERSION"
