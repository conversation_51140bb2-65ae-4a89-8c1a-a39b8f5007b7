import logging

from lib_python_logging.custom_json_formatter import CustomJson<PERSON>ormatter

from serving_utils.trace_id_filter import Trace<PERSON>d<PERSON>ilter


def setup_json_logger(
    log_level: int = logging.INFO, logger_name: str = "uvicorn.access"
) -> logging.Logger:
    """Configures a specific logger to output logs in JSON format using CustomJsonFormatter.

    This function retrieves a logger by the provided `logger_name`, clears its
    existing handlers and filters, sets the specified `log_level`, adds a `StreamHandler`
    configured with `CustomJsonFormatter`, and adds a `TraceIdFilter` to inject
    tracing information into the logs.

    It intentionally avoids modifying the root logger to prevent conflicts with
    other logging configurations (e.g., from frameworks like FastAPI).

    Args:
        log_level: The logging level to set for the logger (e.g., logging.INFO).
        logger_name: The name of the logger to configure.

    Returns:
        The configured logger instance.
    """
    logger = logging.getLogger(logger_name)
    # Clear existing handlers and filters to prevent duplication
    logger.handlers.clear()
    logger.filters.clear()

    logger.setLevel(log_level)

    json_log_handler = logging.StreamHandler()
    json_formatter = CustomJsonFormatter()
    json_log_handler.setFormatter(json_formatter)

    # Add the filter to inject trace/span IDs
    trace_filter = TraceIdFilter()
    json_log_handler.addFilter(trace_filter)

    logger.addHandler(json_log_handler)
    # Prevent logs from propagating to the root logger if it has handlers
    logger.propagate = False

    return logger
