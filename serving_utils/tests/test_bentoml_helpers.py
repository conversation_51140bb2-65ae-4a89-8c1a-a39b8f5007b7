import logging
import os
from unittest.mock import patch

import pytest
from lib_python_logging.custom_json_formatter import CustomJsonFormatter

from serving_utils.bentoml_helpers import (
    get_number_of_workers,
    get_service_version,
    setup_json_logger,
)


def test_setup_json_logger() -> None:
    logger = setup_json_logger()
    assert logger.level == logging.INFO
    assert len(logger.handlers) == 1
    assert isinstance(logger.handlers[0], logging.StreamHandler)
    assert isinstance(logger.handlers[0].formatter, CustomJsonFormatter)


def test_get_semver() -> None:
    assert get_service_version() == "UNKNOWN_SERVICE_VERSION"

    os.environ["PROJECT_VERSION"] = "v1.2.3-my-service"
    assert get_service_version() == "UNKNOWN_SERVICE_VERSION"

    os.environ["PROJECT_VERSION"] = "my-ml-service-v1.222.321"
    assert get_service_version() == "v1.222.321"


class TestGetNumberOfWorkers:
    @patch.dict(os.environ, {"BENTOML_NUM_WORKERS": "3"})
    def test_returns_BENTOML_NUM_WORKERS(self) -> None:
        assert get_number_of_workers() == 3

    def test_when_no_BENTOML_NUM_WORKERS_defaults_to_1(self) -> None:
        os.environ.pop("BENTOML_NUM_WORKERS", None)

        assert get_number_of_workers() is None

    @patch.dict(os.environ, {"BENTOML_NUM_WORKERS": "not_int"})
    def test_when_BENTOML_NUM_WORKERS_is_not_int_raises_ValueError(self) -> None:
        with pytest.raises(ValueError):
            get_number_of_workers()
