{% extends bento_base_template %}
{% block SETUP_BENTO_BASE_IMAGE %}
{{ super() }}

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential wget zlib1g-dev libssl-dev libffi-dev libbz2-dev libsqlite3-dev libreadline-dev liblzma-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Version 1.3.1 has the fix for https://security-tracker.debian.org/tracker/CVE-2023-45853
ENV ZLIB_VERSION=1.3.1

# Download and extract zlib source since debian bullseye still uses 1.2.13
RUN wget http://zlib.net/zlib-$ZLIB_VERSION.tar.gz && \
    tar -xzf zlib-$ZLIB_VERSION.tar.gz && \
    rm zlib-$ZLIB_VERSION.tar.gz && \
    cd zlib-$ZLIB_VERSION && \
    ./configure --prefix=/usr --libdir=/usr/lib/aarch64-linux-gnu && \
    make && \
    make install && \
    ldconfig && \
    rm -rf /zlib-$ZLIB_VERSION

ENV LD_LIBRARY_PATH="/usr/local/lib:/usr/lib/aarch64-linux-gnu:$LD_LIBRARY_PATH"

# Download and build Python from source so it picks up the updated zlib
ENV PYTHON_VERSION=3.10.15
RUN wget https://www.python.org/ftp/python/$PYTHON_VERSION/Python-$PYTHON_VERSION.tgz && \
    tar -xzf Python-$PYTHON_VERSION.tgz && \
    rm Python-$PYTHON_VERSION.tgz && \
    cd Python-$PYTHON_VERSION && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    rm -rf /Python-$PYTHON_VERSION

# Set the newly compiled Python version as default
RUN ln -sf /usr/local/bin/python3.10 /usr/local/bin/python3 && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip3

# Clean up to reduce image size
WORKDIR /
RUN apt-get purge -y build-essential wget && \
    apt-get autoremove -y && \
    rm -rf /zlib-$ZLIB_VERSION && \
    rm -rf /var/lib/apt/lists/*
{% endblock %}

{% block SETUP_BENTO_COMPONENTS %}
{{ super() }}

# Set up arguments -- these are passed in as part of build command
ARG PROJECT_DIR
ARG PROJECT_NAME

ENV MODULE_TO_RUN=$PROJECT_NAME

# Install poetry. Then, export the requirements and install them using pip
ENV POETRY_VERSION=1.5.1
RUN curl -sSL https://install.python-poetry.org | python3.10 - --version ${POETRY_VERSION}
ENV PATH="$PATH:/root/.local/bin"
RUN poetry self add keyrings-google-artifactregistry-auth

# Create a symlink to the model code to make it easier for BentoML
RUN ln -s ${PROJECT_DIR}/${MODULE_TO_RUN} src/${MODULE_TO_RUN}

# Do not create a virtualenv since docker containers are already isolated
RUN poetry config virtualenvs.create false

# Install the dependencies and delete Poetry cache
RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/root/.config/gcloud/application_default_credentials.json \
    poetry install --directory src/${PROJECT_DIR} --without dev --no-interaction --no-root && \
    rm -rf /root/.cache/pypoetry

RUN echo "We are running this during bentoml containerize!"
{% endblock %}
