
# Online Feature Store Python SDK

This package allows Python code to access and modify feature values stored in the BigTable online
feature store.

An "online" feature store is one meant for serving feature values to models at inference time, as
opposed to feature stores that are more optimized for large batch queries for
training/experimentation purposes. For the remainder of this document, "feature store" will be used
as shorthand for "online feature store".


## Using the Feature Store

If consuming the feature store SDK from within the `ml-services` repo, simply add a relative path package dependency to your `pyproject.toml`

```
feature-store = {path = "../../../feature_store", develop = true}
```

From outside `ml-services`, the feature store SDK can be imported via our [internal artifact
registry])https://www.notion.so/apella/Python-Artifact-Registry-22b98657c8fe45da9b6b24d4f28689bf), like so:

```
feature-store = "^0.2.0"
```

From there, run `poetry install` to add the package to your virtualenv.

### Initialization

`FeatureStore` is a class with an interface to read and write features for a particular "entity"
type. It requires two arguments:
  * A BigTable instance - for the connection to BigTable
  * The entity type - for constructing the BigTable table name where the features for the entity are stored

The "entity" can be thought of as a class with features being analgous to attributes of the class.
Instances of an entity are defined by some unique indentifier. For example, a "creature" could be an
entity with features like "num_eyes", "num_legs", and "species_name".

Example initialization:
```python
from google.cloud.bigtable import Client

from feature_store import FeatureStore

bigtable_client = Client()
instance = bigtable_client.instance('example-bigtable-instance-id')
entity_name = "creatures"

feature_store = FeatureStore(instance, entity_name)
```

### Reading and Writing Features

Once you have a `FeatureStore` instance, you can read and write features for entity instances.

To write features to the store, call `FeatureStore.store_features()` with a DataFrame, where the
index values are the unique identifier for the entities, the column names are the feature names, and
the cell values are the individual feature values for each entity.

To read features from the store, call `FeatureStore.load_features()` with a `Set` of entity
identifiers and a `Sequence` of feature names to read for those entities.

See the [docstrings on each of these methods](feature_store/feature_store.py) for more information
about the API.

## BigTable implementation

See the [BigTable Terraform in the infrastructure repository](https://github.com/Apella-Technology/infrastructure/blob/59a3b194e8aace72dc3877c0defa61975e5ad12a/google-cloud/data-platform/modules/online-feature-store/main.tf)
for details about how the BigTable infrastructure is created for the feature store.

The feature store maps entity names into BigTable tables with an `ml_features_` prefix, so an entity
called `cases` would end up being stored in a BigTable table called `ml_features_cases`. Each
BigTable row represents an instance of an entity and has a key, derived from the index of the
DataFrame that's passed to `store_features()`. 

BigTable supports sparse rows, so a row can be created with a subset of the set of features that are
ultimately calculated for that entity. Entities with unpopulated feature values are ignored when
such a feature is specified in a call to `load_features()`.

Feature values are stored in BigTable cells, which are immutable but can have multiple versions.
Currently, we configure the BigTable entity tables to quickly garbage collect feature values which
aren't the latest since we generally don't care about them. The latest value is always read when
calling `load_features()`.

Column names and entity IDs are encoded as UTF-8 in the feature store. Feature values, on the other
hand, are encoded using `msgpack`, with some custom formats for supporting types like `datetime` and
the various notions that Pandas has of "null" (e.g. `NaT` and `NA`).

## Development

Use the standard `make format`, `make lint`, and `make test` to format, lint, and test the code
respectively.

### Publishing development packages

To publish the feature store for use in other repositories, follow these instructions.

First, ensure that you have a global repository configured for publishing (for some reason, Poetry
requires repositories that are published to to be global):

```
poetry config repositories.prod-python-registry https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/
```

Then, ensure you have the dynamic versioning plugin installed for Poetry:

```
poetry self add "poetry-dynamic-versioning[plugin]"
```

Finally, build and publish the package:

```
poetry publish -r prod-python-registry --build
```

This will create a Python package versioned with the most recent release version and the commit hash
(e.g. `0.3.0.post13.dev0+ae5f56a`). You can then specify that version in the other
repo's `pyproject.toml` and run `poetry install` to install it.

## Inspecting row values
Our BigTable rows are encoded in a complex way. To decode a row's values, you can use the script feature_store/scripts/decode_row.py. If this doesn't work, it's likely because updates were made to feature_store.FeatureStore._decode_ext.
The rows are frequently deleted, so these examples won't find anything. To find a row key of interest, use [BigTable's Builer UI](https://console.cloud.google.com/bigtable/instances/dev-general-ssd/studio/query?project=dev-data-platform-439b4c).

Example usage:  
```
python feature_store/scripts/decode_row.py --row_key=palo_alto_room_0#2024-11-22T00:48:51.000244+00:00#ATPA-OR1-CAM-0001
```

You can override the default values as well:  
```
python feature_store/scripts/decode_row.py --project_id=dev-data-platform-439b4c --instance_id=dev-general-ssd --table_id=ml_features_image_features --row_key=palo_alto_room_0#2024-11-19T21:40:44.421003+00:00#ATPA-OR1-CAM-0005
```