from feature_store.feature_set import generate_feature_set
from tests.test_feature_store import CreatureSchema


def test_generated_feature_set_includes_the_columns() -> None:
    feature_set = generate_feature_set(CreatureSchema, columns=["eyes", "legs"])

    feature_set_schema = feature_set.to_schema()

    assert "eyes" in feature_set_schema.columns
    assert "legs" in feature_set_schema.columns
    assert "leg_lengths" not in feature_set_schema.columns


def test_generated_feature_set_does_not_leak_if_multiple_are_generated() -> None:
    feature_set1 = generate_feature_set(CreatureSchema, columns=["eyes", "legs"])
    feature_set2 = generate_feature_set(CreatureSchema, columns=["eyes", "leg_lengths"])

    feature_set_schema1 = feature_set1.to_schema()
    feature_set_schema2 = feature_set2.to_schema()

    assert "eyes" in feature_set_schema1.columns
    assert "legs" in feature_set_schema1.columns
    assert "leg_lengths" not in feature_set_schema1.columns

    assert "eyes" in feature_set_schema2.columns
    assert "leg_lengths" in feature_set_schema2.columns
    assert "legs" not in feature_set_schema2.columns
