from __future__ import annotations

import os
import random
import string
import time
from collections.abc import Iterator, Sequence, Set
from datetime import date, datetime, timedelta, timezone
from socket import create_connection
from typing import Annotated, Any, Type
from zoneinfo import ZoneInfo

import docker
import google.api_core.exceptions  # noqa: F401
import numpy as np
import pandas as pd
import pandera as pa
import pytest
from google.api_core.client_options import ClientOptions
from google.cloud.bigtable import Client
from google.cloud.bigtable.instance import Instance
from google.cloud.bigtable.table import Table
from pandera.typing import Index

from feature_store import FeatureStore, TzNaiveDateTimeError
from feature_store.feature_set import generate_feature_set
from feature_store.feature_store import FeatureSetT, FeatureStoreDateTime


def wait_for_port(host: str, port: int, timeout: timedelta) -> None:
    start = time.monotonic()
    time_remaining = timeout.total_seconds()

    while True:
        try:
            with create_connection((host, port), timeout=time_remaining):
                break
        except Exception as e:
            time_elapsed = time.monotonic() - start
            time_remaining = timeout.total_seconds() - time_elapsed
            if time_remaining <= 0:
                print(f"Failed to connect within {timeout.total_seconds()} seconds")
                raise e

            pass


def wait_for_docker_port(
    client: docker.DockerClient,
    container_id: str,
    internal_port: int,
    timeout: timedelta | None = None,
) -> int:
    if timeout is None:
        timeout = timedelta(seconds=10)

    start = time.monotonic()
    while True:
        public_ports = client.api.port(container_id, internal_port)
        if public_ports:
            return int(public_ports[0]["HostPort"])
        else:
            now = time.monotonic()
            if now > start + timeout.total_seconds():
                raise RuntimeError(f"Timed out waiting for docker port {internal_port}")

            time.sleep(0.1)


class CreatureSchema(pa.DataFrameModel):
    class Config:
        strict = True

    idx: Index[str]
    eyes: int | None
    legs: float | None = pa.Field(nullable=True)
    name: str | None
    birthday: FeatureStoreDateTime | None = pa.Field(coerce=True, nullable=True)
    leg_lengths: list[float] | None = pa.Field(nullable=True)

    # Eventually, we'd like all datetimes stored in the feature store to be UTC so that we can use
    # a better dtype than `object`. This field shows how that might be done.
    utc_birthday: pa.typing.Series[Annotated[pd.DatetimeTZDtype, "ns", "utc"]] | None


class CreatureEyesAndLegsFeatureSchema(pa.DataFrameModel):
    class Config:
        strict = True

    idx: Index[str]
    eyes: int | None
    legs: float | None = pa.Field(nullable=True)


class CreatureBirthdayFeatureSchema(pa.DataFrameModel):
    class Config:
        strict = True

    idx: Index[str]
    birthday: FeatureStoreDateTime | None = pa.Field(coerce=True, nullable=True)


class TestFeatureStore:
    ENTITY_NAME = "creatures"

    @pytest.fixture(scope="session")
    @staticmethod
    def bigtable_emulator() -> Iterator[int]:
        client = docker.from_env()

        container = client.containers.run(
            "google/cloud-sdk:455.0.0-emulators",
            [
                "gcloud",
                "beta",
                "emulators",
                "bigtable",
                "start",
                "--host-port=0.0.0.0:8086",
            ],
            detach=True,
            remove=True,
            ports={"8086": None},
        )

        port = wait_for_docker_port(client, container.id, 8086)
        wait_for_port("127.0.0.1", port, timeout=timedelta(seconds=30))

        os.environ["BIGTABLE_EMULATOR_HOST"] = f"127.0.0.1:{port}"
        yield port
        del os.environ["BIGTABLE_EMULATOR_HOST"]

        # Graceful shutdown takes several seconds and is completely unncessary for this use case
        container.kill()

    @pytest.fixture(scope="session")
    @staticmethod
    def bigtable_admin_client(bigtable_emulator: int) -> Client:
        client_options = ClientOptions(api_endpoint=f"localhost:{bigtable_emulator}")
        return Client(client_options=client_options, admin=True)

    @pytest.fixture(scope="session")
    @staticmethod
    def bigtable_instance(bigtable_admin_client: Client) -> Iterator[Instance]:
        """
        The BigTable emulator doesn't actually have the notion of instances, so just make up any old ID.
        """
        instance_id = "".join(random.choices(string.ascii_lowercase, k=10))
        yield bigtable_admin_client.instance(instance_id)

    @pytest.fixture
    @staticmethod
    def entity_table(bigtable_instance: Instance) -> Iterator[Table]:
        table_id = f"ml_features_{TestFeatureStore.ENTITY_NAME}"
        table = bigtable_instance.table(table_id)

        while True:
            try:
                table.create()
                break
            except google.api_core.exceptions.ServiceUnavailable:
                # Wait for the BigTable emulator service to be available
                pass

        yield table

        table.delete()

    @pytest.fixture
    @staticmethod
    def feature_store(
        bigtable_instance: Instance, entity_table: Table
    ) -> Iterator[FeatureStore[CreatureSchema]]:
        entity_table.column_family("features").create()

        yield FeatureStore(bigtable_instance, TestFeatureStore.ENTITY_NAME, CreatureSchema)

    @staticmethod
    def assert_dataframes_equal(
        actual: pd.DataFrame, expected: pd.DataFrame, check_dtype: bool = True
    ) -> None:
        actual = actual.sort_index()
        # Remove bigtable metadata columns
        actual = actual.drop(columns=actual.filter(like="_bigtable_timestamp").columns)
        expected = expected.sort_index()
        pd.testing.assert_frame_equal(actual, expected, check_dtype=check_dtype)

    @staticmethod
    @pa.check_types
    def create_feature_df(
        data: dict[str, Sequence[Any]], *, columns: Sequence[str]
    ) -> pa.typing.DataFrame[CreatureSchema]:
        df = pd.DataFrame.from_dict(data, orient="index", columns=list(columns))
        return pa.typing.DataFrame[CreatureSchema](df)

    @staticmethod
    def store_creature_features(
        feature_store: FeatureStore[CreatureSchema],
        data: dict[str, Sequence[Any]],
        *,
        columns: Sequence[str],
    ) -> pa.typing.DataFrame[CreatureSchema]:
        df = TestFeatureStore.create_feature_df(data, columns=columns)
        feature_store.store_features(df)
        return df

    @staticmethod
    def load_creature_features(
        feature_store: FeatureStore[CreatureSchema], entities: Set[str], features: Type[FeatureSetT]
    ) -> pd.DataFrame:
        return feature_store.load_features(entities, features).entities

    def roundtrip_serialization_test(
        self,
        feature_store: FeatureStore[CreatureSchema],
        feature_name: str,
        data: object,
        output_feature_schema: Type[FeatureSetT],
        check_dtype: bool = True,
    ) -> None:
        serialized = self.store_creature_features(
            feature_store,
            {
                "entity": [data],
            },
            columns=[feature_name],
        )
        deserialized = self.load_creature_features(feature_store, {"entity"}, output_feature_schema)

        self.assert_dataframes_equal(deserialized, serialized, check_dtype=check_dtype)

    def test_serialize_integer(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class IntegerFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            eyes: int | None

        self.roundtrip_serialization_test(feature_store, "eyes", 2, IntegerFeatureSchema)

    def test_serialize_string(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class StringFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            name: str | None

        self.roundtrip_serialization_test(feature_store, "name", "bill", StringFeatureSchema)

    def test_serialize_list_of_floats(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class ListFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            leg_lengths: list[float] | None = pa.Field(nullable=True)

        self.roundtrip_serialization_test(
            feature_store, "leg_lengths", [1.0, 2.5, 12.0005], ListFeatureSchema
        )

    def test_serialize_list_returns_error_wrong_type(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        class ListFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            leg_lengths: list[float] | None = pa.Field(nullable=True)

        with pytest.raises(pa.errors.SchemaError) as exc_info:
            self.roundtrip_serialization_test(
                feature_store, "leg_lengths", [1.0, "not_a_float", 12.0005], ListFeatureSchema
            )
        assert "expected series 'leg_lengths' to have type list[float]" in str(exc_info.value)

    def test_serialize_date(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.roundtrip_serialization_test(
            feature_store, "birthday", date(1999, 12, 31), CreatureBirthdayFeatureSchema
        )

    def test_serialize_NaT(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        # dtypes aren't checked due to the behavior specified in
        # test_all_nat_datetimes_returned_as_tz_aware_dtype
        self.roundtrip_serialization_test(
            feature_store, "birthday", pd.NaT, CreatureBirthdayFeatureSchema, check_dtype=False
        )

    def test_serialize_NA(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        # Use birthday for this test since it's currently an object type and pd.NA is autodetected
        # as object.
        self.roundtrip_serialization_test(
            feature_store, "birthday", pd.NA, CreatureBirthdayFeatureSchema
        )

    def test_serialize_NaN(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class FloatFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            legs: float | None = pa.Field(nullable=True)

        self.roundtrip_serialization_test(feature_store, "legs", np.nan, FloatFeatureSchema)

    def test_serialize_utc_datetime(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class UTCFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            utc_birthday: pa.typing.Series[Annotated[pd.DatetimeTZDtype, "ns", "utc"]] | None

        self.roundtrip_serialization_test(
            feature_store,
            "utc_birthday",
            datetime(1999, 12, 31, 23, 59, 59, tzinfo=timezone.utc),
            UTCFeatureSchema,
        )

    def test_serialize_local_datetime(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        """
        Local timezones should be preserved in the serialized values, although UTC times are
        strongly recommended to avoid confusion.
        """
        serialized = self.store_creature_features(
            feature_store,
            {
                "human": [datetime(1986, 8, 14, 16, 21, 32, tzinfo=ZoneInfo("America/Chicago"))],
            },
            columns=["birthday"],
        )

        deserialized = self.load_creature_features(
            feature_store, {"human"}, CreatureBirthdayFeatureSchema
        )

        # Fudge the deserialized dataframe. Data goes in with the "America/Chicago" timezone, which
        # is normalized to UTC-05:00 for the date in question. When it comes back out via
        # load_features(), it's in its normalized state.
        assert deserialized["birthday"].dtype == pd.DatetimeTZDtype(tz="UTC-05:00")
        deserialized["birthday"] = deserialized["birthday"].astype(serialized["birthday"].dtype)

        self.assert_dataframes_equal(deserialized, serialized)

    def test_serialize_local_datetimes_with_different_timezones(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        """
        Pandas has the somewhat annoying behavior of baking the timezone into the dtype for a
        Series. This means that if you have two datetimes in a Series with different timezones,
        Pandas throws up its hands and decides to use the object dtype. This test simply documents
        that behavior.
        """
        serialized = self.store_creature_features(
            feature_store,
            {
                "human": [datetime(1986, 8, 14, 16, 21, 32, tzinfo=ZoneInfo("America/Chicago"))],
                "dancer": [
                    datetime(1986, 8, 14, 16, 21, 32, tzinfo=ZoneInfo("America/Los_Angeles"))
                ],
            },
            columns=["birthday"],
        )
        assert isinstance(serialized["birthday"].dtype, np.dtypes.ObjectDType)

        deserialized = self.load_creature_features(
            feature_store, {"human", "dancer"}, CreatureBirthdayFeatureSchema
        )
        assert isinstance(deserialized["birthday"].dtype, np.dtypes.ObjectDType)

        self.assert_dataframes_equal(deserialized, serialized)

    def test_tz_naive_datetime_raises_error(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        with pytest.raises(
            TzNaiveDateTimeError,
            match="Tried to store naive datetime '1986-08-14 00:00:00' for column 'birthday' in entity 'patrick'",
        ):
            self.store_creature_features(
                feature_store,
                {"patrick": [datetime(1986, 8, 14)]},
                columns=["birthday"],
            )

    def test_all_nat_datetimes_returned_as_tz_aware_dtype(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        self.store_creature_features(
            feature_store,
            {"neo": [pd.NaT], "cronus": [pd.NaT]},
            columns=["birthday"],
        )

        result = self.load_creature_features(
            feature_store, {"neo", "cronus"}, CreatureBirthdayFeatureSchema
        )

        assert result["birthday"].dtype == "datetime64[ns, UTC]"

    def test_mixed_datetimes_returned_as_tz_aware_dtype(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        self.store_creature_features(
            feature_store,
            {
                "patrick": [datetime(1986, 8, 14, tzinfo=timezone(timedelta(hours=-7)))],
                "cronus": [pd.NaT],
            },
            columns=["birthday"],
        )

        result = self.load_creature_features(
            feature_store, {"patrick", "cronus"}, CreatureBirthdayFeatureSchema
        )

        assert result["birthday"].dtype == "datetime64[ns, UTC-07:00]"

    def test_serialize_one_feature_at_a_time(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        # Add information about eye count
        eyes = self.store_creature_features(
            feature_store,
            {
                "human": [2],
                "dog": [2],
                "spider": [8],
            },
            columns=["eyes"],
        )
        feature_store.store_features(eyes)

        # Add information about leg count
        self.store_creature_features(
            feature_store,
            {
                "human": [2.0],
                "dog": [4.0],
                "spider": [8.0],
            },
            columns=["legs"],
        )

        # Retrieve both the eye and leg count
        deserialized = self.load_creature_features(
            feature_store, {"human", "dog", "spider"}, CreatureEyesAndLegsFeatureSchema
        )

        # The resulting dataframe should have eye and leg count information
        expected = self.create_feature_df(
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        self.assert_dataframes_equal(deserialized, expected)

    def test_serialize_one_entity_at_a_time(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
            },
            columns=["eyes", "legs"],
        )

        self.store_creature_features(
            feature_store,
            {
                "dog": [2, 4.0],
            },
            columns=["eyes", "legs"],
        )

        self.store_creature_features(
            feature_store,
            {
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        # The resulting dataframe should have all of the creatures' features
        deserialized = self.load_creature_features(
            feature_store, {"human", "dog", "spider"}, CreatureEyesAndLegsFeatureSchema
        )

        expected = self.create_feature_df(
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        self.assert_dataframes_equal(deserialized, expected)

    def test_update_features(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "spiderman": [2, 2.0],
                "werewolf": [2, 2.0],
            },
            columns=["eyes", "legs"],
        )

        overwritten = self.store_creature_features(
            feature_store,
            {
                "spiderman": [8, 8.0],
                "werewolf": [2, 4.0],
            },
            columns=["eyes", "legs"],
        )

        deserialized = self.load_creature_features(
            feature_store, {"spiderman", "werewolf"}, CreatureEyesAndLegsFeatureSchema
        )

        self.assert_dataframes_equal(deserialized, overwritten)

    def test_update_overlapping_features(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        """
        Show that a single call to store features can add new features to an existing creature and
        create a new creature with the given feature
        """
        self.store_creature_features(
            feature_store,
            {
                "human": [2],
            },
            columns=["eyes"],
        )

        self.store_creature_features(
            feature_store,
            {
                "human": [2.0],
                "dog": [4.0],
            },
            columns=["legs"],
        )

        deserialized = self.load_creature_features(
            feature_store, {"human"}, CreatureEyesAndLegsFeatureSchema
        )

        expected = self.create_feature_df(
            {
                "human": [2, 2.0],
            },
            columns=["eyes", "legs"],
        )

        self.assert_dataframes_equal(deserialized, expected)

    def test_deserialize_single_feature(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human": [2, date(1994, 8, 25)],
                "dog": [2, date(2000, 1, 31)],
                "spider": [8, date(1999, 12, 31)],
            },
            columns=["eyes", "birthday"],
        )

        deserialized = self.load_creature_features(
            feature_store, {"human", "dog", "spider"}, CreatureBirthdayFeatureSchema
        )

        expected = self.create_feature_df(
            {
                "human": [date(1994, 8, 25)],
                "dog": [date(2000, 1, 31)],
                "spider": [date(1999, 12, 31)],
            },
            columns=["birthday"],
        )

        self.assert_dataframes_equal(deserialized, expected)

    def test_request_entity_schema_throws_error(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        with pytest.raises(ValueError) as exc_info:
            _ = feature_store.load_features({"human", "dog", "spider"}, CreatureSchema)
        assert "Do not use the entity schema as the query schema" in str(exc_info.value)

    def test_request_nonexistent_entity(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        result = feature_store.load_features(
            {"sasquatch", "yeti"}, CreatureEyesAndLegsFeatureSchema
        )

        assert len(result.entities) == 0
        assert result.unknown_entities == {"sasquatch", "yeti"}
        assert len(result.entities_with_missing_features) == 0

    def test_request_nonexistent_feature(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        class NonExistantFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            appendix: str | None

        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        with pytest.raises(ValueError) as exc_info:
            _ = feature_store.load_features({"human", "dog", "spider"}, NonExistantFeatureSchema)
        assert (
            str(exc_info.value)
            == "Requested unexpected feature names: Keys ['appendix'] not found in schema columns!"
        )

    def test_request_feature_with_wrong_type(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        class IncorrectTypeFeatureSchema(pa.DataFrameModel):
            idx: Index[str]
            eyes: float | None

        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        with pytest.raises(ValueError) as exc_info:
            _ = feature_store.load_features({"human", "dog", "spider"}, IncorrectTypeFeatureSchema)
        assert "Requested unexpected type for eyes: float64 vs. stored features int64" in str(
            exc_info.value
        )

    def test_request_feature_missing_index(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        class MissingIndexFeatureSchema(pa.DataFrameModel):
            eyes: float | None

        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        with pytest.raises(ValueError) as exc_info:
            _ = feature_store.load_features({"human", "dog", "spider"}, MissingIndexFeatureSchema)
        assert (
            str(exc_info.value)
            == "Expected index <Schema Index(name=None, type=DataType(str))> but requested None"
        )

    def test_mixed_extant_and_missing_entities(
        self, feature_store: FeatureStore[CreatureSchema]
    ) -> None:
        class CreatureNameEyesLegsFeatureSchema(CreatureEyesAndLegsFeatureSchema):
            name: str | None

        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        self.store_creature_features(
            feature_store,
            {
                "human": ["Gavin"],
                "dog": ["Fido"],
            },
            columns=["name"],
        )

        result = feature_store.load_features(
            {"sasquatch", "human", "spider"}, CreatureNameEyesLegsFeatureSchema
        )

        expected_df = self.create_feature_df(
            {
                "human": [2, 2.0, "Gavin"],
            },
            columns=["eyes", "legs", "name"],
        )
        self.assert_dataframes_equal(result.entities, expected_df)
        assert result.unknown_entities == {"sasquatch"}
        assert result.entities_with_missing_features == {"spider"}

    def test_ask_for_no_entities(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        result = feature_store.load_features(set(), CreatureEyesAndLegsFeatureSchema)

        expected_df = pd.DataFrame.from_dict({}, orient="index", columns=["eyes", "legs"])

        self.assert_dataframes_equal(result.entities, expected_df)
        assert len(result.unknown_entities) == 0
        assert len(result.entities_with_missing_features) == 0

    def test_ask_for_key_range(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human_00": [2, 2.0],
                "human_01": [2, 1.0],
                "human_02": [2, 0.0],
                "human_03": [1, 2.0],
                "human_04": [2, 2.0],
            },
            columns=["eyes", "legs"],
        )

        result = feature_store.load_features_range(
            "human_01", "human_03", CreatureEyesAndLegsFeatureSchema
        )

        expected_df = self.create_feature_df(
            {
                "human_01": [2, 1.0],
                "human_02": [2, 0.0],
                "human_03": [1, 2.0],
            },
            columns=["eyes", "legs"],
        )

        self.assert_dataframes_equal(result.entities, expected_df)
        assert len(result.unknown_entities) == 0
        assert len(result.entities_with_missing_features) == 0

    def test_generated_feature_set_works(self, feature_store: FeatureStore[CreatureSchema]) -> None:
        self.store_creature_features(
            feature_store,
            {
                "human": [2, 2.0],
                "dog": [2, 4.0],
                "spider": [8, 8.0],
            },
            columns=["eyes", "legs"],
        )

        feature_set = generate_feature_set(CreatureSchema, columns=["eyes"])

        result = feature_store.load_features(entity_ids={"human", "dog"}, feature_set=feature_set)

        expected_df = self.create_feature_df(
            {
                "human": [2],
                "dog": [2],
            },
            columns=["eyes"],
        )
        self.assert_dataframes_equal(result.entities, expected_df)
        assert len(result.unknown_entities) == 0
        assert len(result.entities_with_missing_features) == 0
