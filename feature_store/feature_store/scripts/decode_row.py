import argparse
from datetime import date, datetime
from io import Bytes<PERSON>
from typing import Any

import msgpack
import pandas as pd
from google.cloud import bigtable  # type: ignore


def _decode_ext(code: int, data: bytes) -> Any:
    if code == 0:
        return datetime.fromisoformat(data.decode())
    elif code == 1:
        return date.fromisoformat(data.decode())
    elif code == 2:
        return pd.NaT
    elif code == 3:
        return pd.NA


def main(project_id: str, instance_id: str, table_id: str, row_key: str) -> None:
    # Initialize Bigtable client
    client = bigtable.Client(project=project_id)
    instance = client.instance(instance_id)
    table = instance.table(table_id)

    # Query a specific row by key
    row = table.read_row(row_key)

    if row:
        print(f"Row key: {row.row_key.decode('utf-8')}")
        for family, columns in row.cells.items():
            for column, cells in columns.items():
                for cell in cells:
                    decoded_value = msgpack.Unpacker(
                        BytesIO(cell.value),
                        ext_hook=_decode_ext,
                    ).unpack()
                    print(f"Family: {family}, Column: {column}, Value: {decoded_value}")
    else:
        print(f"Row with key '{row_key}' not found.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Decode a row from Bigtable")
    parser.add_argument(
        "--project_id", default="dev-data-platform-439b4c", help="Google Cloud project ID"
    )
    parser.add_argument("--instance_id", default="dev-general-ssd", help="Bigtable instance ID")
    parser.add_argument(
        "--table_id", default="ml_features_image_features", help="Bigtable table ID"
    )
    parser.add_argument("--row_key", required=True, help="Row key to query")

    args = parser.parse_args()

    main(args.project_id, args.instance_id, args.table_id, args.row_key)
