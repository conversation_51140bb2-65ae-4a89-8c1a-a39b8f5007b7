# This is required because pandas-stubs marks Series as taking a type argument but the actual Pandas
# Series class doesn't support []. This import tells Python to consider the [Any] a type argument at
# runtime.
from __future__ import annotations

from datetime import datetime
from typing import Any

import pandas as pd


def datetime_is_timezone_naive(d: datetime) -> bool:
    """
    Adapted from https://docs.python.org/3.10/library/datetime.html#determining-if-an-object-is-aware-or-naive
    """
    return d.tzinfo is None or d.tzinfo.utcoffset(d) is None


def convert_nat_columns_to_tz_aware_dtype(df: pd.DataFrame) -> pd.DataFrame:
    """
    Convert any columns in df that are all NaTs from the default timezone-naive dtype to a timezone-aware dtype.

    We allow pandas.NaT to be stored even though it's tz-naive. On the way out, coerce columns
    with all NaTs to a tz-aware dtype like they would have if they had even one row with a
    tz-aware datetime. Otherwise, code that tries to compare these times to tz-aware times
    will fail.
    """

    def maybe_convert_series(series: pd.Series[Any]) -> pd.Series[Any]:
        if pd.api.types.is_datetime64_any_dtype(series) and series.isna().all():
            return series.dt.tz_localize("UTC")

        return series

    return df.apply(maybe_convert_series)
