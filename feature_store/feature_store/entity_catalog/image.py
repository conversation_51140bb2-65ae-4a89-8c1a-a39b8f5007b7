from __future__ import annotations

import pandera as pa

from feature_store.feature_store import FeatureStoreDateTime


class ImageSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    # A string representation of $room_id $timestamp $camera_id.
    image_id: pa.typing.Index[str]

    camera_id: str | None
    frame_time: FeatureStoreDateTime | None
    room_id: str | None
    # Each embedding version has its own column.
    resnet34_imagenet1kv1_resize_crop_v1: list[float] | None = pa.Field(nullable=True)
    yolo_embedding_v1: list[float] | None = pa.Field(nullable=True)
