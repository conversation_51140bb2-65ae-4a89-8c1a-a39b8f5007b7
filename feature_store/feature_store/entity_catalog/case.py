import pandera as pa

from feature_store.feature_store import FeatureStoreDateTime


class CaseSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    actual_duration_mean: float | None
    actual_duration_var: float | None
    actual_start_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    add_on: int | None
    back_table_open_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    case_date: FeatureStoreDateTime | None
    case_procedure_list: list[str] | None
    case_type_short: str | None
    cumsum_scheduled_case_duration_so_far: int | None
    curr_case_end_offset: float | None = pa.Field(nullable=True)
    curr_case_start_offset: float | None = pa.Field(nullable=True)
    day_of_week: str | None
    end_offset_mean: float | None
    end_offset_var: float | None
    first_case: int | None
    first_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    first_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    first_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    first_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    first_primary_procedure: str | None
    first_primary_surgeon: str | None
    is_first_case: int | None
    is_flip_room: bool | None
    is_follows_flip_case: int | None
    last_case: int | None
    minutes_after_previous_case_scheduled_end: float | None = pa.Field(nullable=True)
    num_scheduled_cases: int | None
    num_times_patient_draped_in_case: int | None = pa.Field(nullable=True)
    num_times_patient_undraped_in_case: int | None = pa.Field(nullable=True)
    num_times_patient_xfer_to_bed_in_case: int | None = pa.Field(nullable=True)
    num_times_patient_xfer_to_or_table_in_case: int | None = pa.Field(nullable=True)
    number_first_cases_started_at_same_time: float | None = pa.Field(nullable=True)
    number_of_or_day_different_procedures: int | None
    number_of_or_day_different_surgeons: float | None
    org_id: str | None
    outpatient: int | None
    patient_class: str | None
    prev_case_end_offset: float | None = pa.Field(nullable=True)
    prev_case_id: str | None = pa.Field(nullable=True)
    prev_case_scheduled_end_time_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    prev_case_scheduled_start_time_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    prev_case_start_offset: float | None = pa.Field(nullable=True)
    prev_case_wheels_in_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    prev_case_wheels_out_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    prev_flip_case_id: str | None = pa.Field(nullable=True)
    prev_flip_case_scheduled_start_time_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    prev_flip_case_start_offset: float | None = pa.Field(nullable=True)
    prev_flip_case_wheels_in_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    procedure_count: float | None
    room: str | None
    room_id: str | None
    running_during_lunch: int | None
    schedule_actual_diff_normalized_mean: float | None
    schedule_actual_diff_normalized_var: float | None
    scheduled_duration: int | None
    scheduled_end_time_local: FeatureStoreDateTime | None
    scheduled_start_datetime_local: FeatureStoreDateTime | None
    scheduled_start_time_local: FeatureStoreDateTime | None
    scheduled_starting_hour: int | None
    scheduled_turnover_minutes: float | None = pa.Field(nullable=True)
    second_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    site_id: str | None
    sum_or_day_scheduled_minutes: int | None
    surgeon_count: float | None
    surgeon_list: str | None
    surgeon_proc_combo_num: str | None
    to_follow_case: int | None
    wheels_in_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    wheels_out_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
