# Feature Store Catalog

This directory contains Pythonic entity schemas for ML models used at Apella. These schemas are
defined as Pandera DataFrameModels which are then used as an argument to instantiate the
FeatureStore class. DataFrames passed to `store_features()` and returned from `load_features()` are
then validated against that schema.

This allows engineers to browse the known features for each entity in our system by examining the
Python code and gives us confidence that features don't suddenly change names or types.

Entity schemas in the catalog will have the following qualities:

* A single index of type `string`. The name isn't important.
* Configured with `strict = True`. This prevents extraneous, unknown columns from accidentally being
  added as features.
* Every field is optional - this allows a subset of the schema to be passed as a DataFrame to
  `store_features()` while still ensuring that data types are correct and incorrectly named or
  unknown features aren't accidentally added.
* For fields that are `nullable=True`, ensure that `coerce=True` is also passed. Otherwise, if the
  dataframe happens to contain all null values (`numpy.nan`, `pandas.NaT`, `pandas.NA`, etc) the
  column's dtype will likely be detected as `object` which will cause DataFrame validation to fail.
  This happens easily with `pandas.read_sql()`, for example.
* Datetimes are defined as `object`s. Pandas has a limitation in its dtypes which dictates that
  datetime columns can either be completely timezone-naive or are timezone-aware but all have the
  same timezone. Unfortunately, we currently store features with heterogenous local timezones, which
  causes Pandera datatype checking to fail. In order to force these columns to always have the
  `object` type, `coerce=True` should be passed to the `Field()` initializer. We may be able to use
  Pandera's support for custom data types to overcome this limitation.
