from __future__ import annotations

import pandera as pa

from feature_store.feature_store import FeatureStoreDateTime

# This is the type of this entity, meant to be used in the constructor of the FeatureStore class
# It does not include the prefix "ml_features_" because that is automatically prepended.
EVENT_MODEL_FORECASTS_ENTITY_TYPE = "event_model_forecasts"


class EventModelForecastSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    # The room_id is the key
    room_id: pa.typing.Index[str]

    inference_timestamp: FeatureStoreDateTime | None
    patient_wheels_out_timestamp: FeatureStoreDateTime | None = pa.Field(nullable=True)
    patient_wheels_out_confidence: float | None = pa.Field(nullable=True)
