import logging
from collections import defaultdict
from collections.abc import Sequence, Set
from dataclasses import dataclass
from datetime import date, datetime
from io import BytesIO
from typing import Generic, Type, TypeVar

import msgpack
import pandas as pd
import pandera as pa
from google.cloud.bigtable.batcher import MutationsBatcher
from google.cloud.bigtable.instance import Instance, Table
from google.cloud.bigtable.row_filters import CellsColumnLimitFilter
from google.cloud.bigtable.row_set import RowSet
from google.rpc.status_pb2 import Status

from feature_store.datetime import (
    convert_nat_columns_to_tz_aware_dtype,
    datetime_is_timezone_naive,
)

logger = logging.getLogger(__name__)


class TzNaiveDateTimeError(ValueError):
    """
    For dataframe dtype consistency reasons, the only timezone naive datetimes that can be stored in
    the feature store are pandas.NaT.
    """

    pass


EntitySchemaT = TypeVar("EntitySchemaT", bound=pa.DataFrameModel)
FeatureSetT = TypeVar("FeatureSetT", bound=pa.DataFrameModel)


@dataclass
class LoadFeaturesResult(Generic[FeatureSetT]):
    # Entities with the requested features as columns, in the order provided. Indexed by entity_id.
    entities: pa.typing.DataFrame[FeatureSetT]

    # Entities whose ID is not known to the feature store
    bigtable_timestamps: pd.DataFrame

    # Entities whose ID is not known to the feature store
    # This is only populated for queries that specify a specific set of entity IDs (not a range)
    unknown_entities: set[str]

    # Entities which are missing features that were asked for
    entities_with_missing_features: set[str]

    # The count of how many entities are missing each type of feature
    missing_features_count: dict[str, int]


# For now, because we store datetimes with heterogenous timezones in the same column, datetimes have
# to be typed with `object`. Once we move everything to UTC, we'll be able to give this a proper
# type (or just remove it altogether).
FeatureStoreDateTime = object


@dataclass
class FeatureStore(Generic[EntitySchemaT]):
    """
    A low-latency online feature store suitable for serving features at inference-time, backed by
    BigTable.

    To create an instance of the FeatureStore, you must pass it a BigTable Instance and entity type,
    like so::

        from google.cloud.bigtable import Client

        from feature_store import FeatureStore

        bigtable_client = Client()
        instance = bigtable_client.instance('example-bigtable-instance-id')
        entity_name = "creatures"

        feature_store = FeatureStore(instance, entity_name)
    """

    instance: Instance
    entity_type: str
    entity_schema: Type[EntitySchemaT]
    prefix: str = "ml_features"

    def _table_for_entity_type(self, entity_type: str, prefix: str) -> Table:
        """
        ML features are namespaced in BigTable under `ml_features`,
        so entity_type should not have that prefix.
        """
        return self.instance.table(f"{prefix}_{entity_type}")

    def _on_batch_completed(self, statuses: Sequence[Status]) -> None:
        logger.debug(f"Completed write batch of size {len(statuses)}: {statuses}")

    def _decode_ext(self, code: int, data: bytes) -> object:
        if code == 0:
            return datetime.fromisoformat(data.decode())
        elif code == 1:
            return date.fromisoformat(data.decode())
        elif code == 2:
            return pd.NaT
        elif code == 3:
            return pd.NA

        return msgpack.ExtType(code, data)

    def _validate_load_features_schema(self, requested_features: Type[FeatureSetT]) -> None:
        if requested_features is self.entity_schema:
            # If we use the entity schema as the query schema, and someone adds a new column to
            # the entity schema, but hasn't populated it yet, the query will return no rows because
            # there are missing columns that were queried for.
            # See: https://www.notion.so/apella/106af54ad37a80bca5cbdb8658518d5f
            raise ValueError("Do not use the entity schema as the query schema.")

        requested_features_schema = requested_features.to_schema()
        stored_features_schema = self.entity_schema.to_schema()
        if (
            not requested_features_schema.index
            or requested_features_schema.index != stored_features_schema.index
        ):
            raise ValueError(
                f"Expected index {stored_features_schema.index} but requested {requested_features_schema.index}"
            )
        try:
            features_stored_schema = stored_features_schema.select_columns(
                list(requested_features_schema.columns.keys())
            )
        except pa.errors.SchemaInitError as e:
            raise ValueError(f"Requested unexpected feature names: {e}")

        for column, attributes in requested_features_schema.columns.items():
            stored_dtype = features_stored_schema.columns[column].dtype
            if str(attributes.dtype) != str(stored_dtype):
                raise ValueError(
                    f"Requested unexpected type for {column}: {attributes.dtype} vs. stored features {stored_dtype}"
                )

    def store_features(
        self, entities: pa.typing.DataFrame[EntitySchemaT], timestamp: datetime | None = None
    ) -> None:
        """
        Store the features values for the given entities in BigTable.

        The feature values passed can be sparse (i.e. you can pass values for just a subset of the
        features defined for the entity); existing values for features not passed will remain in
        their current state, which may be unset. Once a feature value is set, there is currently no
        way to unset it.

        On error, the backing store will be in an indeterminate state; some writes may have
        completed successfully.

        Args:
            entities: The feature values to store. The index of the DataFrame defines the key used
                to refer to the entity in BigTable and the names of the DataFrame columns are
                used as the names of the features.

                Note: datetime features must be tz-aware and the timezone information is stored
                along with the value.

        """
        table = self._table_for_entity_type(self.entity_type, self.prefix)
        packer = msgpack.Packer()

        # The MutationsBatcher takes care of batching writes to the server. By default, it sends 100
        # at a time.
        with MutationsBatcher(table, batch_completed_callback=self._on_batch_completed) as batch:
            for index, *values in entities.itertuples(name=None):
                # Note: in order to preserve the same lexicographic sorting of indices that we have
                # in Python when the data is stored in BigTable, we need to ensure that the encoded
                # value preserves the sorting order. As such, we use UTF-8 encoding for strings,
                # which preserves lexicographic sort according to [RFC
                # 3239](https://www.rfc-editor.org/rfc/rfc3629.txt) Section 1.
                row = table.direct_row(index.encode("utf-8"))

                # Note: BigTable has support for numeric column values which can be atomically
                # incremented. However, the FeatureStore does not currently support these numeric
                # types. We would need to know that the column had a numeric type prior to decoding
                # it so that it could be properly decoded using BigTable's encoding rather than
                # msgpack. We could do this once we have some kind of feature
                # catalog to be passed to the store.
                for column_name, value in zip(entities.columns.values, values):
                    # Surprisingly, pd.NaT is an instance of their actual
                    # counterparts (e.g. isinstance(pd.NaT, datetime) returns True), so we have
                    # to check for them first.
                    if value is pd.NaT:
                        packer.pack_ext_type(2, b"")
                        encoded_value = packer.bytes()
                        packer.reset()
                    elif value is pd.NA:
                        packer.pack_ext_type(3, b"")
                        encoded_value = packer.bytes()
                        packer.reset()
                    elif isinstance(value, datetime):
                        if datetime_is_timezone_naive(value):
                            raise TzNaiveDateTimeError(
                                f"Tried to store naive datetime '{value}' for column '{column_name}' in entity '{index}'"
                            )
                        packer.pack_ext_type(0, value.isoformat().encode())
                        encoded_value = packer.bytes()
                        packer.reset()
                    elif isinstance(value, date):
                        packer.pack_ext_type(1, value.isoformat().encode())
                        encoded_value = packer.bytes()
                        packer.reset()
                    else:
                        encoded_value = packer.pack(value)
                    row.set_cell(
                        "features", column_name.encode(), encoded_value, timestamp=timestamp
                    )

                batch.mutate(row)

        logger.debug(
            f"Stored features for {len(entities)} entities of type '{self.entity_type}': {entities.columns.tolist()}"
        )

    def load_features(
        self,
        entity_ids: Set[str],
        feature_set: Type[FeatureSetT],
    ) -> LoadFeaturesResult[FeatureSetT]:
        """
        Load features values for the given entities from BigTable.

        Args:
            entity_ids: The entity IDs to load features for, can be empty
            feature_set: The DataFrameModel defining the feature names and their corresponding types to be returned.

        Returns:
            A DataFrame, indexed with the entity ID and with columns matching the given
            ``feature_set``

            If the value for one or more of the desired features for a given entity is unset or the
            entity is missing entirely from BigTable, it is omitted from the returned DataFrame, so
            len(entity_ids) is not necessarily equal to the number of rows in the returned
            DataFrame.

            The order of the entities in the returned DataFrame is not guaranteed, but the
            DataFrame's columns will be in the same order as they appear in ``feature_set``.
        """

        feature_names = list(feature_set.to_schema().columns.keys())
        # If you pass an empty RowSet to `read_rows()`, BigTable treats it as if you didn't pass a
        # RowSet at all, so we have to handle that case specially.
        if len(entity_ids) == 0:
            return LoadFeaturesResult(
                pa.typing.DataFrame[FeatureSetT](pd.DataFrame(columns=feature_names)),
                bigtable_timestamps=pd.DataFrame(),
                unknown_entities=set(),
                entities_with_missing_features=set(),
                missing_features_count={},
            )

        row_set = RowSet()
        for entity_id in entity_ids:
            row_set.add_row_key(entity_id.encode("utf-8"))

        return self.load_row_set(row_set, feature_set)

    def load_features_range(
        self,
        start_key: str,
        end_key: str,
        feature_set: Type[FeatureSetT],
        start_inclusive: bool = True,
        end_inclusive: bool = True,
    ) -> LoadFeaturesResult[FeatureSetT]:
        """
        Load feature values for a range of keys

        :param start_key: The start key of the range
        :param end_key: The end key of the range
        :param feature_set: The DataFrameModel defining the feature names and their corresponding types to be returned.
        :param start_inclusive: Whether the start key is inclusive
        :param end_inclusive: Whether the end key is inclusive
        """
        row_set = RowSet()

        row_set.add_row_range_from_keys(
            start_key=start_key.encode("utf-8"),
            end_key=end_key.encode("utf-8"),
            start_inclusive=start_inclusive,
            end_inclusive=end_inclusive,
        )

        return self.load_row_set(row_set, feature_set)

    def load_row_set(
        self,
        row_set: RowSet,
        feature_set: Type[FeatureSetT],
    ) -> LoadFeaturesResult[FeatureSetT]:
        """
        Load feature values for a RowSet

        :param row_set: The RowSet to load features for
        :param feature_set: The DataFrameModel defining the feature names and their corresponding types to be returned.
        """
        self._validate_load_features_schema(feature_set)
        table = self._table_for_entity_type(self.entity_type, self.prefix)
        feature_names = list(feature_set.to_schema().columns.keys())

        # Read the rows, making sure to only read the most recent version of each column. Without
        # this, frequently updated features can return several hundred versions of the feature if
        # BigTable is lazy about garbage collecting.
        rows = table.read_rows(row_set=row_set, filter_=CellsColumnLimitFilter(1))

        # If there are any row_keys specified, keep track of which we have not seen yet
        # by keeping a set of "unknown_entities"
        unknown_entities = set([row_key.decode("utf-8") for row_key in row_set.row_keys])

        # For any entities that are missing one or more features, keep track of them, and
        # do not include them in the final result
        entities_with_missing_features = set()

        missing_features_count: dict[str, int] = defaultdict(int)
        features_set = set(feature_names)

        df_dict = {}
        df_timestamp_dict = {}
        for row in rows:
            entity_id = row.row_key.decode("utf-8")
            if entity_id in unknown_entities:
                unknown_entities.remove(entity_id)

            feature_values = row.cells["features"]

            # Check if the row contains all the desired features. If not, drop it.
            returned_features = {f.decode() for f in feature_values}
            if not returned_features.issuperset(feature_names):
                # Keep track of how many entities are missing each type of feature
                for feature_name in features_set - returned_features:
                    missing_features_count[feature_name] += 1

                entities_with_missing_features.add(entity_id)
                continue

            # Decode the the desired features into the dictionary.  It's important that the list of
            # features be in the same order as the list of feature_names passed in because that
            # value is used for the column names below.
            # XXX: It would almost certainly be more efficient to filter this on the server side
            # using a ColumnRangeFilter, but BigTable won't tell you why a row was excluded if there
            # are multiple filters (e.g. a row_set and a ColumnRangeFilter). For debuggability, we
            # can start by doing the filtering on the client side and move it later if it becomes a
            # performance problem.
            def decode_value(value: bytes) -> object:
                return msgpack.Unpacker(
                    BytesIO(value),
                    ext_hook=self._decode_ext,
                ).unpack()

            entity_values = []
            entity_timestamps = []
            for f in feature_names:
                entity_values.append(decode_value(feature_values[f.encode()][0].value))
                entity_timestamps.append(feature_values[f.encode()][0].timestamp.timestamp())
            df_dict[entity_id] = entity_values
            df_timestamp_dict[entity_id] = entity_timestamps

        if df_dict:
            logger.debug(f"Loaded features for entities: {set(df_dict.keys())}")
            logger.debug(
                f"Loaded features for {len(df_dict)} entities of type '{self.entity_type}': {feature_names}"
            )

        if unknown_entities:
            logger.debug(f"Ignored unknown entities: {unknown_entities}")

        if entities_with_missing_features:
            logger.debug(
                f"Ignored entities with one or more missing features ({feature_names}): {entities_with_missing_features}"
            )

        df_columns = []
        df_timestamp_columns = []
        for feature_name in feature_names:
            df_columns.append(feature_name)
            df_timestamp_columns.append(f"{feature_name}_bigtable_timestamp")
        df = pd.DataFrame.from_dict(df_dict, orient="index", columns=list(df_columns))
        df = convert_nat_columns_to_tz_aware_dtype(df)
        df_timestamp = pd.DataFrame.from_dict(
            df_timestamp_dict, orient="index", columns=list(df_timestamp_columns)
        )

        return LoadFeaturesResult(
            pa.typing.DataFrame[FeatureSetT](df),
            bigtable_timestamps=df_timestamp,
            unknown_entities=unknown_entities,
            entities_with_missing_features=entities_with_missing_features,
            missing_features_count=missing_features_count,
        )
