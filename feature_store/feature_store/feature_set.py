from typing import Type

import pandera as pa


def generate_feature_set(
    entity_type: Type[pa.DataFrameModel], columns: list[str]
) -> Type[pa.DataFrameModel]:
    """
    Generates a feature set based on an EntitySchema and a list of columns to include.

    This is useful for generating a feature set dynamically at runtime.
    """

    # Generate a new DataFrameModel.  This generates a new one for every call to this function.
    class QueryModel(pa.DataFrameModel):
        pass

    entity_schema = entity_type.to_schema()
    schema = QueryModel.to_schema()
    schema.columns = {column: entity_schema.columns[column] for column in columns}
    schema.coerce = entity_schema.coerce
    schema.strict = entity_schema.strict
    schema.index = entity_schema.index

    return QueryModel
