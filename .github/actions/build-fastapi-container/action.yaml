---
name: Build FastAPI Docker Container
description: Builds a docker container using FastAPI and publishes to the container registry with all appropriate keys. See README for more information.

inputs:
  CONTAINER_REPOSITORY:
    required: true
    description: The name of the repository you'd like to upload the docker image to i.e. api-server
  CONTAINER_REGISTRY:
    required: false
    description: The docker container registry to upload the container to
    default: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry
  PROJECT_DIR:
    required: true
    description: The directory where the project is located
  PROJECT_NAME:
    required: true
    description: The name of the project
  PROJECT_VERSION:
    required: false
    description: The version of the project
  EXTRA_TAGS:
    required: false
    description: |
      Additional, space-delimited tags to add to the container image beyond the default ones added
      by the docker/metadata-action action
    default: ""
  ARTIFACT_REGISTRY_SA_CREDENTIALS:
    description: Artifact registry credentials. Default should be secrets.
    required: true

runs:
  using: "composite"

  steps:
    - name: Build Container
      uses: Apella-Technology/build-docker-container@v2
      with:
        DOCKERFILE: ${{ inputs.PROJECT_DIR }}/Dockerfile
        CONTAINER_REGISTRY: ${{ inputs.CONTAINER_REGISTRY }}
        CONTAINER_REPOSITORY: ${{ inputs.CONTAINER_REPOSITORY }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ inputs.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
        EXTRA_TAGS: ${{ inputs.EXTRA_TAGS }}
        DOCKER_BUILD_ARGS: |
          PROJECT_DIR=${{ inputs.PROJECT_DIR }}
          PROJECT_NAME=${{ inputs.PROJECT_NAME }}
          PROJECT_VERSION=${{ inputs.PROJECT_VERSION }}
