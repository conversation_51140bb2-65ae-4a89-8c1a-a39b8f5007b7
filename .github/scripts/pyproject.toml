[tool.poetry]
name = "ci scripts"
version = "0.1.0"
description = "Python scripts used in the CI workflows."
authors = ["Apella Engineering <<EMAIL>>", "Data Science & Machine Learning <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
ruff = "^0.11.2"
mypy = "^1.15.0"
pytest = "^8.3.5"
pyyaml = "^6.0.2"
types-pyyaml = "^6.0.12.20250326"

[tool.ruff]
line-length = 100

[tool.ruff.lint]
select = ["T201"]

[tool.mypy]
explicit_package_bases = "True"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
