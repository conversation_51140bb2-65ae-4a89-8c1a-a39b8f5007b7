import argparse
import yaml

from collections import OrderedDict


class SinglyQuoted(str):
    pass


class DoublyQuoted(str):
    pass


def ordered_load(stream, Loader=yaml.SafeLoader):
    class OrderedLoader(Loader):
        pass

    def construct_mapping(loader, node):
        loader.flatten_mapping(node)
        return OrderedDict(loader.construct_pairs(node))

    OrderedLoader.add_constructor(yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG, construct_mapping)
    return yaml.load(stream, OrderedLoader)


def ordered_dump(data, stream=None, Dumper=yaml.SafeDumper, **kwds):
    class OrderedDumper(Dumper):
        def increase_indent(self, flow=False, *args, **kwargs):
            return super().increase_indent(flow=flow, indentless=False)

    def _dict_representer(dumper, data):
        return dumper.represent_mapping(
            yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG, data.items()
        )

    def singly_quoted_presenter(dumper, data):
        return dumper.represent_scalar("tag:yaml.org,2002:str", data, style="'")

    def doubly_quoted_presenter(dumper, data):
        return dumper.represent_scalar("tag:yaml.org,2002:str", data, style='"')

    OrderedDumper.add_representer(OrderedDict, _dict_representer)
    OrderedDumper.add_representer(SinglyQuoted, singly_quoted_presenter)
    OrderedDumper.add_representer(DoublyQuoted, doubly_quoted_presenter)

    return yaml.dump(data, stream, OrderedDumper, **kwds)


def inject_version_into_bentofile(version, filepath="bentofile.yaml"):
    with open(filepath) as file:
        data = ordered_load(file, Loader=yaml.FullLoader)

    if "docker" in data:
        data["docker"]["env"] = [f"PROJECT_VERSION={version}"]

    # Add quotes to the service name, include items, and dockerfile_template, preserving the original template values
    if "service" in data:
        data["service"] = SinglyQuoted(data["service"])
    if "include" in data:
        data["include"] = [
            SinglyQuoted(item) if not item.startswith("*") else item for item in data["include"]
        ]
    if "docker" in data and "dockerfile_template" in data["docker"]:
        data["docker"]["dockerfile_template"] = DoublyQuoted(data["docker"]["dockerfile_template"])

    with open(filepath, "w") as file:
        ordered_dump(data, file, Dumper=yaml.SafeDumper, default_flow_style=False)


def main():
    parser = argparse.ArgumentParser(
        description="Inject a version number into bentofile.yaml under the docker key."
    )
    parser.add_argument("--version", type=str, help="Version number to inject.", required=True)
    parser.add_argument(
        "--file-path",
        type=str,
        default="bentofile.yaml",
        help="Path to the bentofile.yaml.",
    )
    args = parser.parse_args()

    inject_version_into_bentofile(args.version, args.file_path)


if __name__ == "__main__":
    main()
