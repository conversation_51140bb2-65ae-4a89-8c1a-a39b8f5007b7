import argparse
import os
import unittest
from unittest.mock import patch

from inject_version import main as inject_version_main


FILE_PATH = "./tests/mock_repo/mock_project_1/bentofile.yaml"

patch_default_runner = patch("configs.projects_config.DEFAULT_RUNNER", "mock-default-runner")
patch_argparse = patch("argparse.ArgumentParser.parse_args")


class TestInjectVersion(unittest.TestCase):
    def setUp(self):
        self.maxDiff = None

    def tearDown(self):
        with open(FILE_PATH, "w") as f:
            f.write(
                """service: 'hotdog_or_not.app:HotDogOrNot'
labels:
  owner: dsml
  project: predicting
include:
  - '*.py'
  - 'pyproject.toml'
  - 'poetry.lock'
  - 'README.md'

# Leave packages and requirements empty. We install these using poetry, bypassing BentoML
# python:
docker:
  dockerfile_template: "serving_utils/Dockerfile.bentoml.template"
  system_packages:
    - curl
"""
            )

    @patch_default_runner
    @patch_argparse
    def test_return_updated_yaml(self, mock_parse_args):
        EXPECTED_OUTPUT = """service: 'hotdog_or_not.app:HotDogOrNot'
labels:
  owner: dsml
  project: predicting
include:
  - '*.py'
  - 'pyproject.toml'
  - 'poetry.lock'
  - 'README.md'
docker:
  dockerfile_template: "serving_utils/Dockerfile.bentoml.template"
  system_packages:
    - curl
  env:
    - PROJECT_VERSION=hot-dog-or-not-v1.0.0
"""

        mock_parse_args.return_value = argparse.Namespace(
            file_path=FILE_PATH, version="hot-dog-or-not-v1.0.0"
        )

        inject_version_main()

        with open(FILE_PATH, "r") as f:
            file_content = f.read()
            self.assertEqual(file_content, EXPECTED_OUTPUT)

    @patch(
        "sys.argv",
        [
            "inject_version.py",
        ],
    )
    def test_withUnknownProjectType_exitsWithError(self):
        CLI_ERROR_CODE = 2

        with self.assertRaises(SystemExit) as cm:
            inject_version_main()

        self.assertEqual(cm.exception.code, CLI_ERROR_CODE)
