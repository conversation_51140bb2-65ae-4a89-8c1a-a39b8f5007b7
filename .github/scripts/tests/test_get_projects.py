import argparse
import os
import unittest
from unittest.mock import patch

from get_projects import main as get_projects_main

MOCK_REPO = "./tests/mock_repo"
GITHUB_OUTPUT_FILE = "/tmp/github_output"

patch_default_runner = patch("configs.projects_config.DEFAULT_RUNNER", "mock-default-runner")
patch_argparse = patch("argparse.ArgumentParser.parse_args")


class TestGetProjects(unittest.TestCase):
    def setUp(self):
        os.environ["GITHUB_OUTPUT"] = GITHUB_OUTPUT_FILE
        self.maxDiff = None

    def tearDown(self):
        try:
            os.remove(GITHUB_OUTPUT_FILE)
        except FileNotFoundError:
            pass

    @patch_default_runner
    @patch_argparse
    def test_forBentoProject_returnsCorrectProjects(self, mock_parse_args):
        expected_project_1 = f'{{"project_dir": "{MOCK_REPO}/mock_project_1", "project_name": "mock-project-1", "runner": "mock-default-runner"}}'
        expected_project_2 = f'{{"project_dir": "{MOCK_REPO}/mock_project_2", "project_name": "mock-project-2", "runner": "mock-default-runner"}}'
        expected_output = f"BENTO_PROJECTS=[{expected_project_1}, {expected_project_2}]"
        mock_parse_args.return_value = argparse.Namespace(
            type="bento",
            output_key="BENTO_PROJECTS",
            directory=MOCK_REPO,
            release_tag="",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch_default_runner
    @patch_argparse
    def test_forPoetryProject_returnsCorrectProjects(self, mock_parse_args):
        expected_project_1 = f'{{"project_dir": "{MOCK_REPO}/mock_project_1", "project_name": "mock-project-1", "runner": "mock-default-runner"}}'
        expected_project_3 = f'{{"project_dir": "{MOCK_REPO}/mock_project_3", "project_name": "mock-project-3", "runner": "mock-default-runner"}}'
        expected_output = f"POETRY_PROJECTS=[{expected_project_1}, {expected_project_3}]"
        mock_parse_args.return_value = argparse.Namespace(
            type="poetry",
            output_key="POETRY_PROJECTS",
            directory=MOCK_REPO,
            release_tag="",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch_default_runner
    @patch_argparse
    @patch("configs.projects_config.RUNNERS_CONFIG", {"mock-project-1": "mock-special-runner"})
    def test_withCustomRunnerConfig_returnsProjectWithCorrectRunner(self, mock_parse_args):
        expected_project_1 = f'{{"project_dir": "{MOCK_REPO}/mock_project_1", "project_name": "mock-project-1", "runner": "mock-special-runner"}}'
        expected_project_2 = f'{{"project_dir": "{MOCK_REPO}/mock_project_2", "project_name": "mock-project-2", "runner": "mock-default-runner"}}'
        expected_output = f"BENTO_PROJECTS=[{expected_project_1}, {expected_project_2}]"
        mock_parse_args.return_value = argparse.Namespace(
            type="bento",
            output_key="BENTO_PROJECTS",
            directory=MOCK_REPO,
            release_tag="",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch_default_runner
    @patch_argparse
    def test_withReleaseTag_returnsProjectsMatchingReleaseTag(self, mock_parse_args):
        expected_project = f'{{"project_dir": "{MOCK_REPO}/mock_project_2", "project_name": "mock-project-2", "runner": "mock-default-runner"}}'
        expected_output = f"BENTO_PROJECTS=[{expected_project}]"
        mock_parse_args.return_value = argparse.Namespace(
            type="bento",
            output_key="BENTO_PROJECTS",
            directory=MOCK_REPO,
            release_tag="mock-project-2-v1.0.0",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch_default_runner
    @patch_argparse
    @patch.dict("configs.projects_config.PROJECT_TYPES", {"bento": "nonexistent-file"})
    def test_whenNoFilesFound_returnsEmptyList(self, mock_parse_args):
        expected_output = "BENTO_PROJECTS=[]"
        mock_parse_args.return_value = argparse.Namespace(
            type="bento",
            output_key="BENTO_PROJECTS",
            directory=MOCK_REPO,
            release_tag="",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch_default_runner
    @patch_argparse
    @patch("configs.projects_config.NON_PROJECT_DIRS", ["mock_project_2"])
    def test_ignoresNonProjectDirs(self, mock_parse_args):
        expected_project_1 = f'{{"project_dir": "{MOCK_REPO}/mock_project_1", "project_name": "mock-project-1", "runner": "mock-default-runner"}}'
        expected_output = f"BENTO_PROJECTS=[{expected_project_1}]"
        mock_parse_args.return_value = argparse.Namespace(
            type="bento",
            output_key="BENTO_PROJECTS",
            directory=MOCK_REPO,
            release_tag="",
            only_changed=False,
            only_releasable=False,
        )

        get_projects_main()

        with open(GITHUB_OUTPUT_FILE, "r") as f:
            self.assertEqual(f.read(), expected_output)

    @patch(
        "sys.argv",
        [
            "get_projects.py",
            "--type",
            "unknown",
            "--output_key",
            "UNKNOWN_PROJECTS",
            "--directory",
            MOCK_REPO,
            "--release_tag",
            "",
            "--only_releasable",
        ],
    )
    def test_withUnknownProjectType_exitsWithError(self):
        CLI_ERROR_CODE = 2

        with self.assertRaises(SystemExit) as cm:
            get_projects_main()

        self.assertEqual(cm.exception.code, CLI_ERROR_CODE)
