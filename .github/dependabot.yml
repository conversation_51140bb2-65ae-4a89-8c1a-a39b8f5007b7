updates:
- directory: /realtime-processing/image-processor
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /realtime-processing/feature-processor
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/image-embedding/resnet_embedding
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/object-detection/apella_yolov5
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/forecast_combiner
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/static_start_offset
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/standalone_case_duration
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/event_model_forecasts
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/dynamic_case_end
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/case_duration
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/bayesian_case_duration
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/case_slot_ranker
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /models/forecasting/turnover
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /.github/scripts
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /serving_utils
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /feature_store
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
- directory: /training_utils
  groups:
    development-dependencies:
      dependency-type: development
      patterns:
      - '*'
    production-dependencies:
      dependency-type: production
      patterns:
      - '*'
  ignore:
  - dependency-name: '*'
    update-types:
    - version-update:semver-major
  package-ecosystem: pip
  schedule:
    interval: weekly
version: 2
