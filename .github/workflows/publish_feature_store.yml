name: Publish Feature Store to Artifact Regsitry

on:
  push:
    branches: [main]
  release:
    types: [published]
  workflow_dispatch:

jobs:
  Publish:
    defaults:
      run:
        working-directory: feature_store
        shell: bash

    runs-on: ubuntu-latest
    if: github.event_name != 'release' || startsWith(github.event.release.tag_name, 'feature-store-v')

    steps:
      - uses: actions/checkout@v4
        with:
          # This is required for the dynamic versioning plugin to get the tags it needs to produce
          # the correct version
          fetch-depth: 0

      - name: Setup Python environment
        uses: Apella-Technology/setup-python-env@v2
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.8.2
          python_version: '3.10'
          working_dir: feature_store

      - name: 'Install dynamic versioning plugin'
        run: poetry self add "poetry-dynamic-versioning[plugin]" 

      - id: google-auth
        name: 'Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}'
          export_environment_variables: false

      - name: <PERSON>uild and publish the library
        env:
          GOOGLE_APPLICATION_CREDENTIALS: '${{ steps.google-auth.outputs.credentials_file_path }}'
        run: |
          poetry config repositories.publish_target https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/
          poetry publish -r publish_target --build
