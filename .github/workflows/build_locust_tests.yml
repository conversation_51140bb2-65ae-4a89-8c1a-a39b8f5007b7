name: Build Images for Locust Tests

on:
  push:
    branches: [ main, vu/data-3196-fastapi-poc-yolo-model ]
  workflow_dispatch:

jobs:
  Build-Resnet-Embedding-Tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: Apella-Technology/build-docker-container@v1
        with:
          CONTAINER_REPOSITORY: ml-services/models/image-embedding/resnet_embedding/locust_tests
          DOCKER_BUILD_PATH: models/image-embedding/resnet_embedding
          DOCKERFILE: models/image-embedding/resnet_embedding/locust.Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}


  Build-Apella-Yolo-Tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: Apella-Technology/build-docker-container@v2
        with:
          CONTAINER_REPOSITORY: ml-services/models/object-detection/apella_yolov5/locust_tests
          DOCKER_BUILD_PATH: models/object-detection/apella_yolov5
          DOCKERFILE: models/object-detection/apella_yolov5/locust.Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
    