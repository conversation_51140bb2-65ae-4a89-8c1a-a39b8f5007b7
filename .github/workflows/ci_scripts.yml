name: <PERSON><PERSON> Scripts

on:
  push:
    branches:
      - main
    paths:
      - '.github/scripts/**'
      - '.github/workflows/ci_scripts.yml'
  pull_request:
    paths:
      - '.github/scripts/**'
      - '.github/workflows/ci_scripts.yml'
  merge_group:
    paths:
      - '.github/scripts/**'
      - '.github/workflows/ci_scripts.yml'

env:
  SCRIPTS_DIR: '.github/scripts'

jobs:
  Validate-Python-Scripts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: Apella-Technology/setup-python-env@v2
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.5.1
          python_version: '3.10'
          working_dir: ${{ env.SCRIPTS_DIR }}
      - name: Lint
        working-directory: ${{ env.SCRIPTS_DIR }}
        run: make python-lint
      - name: Run unit tests
        working-directory: ${{ env.SCRIPTS_DIR }}
        run: make python-test