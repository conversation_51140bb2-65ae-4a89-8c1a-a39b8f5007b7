name: Build Training Images for Trainable Projects

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  Find-Trainable-Models:
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.find-projects.outputs.projects }}
    steps:
      - uses: actions/checkout@v4
      
      - id: find-projects
        run: echo projects=$(find models/* -name __main__.py -path '*training*' | xargs dirname | xargs dirname  | xargs dirname | jq -R -s -c 'split("\n")[:-1]') >> "$GITHUB_OUTPUT"

  Build-Container:
    needs: Find-Trainable-Models
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project_dir: ${{fromJson(needs.Find-Trainable-Models.outputs.projects)}}
    steps:
      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          token_format: "access_token"
          
      - name: Free Disk Space (Ubuntu) # https://github.com/marketplace/actions/free-disk-space-ubuntu
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false
          
          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: false
          docker-images: true
          swap-storage: false

      - uses: actions/checkout@v4

      - id: project-name
        shell: bash
        run: echo name=$(basename ${{ matrix.project_dir}}) >> "$GITHUB_OUTPUT"

      - id: get-gpu-type
        shell: bash
        run: echo gpu_type=$(cat ${{ matrix.project_dir }}/async_training_config.yml | grep -E '^[^#]*gpu_type:' | awk '{print $2}') >> "$GITHUB_OUTPUT"

      - uses: Apella-Technology/build-docker-container@v1
        with:
          CONTAINER_REPOSITORY: ml-services/${{ matrix.project_dir }}/training
          DOCKERFILE: >-
            ${{ 
              steps.get-gpu-type.outputs.gpu_type != '' 
              && 'training_utils/training_utils/Dockerfile.gpu' 
              || 'training_utils/training_utils/Dockerfile.cpu' 
            }}
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          EXTRA_TAGS: latest-training
          DOCKER_BUILD_ARGS: |
             PROJECT_DIR=${{ matrix.project_dir }}
             PROJECT_NAME=${{ steps.project-name.outputs.name }}
