name: Build FastAPI Images

on:
  push:
    branches: [main]
  workflow_dispatch:
  release:
    types: [published]

jobs:
  Find-FastAPI-Services:
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.find-projects.outputs.projects }}
    steps:
      - uses: actions/checkout@v4

      - id: find-projects
        run: python3 .github/scripts/get_projects.py --type fastapi --output-key projects --directory=models --release-tag "${{ github.event.release.tag_name }}"

  Build-Container:
    needs: Find-FastAPI-Services
    if: ${{ needs.Find-FastAPI-Services.outputs.projects != '[]' }}
    strategy:
      matrix:
        project: ${{fromJson(needs.Find-FastAPI-Services.outputs.projects)}}
      fail-fast: false
    runs-on: ${{ matrix.project.runner }}
    steps:
      - name: Free Disk Space (Ubuntu) # https://github.com/marketplace/actions/free-disk-space-ubuntu
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false
          
          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: false
          docker-images: true
          swap-storage: false

      - uses: actions/checkout@v4

      - id: project-name
        shell: bash
        run: echo name=$(basename ${{ matrix.project.project_dir}}) >> "$GITHUB_OUTPUT"

      - uses: ./.github/actions/build-fastapi-container
        with:
          CONTAINER_REPOSITORY: ml-services/${{ matrix.project.project_dir }}/bentoml
          ARTIFACT_REGISTRY_SA_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          EXTRA_TAGS: latest-build
          PROJECT_DIR: ${{ matrix.project.project_dir }}
          PROJECT_NAME: ${{ steps.project-name.outputs.name }}
          PROJECT_VERSION: ${{ github.event.release && github.event.release.tag_name && github.event.release.tag_name }}
