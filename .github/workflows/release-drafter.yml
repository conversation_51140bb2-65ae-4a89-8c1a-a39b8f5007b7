name: Release Management

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize, edited]

jobs:
  Find-Python-Projects:
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.find-projects.outputs.projects }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for all branches

      - id: find-projects
        run: |
          python3 .github/scripts/get_projects.py --type poetry --output-key projects --only-changed --only-releasable

      - name: List all projects
        run: echo '${{ steps.find-projects.outputs.projects }}'

  update_draft_release:
    strategy:
      matrix:
        project: ${{fromJson(needs.Find-Python-Projects.outputs.projects)}}
    runs-on: ubuntu-latest
    needs:
      - Find-Python-Projects
    steps:
      - uses: release-drafter/release-drafter@v6
        # skip a certain project if the release-drafter-<project>.yml has not yet been merged
        # change "foo" to the <project> in question   
        if: ${{ matrix.project.project_name != 'change-me-im-not-a-model' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # allows autolabeler to run without unmerged PRs from being added to draft
          disable-releaser: ${{ github.ref_name != github.event.repository.default_branch }}
          config-name: release-drafter-${{matrix.project.project_name}}.yml
