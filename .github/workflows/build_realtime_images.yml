name: Build Images for Realtime Processing Projects

on:
  push:
    branches: [ main ]
  workflow_dispatch:
  pull_request:
  release:
    types: [published]

jobs:
  Build-Image-Processor:
    runs-on: ubuntu-latest
    if: github.event_name != 'release' || startsWith(github.event.release.tag_name, 'image-processor-v')
    steps:
      - uses: actions/checkout@v4

      - uses: Apella-Technology/build-docker-container@v1
        with:
          CONTAINER_REPOSITORY: ml-services/realtime-processing/image-processor
          DOCKERFILE: realtime-processing/image-processor/Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}

  Build-Feature-Processor:
    runs-on: ubuntu-latest
    if: github.event_name != 'release' || startsWith(github.event.release.tag_name, 'feature-processor-v')
    steps:
      - uses: actions/checkout@v4

      - uses: Apella-Technology/build-docker-container@v1
        with:
          CONTAINER_REPOSITORY: ml-services/realtime-processing/feature-processor
          DOCKERFILE: realtime-processing/feature-processor/Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
