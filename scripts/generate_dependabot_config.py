import os
import yaml

def _get_directories_with_poetry_lock_file():
    """
    returns the list of directories that contain a poetry.lock file
    """
    directories = []
    rootdir = os.getcwd()
    for subdir, _, files in os.walk(rootdir):
        for file in files:
            if file == "poetry.lock":
                directories.append(subdir.replace(rootdir, ""))
    return directories


def main():
    """
    Updates dependabot.yml to have the same dependabot configuration for all poetry.lock containing directories.
    ml-services has many sub-projects and there's no way to apply a single configuration to all sub-directories,
    so every sub-project dir has it's own configuration in dependabot.yml. This configuration batch updates
    dependencies (`groups` causes the batch updates). The `ignore` section with `semver-major` prevents
    major-version updates.
    """
    directories = _get_directories_with_poetry_lock_file()
    dependabot_config = {
        "version": 2,
        "updates": [
            {
                "package-ecosystem": "pip",
                "directory": directory,
                "schedule": {
                    "interval": "weekly"
                },
                "ignore": [
                    {
                        "dependency-name": "*",
                        "update-types": [
                            "version-update:semver-major"
                        ]
                    }
                ],
                "groups": {
                    "production-dependencies": {
                        "dependency-type": "production",
                        "patterns": [
                            "*"
                        ]
                    },
                    "development-dependencies": {
                        "dependency-type": "development",
                        "patterns": [
                            "*"
                        ]
                    }
                }
            } for directory in directories
        ]
    }

    # update dependabot.yml file with newly generated config
    with open('.github/dependabot.yml', 'w') as file:
        yaml.dump(dependabot_config, file, default_flow_style=False)

if __name__ == "__main__":
    main()
