MAKEFILE_DIR=$(realpath $(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run pytest tests/

container:
	docker build --secret id=google-application-credentials,src=${HOME}/.config/gcloud/application_default_credentials.json -t image-processor -f $(MAKEFILE_DIR)/Dockerfile $(MAKEFILE_DIR)/../..

run-dev:
	poetry run image_processor \
		--subscription 'projects/dev-data-platform-439b4c/subscriptions/dev-image-processing-pull-subscription' \
		--bentoml-yolov5-deployment 'http://localhost:9981' \
		--image-embedding-deployment 'http://localhost:3000' \
		--feature-store-project 'dev-data-platform-439b4c' \
		--feature-store-instance 'dev-general-ssd' \
		--image-embedding-output-topic 'projects/dev-data-platform-439b4c/topics/dev-customer-image-embedding-outputs' \
		--object-stats-output-topic 'projects/dev-data-platform-439b4c/topics/dev-customer-image-processing-outputs' \
		--prometheus-port '8000' \
		--calc-image-embeddings 'True'
