[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
strict = true

[[tool.mypy.overrides]]
module = [
    "docker.*",
    "google.cloud.*",
    "google.protobuf.*",
    "requests.models.*",
]
ignore_missing_imports = true

[tool.poetry]
name = "image-processor"
version = "0.1.0"
description = "Processes images"
authors = ["Apella Engineering <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
google-cloud-pubsub = "^2.21.1"
google-cloud-storage = "^2.14.0"
lib-python-logging = "^1.0.0"
numpy = "^1.26.4"
opencv-python-headless = "^********"
prometheus-client = "^0.20.0"
typing-extensions = "^4.10.0"
urllib3 = "^2.2.1"
feature-store = {path = "../../feature_store", develop = true}
pandas = "^2.2.2"
pandas-stubs = "^2.2.2.240603"
pillow = "^11.0.0"
fastavro = "^1.11.1"

[tool.poetry.group.dev.dependencies]
docker = "^7.0.0"
mypy = "^1.8.0"
pytest = "^8.0.0"
pytest-httpserver = "^1.0.9"
ruff = "^0.3.0"

[tool.poetry.scripts]
image_processor = 'image_processor.cli:main'

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
   # Trust ruff format to get line length right. Without this, there are cases where ruff won't
   # reflow a line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`), isort (`I001`) and disallow print (`T201`)
select = ["E", "F", "I001", "T201"]
