from image_processor.object_detection_definitions import BoundingBox


def test_from_json_returns_correct_bounding_box() -> None:
    bounding_box = BoundingBox.from_json(
        {
            "name": "coffee_mug",
            "confidence": 0.9,
            "xmin": 0.1,
            "ymin": 0.2,
            "xmax": 0.3,
            "ymax": 0.4,
        }
    )

    assert bounding_box.object_name == "coffee_mug"
    assert bounding_box.confidence == 0.9
    assert bounding_box.x1 == 0.1
    assert bounding_box.y1 == 0.2
    assert bounding_box.x2 == 0.3
    assert bounding_box.y2 == 0.4


def test_from_json_when_missing_field_raises_exception() -> None:
    try:
        BoundingBox.from_json(
            {
                "name": "coffee_mug",
                "confidence": 0.9,
                "xmin": 0.1,
                "ymin": 0.2,
                "ymax": 0.4,
            }
        )
        assert False
    except KeyError as e:
        assert str(e) == "'xmax'"


def test_from_json_when_extra_field_returns_correct_bounding_box() -> None:
    bounding_box = BoundingBox.from_json(
        {
            "name": "coffee_mug",
            "confidence": 0.9,
            "xmin": 0.1,
            "ymin": 0.2,
            "xmax": 0.3,
            "ymax": 0.4,
            "extra": "field",
        }
    )

    assert bounding_box.object_name == "coffee_mug"
    assert bounding_box.confidence == 0.9
    assert bounding_box.x1 == 0.1
    assert bounding_box.y1 == 0.2
    assert bounding_box.x2 == 0.3
    assert bounding_box.y2 == 0.4
