from pathlib import Path
from typing import Final

from image_processor import color_detection

RESOURCE_DIR: Final = Path(__file__).parent / "resources"


def test_color_extraction_greenframe() -> None:
    image_bytes = (RESOURCE_DIR / "green.jpg").read_bytes()

    colors = color_detection.get_average_color_of_frame(image_bytes)

    assert colors == color_detection.AverageFrameColor(R=0, G=0.996078431372549, B=0)


def test_color_extraction_blue_frame() -> None:
    image_bytes = (RESOURCE_DIR / "blue.jpg").read_bytes()

    colors = color_detection.get_average_color_of_frame(image_bytes)
    assert colors == color_detection.AverageFrameColor(R=0, G=0, B=0.996078431372549)


def test_color_extraction_red_frame() -> None:
    image_bytes = (RESOURCE_DIR / "red.jpg").read_bytes()

    colors = color_detection.get_average_color_of_frame(image_bytes)
    assert colors == color_detection.AverageFrameColor(R=0.996078431372549, G=0, B=0)


def test_color_extraction() -> None:
    image_bytes = (RESOURCE_DIR / "sample_frame.jpg").read_bytes()

    colors = color_detection.get_average_color_of_frame(image_bytes)
    assert colors == color_detection.AverageFrameColor(R=64 / 255, G=52 / 255, B=52 / 255)
