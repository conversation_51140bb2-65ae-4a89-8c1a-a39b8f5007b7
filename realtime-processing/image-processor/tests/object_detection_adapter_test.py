import json
from unittest.mock import Mock, patch

from image_processor.object_detection_adapter import <PERSON><PERSON><PERSON><PERSON>pter
from image_processor.object_detection_definitions import BoundingBox

HIGH_CONFIDENCE_OBJECT = {
    "name": "high_confidence_object",
    "confidence": 0.9,
    "xmin": 1.0,
    "ymin": 2.0,
    "xmax": 3.0,
    "ymax": 4.0,
}
LOW_CONFIDENCE_OBJECT = {
    "name": "low_confidence_object",
    "confidence": 0.7,
    "xmin": 1.0,
    "ymin": 2.0,
    "xmax": 3.0,
    "ymax": 4.0,
}


class TestYoloAdapter:
    @patch(
        "image_processor.object_detection_adapter.encode_multipart_formdata",
        return_value=(b"encoded_data", "multipart/form-data"),
    )
    def test_build_request_uses_encode_multipart_formdata(
        self, mock_encode_multipart_formdata: Mock
    ) -> None:
        adapter = YoloAdapter("yolo_svc_url")

        request = adapter.build_request(b"fake_image_data")

        mock_encode_multipart_formdata.assert_called_once_with(
            {"input_image": ("image.jpeg", b"fake_image_data", "image/jpeg")}
        )
        assert request.url == "yolo_svc_url/predict"
        assert request.headers == {"Content-Type": "multipart/form-data"}
        assert request.data == b"encoded_data"

    def test_parse_objects_returns_detected_objects_with_high_confidence(self) -> None:
        adapter = YoloAdapter("yolo_svc_url")
        prediction_objects = [
            LOW_CONFIDENCE_OBJECT,
            HIGH_CONFIDENCE_OBJECT,
        ]
        response = {
            "prediction_objects": prediction_objects,
            "predictions": json.dumps(prediction_objects),
            "model_version": "1.2.3",
        }

        detected_objects = adapter.parse_objects(response, 0.8)

        assert detected_objects.model_version == "1.2.3"
        assert detected_objects.bounding_boxes == [
            BoundingBox(
                object_name="high_confidence_object", confidence=0.9, x1=1.0, y1=2.0, x2=3.0, y2=4.0
            )
        ]
