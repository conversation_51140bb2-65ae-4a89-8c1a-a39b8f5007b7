import io
import random
from datetime import datetime
from typing import Any, Dict, cast
from unittest import mock

import pytest
from fastavro import schemaless_reader

from image_processor.avro_utils import COMBINED_AVRO_SCHEMA, serialize_combined_message_to_avro
from image_processor.object_detection_definitions import BoundingBox
from image_processor.pipeline import _get_object_stats_output_message, publish_combined_message


def make_sample_combined_message() -> dict[str, Any]:
    embedding = [random.uniform(-1, 1) for _ in range(512)]
    return {
        "guid": "83d75e5d-**************-7c3e9d4849d7",
        "color_model_output": {"average_r": 0.1, "average_g": 0.2, "average_b": 0.3},
        "frame_time": "2025-05-30T17:03:01.720071+00:00",
        "object_model_output": [
            {
                "label_display_name": "unscrubbed",
                "confidence": 0.95,
                "x1": 0.8,
                "x2": 0.9,
                "y1": 0.2,
                "y2": 0.7,
            },
            {
                "label_display_name": "scrubbed",
                "confidence": 0.93,
                "x1": 0.5,
                "x2": 0.7,
                "y1": 0.1,
                "y2": 0.6,
            },
        ],
        "org_id": "org1",
        "room_id": "room1",
        "camera_id": "NYU-LI4-OR08-CAM-01",
        "embedding": embedding,
        "embedding_model_name": "resnet34",
        "embedding_model_version": "v0.1",
        "object_model_version": "yolov5",
    }


def test_serialize_combined_message_to_avro_roundtrip() -> None:
    message = make_sample_combined_message()
    avro_bytes = serialize_combined_message_to_avro(message)
    buf = io.BytesIO(avro_bytes)
    record: Dict[str, Any] = cast(
        Dict[str, Any], schemaless_reader(buf, COMBINED_AVRO_SCHEMA, None)
    )
    assert record["guid"] == message["guid"]
    assert record["color_model_output"] == pytest.approx(message["color_model_output"])
    assert record["frame_time"] == message["frame_time"]
    # Compare object_model_output element-wise
    assert len(record["object_model_output"]) == len(message["object_model_output"])
    for rec_obj, msg_obj in zip(record["object_model_output"], message["object_model_output"]):
        assert rec_obj["label_display_name"] == msg_obj["label_display_name"]
        assert rec_obj["confidence"] == pytest.approx(msg_obj["confidence"])
        assert rec_obj["x1"] == pytest.approx(msg_obj["x1"])
        assert rec_obj["x2"] == pytest.approx(msg_obj["x2"])
        assert rec_obj["y1"] == pytest.approx(msg_obj["y1"])
        assert rec_obj["y2"] == pytest.approx(msg_obj["y2"])
    assert record["org_id"] == message["org_id"]
    assert record["room_id"] == message["room_id"]
    assert record["camera_id"] == message["camera_id"]
    assert record["embedding_model_name"] == message["embedding_model_name"]
    assert record["embedding_model_version"] == message["embedding_model_version"]
    assert record["object_model_version"] == message["object_model_version"]
    assert len(record["embedding"]) == 512
    assert record["embedding"] == pytest.approx(message["embedding"])


def test_publish_combined_message_avro_failure_logs_error(caplog: pytest.LogCaptureFixture) -> None:
    # Mock publisher_client to do nothing
    publisher_client = mock.Mock()
    combined_output_topic = "combined-topic"
    # Construct the base message using _get_object_stats_output_message
    average_color = mock.Mock(R=0.1, G=0.2, B=0.3)
    bounding_boxes: list[BoundingBox] = []
    object_stats_output_message = _get_object_stats_output_message(
        model_display_name="yolov5",
        frame_bucket_id="ok",
        frame_object_id="asdf",
        bounding_boxes=bounding_boxes,
        org_id="org1",
        room_id="room1",
        camera_id="cam1",
        frame_time=datetime(2025, 5, 30, 17, 3, 1, 720071),
        average_color=average_color,
    )

    # Patch the Avro serializer to raise an error
    with mock.patch(
        "image_processor.avro_utils.serialize_combined_message_to_avro",
        side_effect=RuntimeError("Avro serialization failed"),
    ):
        with caplog.at_level("ERROR"):
            try:
                publish_combined_message(
                    publisher_client=publisher_client,
                    combined_output_topic=combined_output_topic,
                    object_stats_output_message=object_stats_output_message,
                    resnet_image_embedding=mock.Mock(
                        embedding=["BAD DATA"] * 512,
                        model_name="resnet34",
                        model_version="v0.1",
                    ),
                )
            except Exception:
                pytest.fail("Exception should have been caught and not propagated")
        assert any("Failed to publish combined Avro message" in r.message for r in caplog.records)


def test_publish_combined_message_success_logs_no_error(caplog: pytest.LogCaptureFixture) -> None:
    publisher_client = mock.Mock()
    combined_output_topic = "combined-topic"
    average_color = mock.Mock(R=0.1, G=0.2, B=0.3)
    bounding_boxes: list[BoundingBox] = []
    object_stats_output_message = _get_object_stats_output_message(
        model_display_name="yolov5",
        frame_bucket_id="ok",
        frame_object_id="asdf",
        bounding_boxes=bounding_boxes,
        org_id="org1",
        room_id="room1",
        camera_id="cam1",
        frame_time=datetime(2025, 5, 30, 17, 3, 1, 720071),
        average_color=average_color,
    )

    # Patch the Avro serializer to just return bytes
    with mock.patch(
        "image_processor.avro_utils.serialize_combined_message_to_avro", return_value=b"avro-bytes"
    ):
        with caplog.at_level("ERROR"):
            publish_combined_message(
                publisher_client=publisher_client,
                combined_output_topic=combined_output_topic,
                object_stats_output_message=object_stats_output_message,
                resnet_image_embedding=mock.Mock(
                    embedding=[0.1] * 512,
                    model_name="resnet34",
                    model_version="v0.1",
                ),
            )
        # There should be no error logs
        assert not any(
            "Failed to publish combined Avro message" in r.message for r in caplog.records
        ), caplog.records
        # Assert publisher_client.publish was called with the correct arguments
        publisher_client.publish.assert_called_once()
