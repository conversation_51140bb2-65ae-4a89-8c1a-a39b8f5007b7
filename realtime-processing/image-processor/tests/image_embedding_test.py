from collections.abc import Iterator

import pytest
from pytest_httpserver import HTTPServer

from image_processor.image_embedder import <PERSON><PERSON>mbedder
from image_processor.request_helper import HTTPError


@pytest.fixture
def fake_embedder() -> Iterator[HTTPServer]:
    server = HTTPServer("127.0.0.1", 0)
    server.start()  # type: ignore[no-untyped-call]

    yield server

    server.stop()  # type: ignore[no-untyped-call]


def test_server_error(fake_embedder: HTTPServer) -> None:
    image_embedder = ImageEmbedder(fake_embedder.url_for("/"))

    fake_embedder.expect_request("/predict").respond_with_data(status=503)

    with pytest.raises(HTTPError, match="503 Server Error"):
        image_embedder.get_image_embedding(b"")


def test_successful_response(fake_embedder: HTTPServer) -> None:
    image_embedder = ImageEmbedder(fake_embedder.url_for("/"))

    expectation = fake_embedder.expect_request("/predict", "POST")
    expectation.respond_with_json(
        {"model_name": "resnet34", "model_version": "v0.1", "embedding": [0.02, 3.2, 1.0]}
    )

    result = image_embedder.get_image_embedding(b"fake image")

    assert result.model_version == "v0.1"
    assert result.embedding == [0.02, 3.2, 1.0]
    assert result.model_name == "resnet34"
