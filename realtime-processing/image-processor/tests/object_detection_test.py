import json
import time
from collections.abc import Iterator
from typing import Any

import pytest
import urllib3
from pytest_httpserver import HTTPServer
from werkzeug.wrappers.request import Request
from werkzeug.wrappers.response import Response

from image_processor.object_detection import ObjectDetector
from image_processor.object_detection_definitions import BoundingBox
from image_processor.request_helper import HTTPError

CONFIDENCE_THRESHOLD = 0.8
HIGH_CONFIDENCE = 0.9
LOW_CONFIDENCE = 0.7


def object_detection_svc_response_json(prediction_objects: list[dict[str, Any]]) -> dict[str, Any]:
    return {
        "prediction_objects": prediction_objects,
        "predictions": json.dumps(prediction_objects),
        "model_version": "1.2.3",
        "embedding": [0.1, 0.2, 0.3],
    }


@pytest.fixture
def mock_service(monkeypatch: pytest.MonkeyPatch) -> Iterator[HTTPServer]:
    server = HTTPServer("127.0.0.1", 0)
    server.start()  # type: ignore[no-untyped-call]

    yield server

    server.stop()  # type: ignore[no-untyped-call]


def test_server_error(
    mock_service: HTTPServer,
) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
    )
    mock_service.expect_request("/predict").respond_with_data(status=503)

    with pytest.raises(HTTPError, match="503 Server Error"):
        object_detector.object_detector_results(b"")


def test_server_timeout(
    mock_service: HTTPServer,
) -> None:
    request_timeout = 0.1
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
        request_timeout=request_timeout,
    )

    def cause_timeout(_request: Request) -> Response:
        time.sleep(request_timeout * 2)
        return Response()

    mock_service.expect_request("/predict", "POST").respond_with_handler(cause_timeout)

    with pytest.raises(urllib3.exceptions.ReadTimeoutError):
        object_detector.object_detector_results(b"")


def test_successful_response_at_threshold(
    mock_service: HTTPServer,
) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
    )
    mock_predictions = [
        {
            "name": "coffee_mug",
            "confidence": CONFIDENCE_THRESHOLD,
            "xmin": 1.0,
            "ymin": 2.0,
            "xmax": 3.0,
            "ymax": 4.0,
        },
    ]

    expectation = mock_service.expect_request("/predict", "POST")
    expectation.respond_with_json(object_detection_svc_response_json(mock_predictions))

    objects, embedding = object_detector.object_detector_results(b"fake image")

    assert objects.model_version == "1.2.3"
    assert objects.bounding_boxes == [
        BoundingBox("coffee_mug", CONFIDENCE_THRESHOLD, 1.0, 2.0, 3.0, 4.0)
    ]
    assert embedding.model_version == "1.2.3"
    assert embedding.embedding == [0.1, 0.2, 0.3]


def test_successful_response_above_threshold(
    mock_service: HTTPServer,
) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
    )
    mock_predictions = [
        {
            "name": "coffee_mug",
            "confidence": HIGH_CONFIDENCE,
            "xmin": 1.0,
            "ymin": 2.0,
            "xmax": 3.0,
            "ymax": 4.0,
        },
    ]

    expectation = mock_service.expect_request("/predict", "POST")
    expectation.respond_with_json(object_detection_svc_response_json(mock_predictions))

    objects, embedding = object_detector.object_detector_results(b"fake image")

    assert objects.model_version == "1.2.3"
    assert objects.bounding_boxes == [
        BoundingBox("coffee_mug", HIGH_CONFIDENCE, 1.0, 2.0, 3.0, 4.0)
    ]
    assert embedding.model_version == "1.2.3"
    assert embedding.embedding == [0.1, 0.2, 0.3]


def test_successful_response_below_threshold(mock_service: HTTPServer) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
    )

    mock_predictions = [
        {
            "name": "sasquatch",
            "confidence": LOW_CONFIDENCE,
            "xmin": 1.0,
            "ymin": 2.0,
            "xmax": 3.0,
            "ymax": 4.0,
        },
    ]

    expectation = mock_service.expect_request("/predict", "POST")
    expectation.respond_with_json(object_detection_svc_response_json(mock_predictions))

    objects, embedding = object_detector.object_detector_results(b"fake image")

    assert objects.model_version == "1.2.3"
    assert objects.bounding_boxes == []

    assert embedding.model_version == "1.2.3"
    assert embedding.embedding == [0.1, 0.2, 0.3]


def test_successful_response_mixed_thresholds(mock_service: HTTPServer) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),  # object detection svc url
        CONFIDENCE_THRESHOLD,
    )

    mock_predictions = [
        {
            "name": "coffee_mug",
            "confidence": HIGH_CONFIDENCE,
            "xmin": 1.0,
            "ymin": 2.0,
            "xmax": 3.0,
            "ymax": 4.0,
        },
        {
            "name": "sasquatch",
            "confidence": LOW_CONFIDENCE,
            "xmin": 1.0,
            "ymin": 2.0,
            "xmax": 3.0,
            "ymax": 4.0,
        },
    ]

    expectation = mock_service.expect_request("/predict", "POST")
    expectation.respond_with_json(object_detection_svc_response_json(mock_predictions))

    objects, embedding = object_detector.object_detector_results(b"fake image")

    assert objects.model_version == "1.2.3"
    assert objects.bounding_boxes == [
        BoundingBox("coffee_mug", HIGH_CONFIDENCE, 1.0, 2.0, 3.0, 4.0)
    ]
    assert embedding.model_version == "1.2.3"
    assert embedding.embedding == [0.1, 0.2, 0.3]


def test_connection_reuse(mock_service: HTTPServer) -> None:
    object_detector = ObjectDetector(
        mock_service.url_for("/"),
        CONFIDENCE_THRESHOLD,
    )
    expectation = mock_service.expect_request("/predict", "POST")
    expectation.respond_with_json(object_detection_svc_response_json([]))

    object_detector.object_detector_results(b"fake image")
    object_detector.object_detector_results(b"fake image")

    connection_pool = object_detector._pool_manager.connection_from_host(
        "127.0.0.1", mock_service.port
    )
    assert connection_pool.num_connections == 1
    assert connection_pool.num_requests == 2
