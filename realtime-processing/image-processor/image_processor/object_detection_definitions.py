from dataclasses import dataclass
from typing import Dict, Union


@dataclass(frozen=True)
class BoundingBox:
    """
    A class for storing a bounding box from the YOLOvX model
    """

    object_name: str
    confidence: float
    x1: float
    y1: float
    x2: float
    y2: float

    @staticmethod
    def from_json(json_data: Dict[str, Union[str, float]]) -> "BoundingBox":
        return BoundingBox(
            object_name=str(json_data["name"]),
            confidence=float(json_data["confidence"]),
            x1=float(json_data["xmin"]),
            y1=float(json_data["ymin"]),
            x2=float(json_data["xmax"]),
            y2=float(json_data["ymax"]),
        )


@dataclass(frozen=True)
class DetectedObjects:
    bounding_boxes: list[BoundingBox]
    model_version: str
