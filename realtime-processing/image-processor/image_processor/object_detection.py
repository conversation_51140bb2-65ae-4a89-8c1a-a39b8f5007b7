from image_processor.image_embedder import EmbeddingModel
from image_processor.object_detection_adapter import (
    YoloAdapter,
)
from image_processor.object_detection_definitions import DetectedObjects
from image_processor.request_helper import RequestHelper


class ObjectDetector(RequestHelper):
    def __init__(
        self,
        object_detection_deployment_url: str,
        confidence_threshold: float,
        max_connections_per_pool: int = 1,
        request_timeout: float = 30.0,
    ):
        super().__init__(max_connections_per_pool, request_timeout)
        self.confidence_threshold = confidence_threshold
        self.yolo_adapter = YoloAdapter(object_detection_deployment_url)

    def object_detector_results(self, image: bytes) -> tuple[DetectedObjects, EmbeddingModel]:
        """
        Send a request to the object detection service and return the detected objects and the embedding

        Args:
            image: bytes: The image to be processed
        Returns:
            tuple[DetectedObjects, EmbeddingModel]: The detected objects and the embedding
        """
        request = self.yolo_adapter.build_request(image)
        responseJson = self._post_request(
            url=request.url, headers=request.headers, body=request.data
        )
        return self.yolo_adapter.parse_objects(
            responseJson, self.confidence_threshold
        ), EmbeddingModel(
            embedding=responseJson["embedding"],
            model_name=responseJson["model_version"],
            model_version=responseJson["model_version"],
        )
