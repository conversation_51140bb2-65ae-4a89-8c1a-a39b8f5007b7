from dataclasses import dataclass
from typing import List

import urllib3

from image_processor.request_helper import RequestHelper


# TODO: Create a DTO package instead of copying EmbeddingModel here.
@dataclass
class EmbeddingModel:
    embedding: List[float]
    model_name: str
    model_version: str


class ImageEmbedder(RequestHelper):
    def __init__(
        self,
        deployment_url: str,
        max_connections_per_pool: int = 1,
        request_timeout: float = 30.0,
    ):
        super().__init__(max_connections_per_pool, request_timeout)
        self.deployment_url = deployment_url

    def get_image_embedding(self, image_data: bytes) -> EmbeddingModel:
        url = f"{self.deployment_url}/predict"
        fields = {
            "image": ("image.jpeg", image_data, "image/jpeg"),
        }

        body, content_type_header = urllib3.encode_multipart_formdata(fields)
        headers = {"Content-Type": content_type_header}
        response = self._post_request(url, headers, body)
        return EmbeddingModel(
            response["embedding"], response["model_name"], response["model_version"]
        )
