import faulthandler
import logging
import signal
from argparse import Argument<PERSON>ars<PERSON>

import prometheus_client
from google.cloud.bigtable import Client as BigTableClient
from google.cloud.pubsub import PublisherClient, SubscriberClient
from google.cloud.pubsub_v1.types import PublisherOptions
from google.cloud.storage import Client as GCSClient
from lib_python_logging.apella_logger import ApellaLogger

from image_processor.pipeline import start_pipeline

# (TODO @negatu) add unit tests for cli.py


def main() -> None:
    # Allow an operator to dump the current Python traceback by sending SIGUSR1. This is useful for
    # debugging deadlocks.  The command to send the process SIGUSR1 is `kill -USR1 <pid>`. If
    # running in k8s, you'll have to exec into the container to do so.
    faulthandler.register(signal.SIGUSR1)

    parser = ArgumentParser()
    parser.add_argument("--subscription", required=True)

    parser.add_argument("--bentoml-yolov5-deployment", required=True)
    parser.add_argument("--object-detection-deployment", required=False)
    parser.add_argument(
        "--image-embedding-deployment",
        required=True,
    )
    parser.add_argument("--feature-store-project", required=True)
    parser.add_argument("--feature-store-instance", required=True)
    parser.add_argument("--image-embedding-output-topic", required=True)
    parser.add_argument("--object-stats-output-topic", required=True)
    parser.add_argument("--prometheus-port", required=True, type=int)
    parser.add_argument("--calc-image-embeddings", default=False, type=bool)
    parser.add_argument("--debug", action="store_true")
    parser.add_argument(
        "--combined-output-topic",
        required=False,
        default=None,
        help="PubSub topic for combined output messages (optional)",
    )
    args = parser.parse_args()

    log_level = logging.DEBUG if args.debug else logging.INFO
    ApellaLogger().setup_logging(log_level=log_level, use_json_logging=True)

    if args.bentoml_yolov5_deployment:
        logging.warning(
            "--bentoml-yolov5-deployment is deprecated. Please use --object-detection-deployment instead."
        )

    args.object_detection_deployment = (
        args.object_detection_deployment or args.bentoml_yolov5_deployment
    )

    if args.calc_image_embeddings:
        logging.info("Calculating image embeddings is turned on.")

    prometheus_client.start_http_server(args.prometheus_port)

    gcs_client = GCSClient()
    bigtable_client = BigTableClient(project=args.feature_store_project)
    with PublisherClient(
        publisher_options=PublisherOptions(enable_message_ordering=False)
    ) as publisher_client, SubscriberClient() as subscriber_client:
        start_pipeline(
            subscriber_client,
            args.subscription,
            gcs_client,
            bigtable_client,
            args.object_detection_deployment,
            args.image_embedding_deployment,
            args.feature_store_instance,
            publisher_client,
            args.object_stats_output_topic,
            args.image_embedding_output_topic,
            args.combined_output_topic,
            args.calc_image_embeddings,
        ).result()
