import io
from typing import Any

from fastavro import parse_schema, schemaless_writer

# Default Avro schema for the combined message
COMBINED_AVRO_SCHEMA = {
    "type": "record",
    "name": "CombinedMessage",
    "fields": [
        {"name": "guid", "type": "string"},
        {
            "name": "color_model_output",
            "type": {
                "type": "record",
                "name": "ColorModelOutput",
                "fields": [
                    {"name": "average_r", "type": "float"},
                    {"name": "average_g", "type": "float"},
                    {"name": "average_b", "type": "float"},
                ],
            },
        },
        {"name": "frame_time", "type": "string"},
        {
            "name": "object_model_output",
            "type": {
                "type": "array",
                "items": {
                    "type": "record",
                    "name": "ObjectModelOutput",
                    "fields": [
                        {"name": "label_display_name", "type": "string"},
                        {"name": "confidence", "type": "float"},
                        {"name": "x1", "type": "float"},
                        {"name": "x2", "type": "float"},
                        {"name": "y1", "type": "float"},
                        {"name": "y2", "type": "float"},
                    ],
                },
            },
        },
        {"name": "org_id", "type": "string"},
        {"name": "room_id", "type": "string"},
        {"name": "camera_id", "type": "string"},
        {"name": "embedding", "type": {"type": "array", "items": "float"}},
        {"name": "embedding_model_name", "type": "string"},
        {"name": "embedding_model_version", "type": "string"},
        {"name": "object_model_version", "type": "string"},
    ],
}


def serialize_combined_message_to_avro(
    combined_message: dict[str, Any], schema: dict[str, Any] = COMBINED_AVRO_SCHEMA
) -> bytes:
    """
    Serializes the combined message dict to Avro bytes using the provided schema.
    """
    buf = io.BytesIO()
    schemaless_writer(buf, parse_schema(schema), combined_message)
    return buf.getvalue()
