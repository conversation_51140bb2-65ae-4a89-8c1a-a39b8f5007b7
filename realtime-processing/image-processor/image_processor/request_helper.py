from dataclasses import dataclass, field
from types import TracebackType
from typing import Any, Literal

import urllib3
from typing_extensions import Self


class HTTPError(RuntimeError):
    pass


@dataclass
class RequestHelper:
    max_connections_per_pool: int = 1
    request_timeout: float = 30.0

    _pool_manager: urllib3.PoolManager = field(init=False)

    def __post_init__(self) -> None:
        timeout = urllib3.util.Timeout(total=self.request_timeout)
        self._pool_manager = urllib3.PoolManager(
            maxsize=self.max_connections_per_pool, timeout=timeout
        )

    def __enter__(self: Self) -> Self:
        return self

    def __exit__(
        self,
        exc_Type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> Literal[False]:
        self._pool_manager.clear()
        return False

    def _post_request(self, url: str, headers: dict[str, str], body: str | bytes) -> Any:
        response = self._pool_manager.request("POST", url, headers=headers, body=body)
        if response.status >= 400 and response.status < 500:
            raise HTTPError(f"{response.status} Client Error: {response.reason} for {url}")
        elif response.status >= 500 and response.status < 600:
            raise HTTPError(f"{response.status} Server Error: {response.reason} for {url}")
        return response.json()
