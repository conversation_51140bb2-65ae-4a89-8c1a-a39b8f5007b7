from dataclasses import dataclass

import cv2
import numpy as np


@dataclass(frozen=True)
class AverageFrameColor:
    R: float
    G: float
    B: float


def get_average_color_of_frame(image_bytes: bytes) -> AverageFrameColor:
    # per https://docs.opencv.org/3.4/d4/da8/group__imgcodecs.html,
    # decoded images are in BGR order
    frame = cv2.imdecode(np.frombuffer(image_bytes, dtype=np.uint8), cv2.IMREAD_COLOR)

    single_pixel = cv2.resize(src=frame, dsize=(1, 1), interpolation=cv2.INTER_AREA)

    average_color = single_pixel[0][0]
    return AverageFrameColor(
        R=float(average_color[2]) / 255,
        G=float(average_color[1]) / 255,
        B=float(average_color[0]) / 255,
    )
