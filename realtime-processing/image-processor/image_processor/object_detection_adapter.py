from abc import ABC, abstractmethod
from typing import Any

from requests.models import Request
from urllib3 import encode_multipart_formdata

from image_processor.object_detection_definitions import (
    BoundingBox,
    DetectedObjects,
)


class ObjectDetectionAdapter(ABC):
    @abstractmethod
    def build_request(self, image: bytes) -> Request:
        pass

    @abstractmethod
    def parse_objects(
        self, response: dict[str, Any], confidence_threshold: float
    ) -> DetectedObjects:
        pass


class YoloAdapter(ObjectDetectionAdapter):
    def __init__(self, deployment_url: str) -> None:
        self.predict_endpoint = f"{deployment_url}/predict"

    def build_request(self, image: bytes) -> Request:
        fields = {
            "input_image": (
                "image.jpeg",
                image,
                "image/jpeg",
            ),
        }
        encoded_data, content_type = encode_multipart_formdata(fields)
        headers = {"Content-Type": content_type}
        return Request(url=self.predict_endpoint, headers=headers, data=encoded_data)

    def parse_objects(
        self, response: dict[str, Any], confidence_threshold: float
    ) -> DetectedObjects:
        predictions = response["prediction_objects"]
        objects = [
            BoundingBox.from_json(prediction)
            for prediction in predictions
            if prediction["confidence"] >= confidence_threshold
        ]
        return DetectedObjects(objects, response["model_version"])
