#
# BUILDER CONTAINER IMAGE
#
# The purpose of this container is to produce a standalone virtualenv directory that can be copied
# into the production environment in the second stage. The tools installed into this container to
# produce the virtualenv are not copied over into production, which keeps the production container
# as small as possible and isolates it from potential vulnerabilities in extraneous tools that
# aren't needed to run the service.
#
FROM python:3.10 as builder

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 - --version 1.7.1
ENV PATH="/root/.local/bin:$PATH"
RUN poetry self add keyrings.google-artifactregistry-auth

WORKDIR /build

# Copy in the bare minimum to convince Poetry to install the dependencies
COPY realtime-processing/feature-processor/pyproject.toml ./
COPY realtime-processing/feature-processor/poetry.lock ./
RUN touch README.md

# Copy in relative dependencies in other projects
COPY feature_store ../../feature_store

# Install dependencies into /build/.venv
ENV POETRY_VIRTUALENVS_IN_PROJECT=true
RUN mkdir -p $HOME/.config/gcloud
RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/root/.config/gcloud/application_default_credentials.json \
    poetry install --only main --no-root

# Copy the production source code in and install this package.
# It's surprising that we're using `pip` here, but as of Poetry 1.7.1, it's the only way to get the
# root package copied into the virtualenv as opposed to installed via a .pth file which points back
# to the source directory. If we don't do this, then the COPY in the runtime container will fail
# because the files pointed to by the .pth file don't exist.
COPY realtime-processing/feature-processor/feature_processor ./feature_processor
RUN poetry build && poetry run pip install dist/*.whl

#
# RUNTIME CONTAINER IMAGE
#
# The purpose of this container is to run the actual production service. All it needs to do is to
# copy over the virtualenv produced in the build container image and set the PATH to include it.
#
# For good measure, it also installs dumb-init, which is useful for ensuring that signals sent to
# the service are handled correctly.
#
FROM python:3.10-slim as runtime

RUN apt update && apt install -y dumb-init && rm -rf /var/lib/apt/lists/*

COPY --from=builder /build/.venv /build/.venv
ENV PATH="/build/.venv/bin:$PATH"

ENV PYTHONUNBUFFERED=1
ENTRYPOINT [ "dumb-init" ]
