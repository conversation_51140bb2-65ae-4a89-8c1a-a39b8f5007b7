[tool.poetry]
name = "feature-processor"
version = "0.1.0"
description = "A lightweight processor to pass data from decodable to the bigtable feature store."
authors = [
    "Apella Engineering <<EMAIL>>"
]
readme = "README.md"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"

[[tool.mypy.overrides]]
module = [
    "google.cloud.pubsub_v1.*",
    "google.cloud.bigtable.*",
]
ignore_missing_imports = true

[tool.poetry.dependencies]
python = "~3.10"
protobuf="^5.29.4"
google-cloud="^0.34.0"
google-cloud-bigtable="^2.30.0"
google-cloud-pubsub="^2.29.0"
feature-store = { path="../../feature_store", develop = true }
pandas-stubs = "^2.2.3.250308"
prometheus-client = "^0.21.1"
python-dateutil = "^2.8.2"
types-python-dateutil = "^2.9.0.20241206"
lib-python-logging = "^1.0.3"

[tool.poetry.scripts]
feature_processor = 'feature_processor.cli:main'
upload_single_message = 'feature_processor.upload_single_message:main'


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
ruff = "^0.11.4"
mypy = "^1.15.0"
docker = "^7.1.0"

