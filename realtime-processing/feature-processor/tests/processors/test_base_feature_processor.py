import json
from unittest.mock import MagicMock, patch

from feature_processor.processors.base_feature_processor import (
    BaseFeatureProcessor,
    MessageAction,
)


class NaiveFeatureProcessor(BaseFeatureProcessor):
    """A simple feature processor for testing the base class functionality."""

    def __init__(
        self,
        should_fail: bool = False,
        should_assert_fail: bool = False,
        assertion_action: MessageAction = MessageAction.NACK,
    ):
        self.should_fail = should_fail
        self.should_assert_fail = should_assert_fail
        self.assertion_action = assertion_action

    def process_message(self, message_data: str) -> None:
        """Process a message, potentially raising an exception based on configuration."""
        if self.should_assert_fail:
            raise AssertionError("Test assertion error")
        if self.should_fail:
            raise Exception("Test error")
        # For successful processing, just parse the JSON to ensure it's valid
        json.loads(message_data)

    def handle_assertion_exception(self, exc: Exception) -> MessageAction:
        return self.assertion_action


class TestBaseFeatureProcessor:
    def test_callback_success(self):
        """Test that callback acks messages on successful processing and calls handle_success."""
        message = MagicMock(data=json.dumps({"test": "data"}).encode("utf-8"))
        processor = NaiveFeatureProcessor()

        with patch.object(processor, "handle_success") as mock_handle_success:
            processor.callback(message)

            message.ack.assert_called_once()
            message.nack.assert_not_called()
            mock_handle_success.assert_called_once_with(message)

    def test_callback_general_exception(self):
        """Test that callback nacks messages on general exceptions and calls handle_exception."""
        message = MagicMock(data=json.dumps({"test": "data"}).encode("utf-8"))
        processor = NaiveFeatureProcessor(should_fail=True)

        with patch.object(processor, "handle_exception") as mock_handle_exception:
            processor.callback(message)

            message.nack.assert_called_once()
            message.ack.assert_not_called()
            mock_handle_exception.assert_called_once()

    def test_callback_assertion_error_with_nack(self):
        """Test that callback nacks messages on assertion errors when configured to NACK."""
        message = MagicMock(data=json.dumps({"test": "data"}).encode("utf-8"))
        processor = NaiveFeatureProcessor(
            should_assert_fail=True, assertion_action=MessageAction.NACK
        )

        processor.callback(message)

        message.nack.assert_called_once()
        message.ack.assert_not_called()

    def test_callback_assertion_error_with_ack(self):
        """Test that callback acks messages on assertion errors when configured to ACK."""
        message = MagicMock(data=json.dumps({"test": "data"}).encode("utf-8"))
        processor = NaiveFeatureProcessor(
            should_assert_fail=True, assertion_action=MessageAction.ACK
        )

        processor.callback(message)

        message.ack.assert_called_once()
        message.nack.assert_not_called()

    def test_callback_invalid_json(self):
        """Test that callback nacks messages with invalid JSON."""
        message = MagicMock(data=b"invalid json")
        processor = NaiveFeatureProcessor()

        with patch.object(processor, "handle_exception") as mock_handle_exception:
            processor.callback(message)

            message.nack.assert_called_once()
            message.ack.assert_not_called()
            mock_handle_exception.assert_called_once()

    def test_callback_empty_message(self):
        """Test that callback nacks empty messages."""
        message = MagicMock(data=b"")
        processor = NaiveFeatureProcessor()

        with patch.object(processor, "handle_exception") as mock_handle_exception:
            processor.callback(message)

            message.nack.assert_called_once()
            message.ack.assert_not_called()
            mock_handle_exception.assert_called_once()
