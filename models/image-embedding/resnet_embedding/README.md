# ResNet Image Embedding Service

A FastAPI service that generates embeddings for images using a ResNet model.

## Installation

```bash
poetry install
```

## Running the Service

To run the service locally:

1. Using the make command:
```bash
make run-local
```

The service will start on `http://localhost:3000`.

## Testing the Service

### Using curl

From the `ml-services` directory, you can test the service using curl:

```bash
curl -X 'POST' \
    'http://localhost:3000/predict' \
    -H 'accept: application/json' \
    -H 'Content-Type: multipart/form-data' \
    -F 'image=@models/image-embedding/resnet_embedding/resnet_embedding/test-image.jpg;type=image/jpeg'
```

## Model Management

### Model Storage

The service downloads the model from our Google Cloud Storage bucket instead of pulling weights from the PyTorch repository. This approach is preferred because PyTorch's model hub has rate limiting that can affect our service's reliability.

### Updating the Model

To generate and save a new model:
1. Use the `model_download_util.py` utility to generate the model
2. Upload the generated model to the Google Cloud Storage bucket

## Testing the Production Build

### Building the Docker Image

Production builds can be found in the [Google Cloud Artifact Registry](https://console.cloud.google.com/artifacts/docker/prod-platform-29b5cb/us-central1/prod-docker-registry/ml-services%2Fmodels%2Fimage-embedding%2Fresnet_embedding%2Fbentoml?hl=en&invt=AbtGDA&project=prod-platform-29b5cb) (tag: `latest-build`).

### Running the Container

1. Authenticate GCloud Docker
```bash
gcloud auth configure-docker us-central1-docker.pkg.dev
```

2. Authenticate with Google Cloud:
```bash
gcloud auth login --update-adc
```

3. Pull the Docker image (replace `<tag>` with your build's SHA):
```bash
docker pull \
    us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/image-embedding/resnet_embedding/bentoml:<tag> --platform amd64
```

4. Run the container:
```bash
docker run \
  -p 3000:3000 \
  -e DEV_STUB=1 \
  -e GOOGLE_APPLICATION_CREDENTIALS="/home/<USER>/.config/gcloud/application_default_credentials.json" \
  -e PROJECT_DIR="models/image-embedding/resnet_embedding" \
  -e PROJECT_NAME="resnet_embedding" \
  --mount type=bind,source=/Users/<USER>/.config/gcloud,target=/home/<USER>/.config/gcloud \
  us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/image-embedding/resnet_embedding/bentoml:<tag>
```

5. Access the service:
   - Open `http://localhost:3000/docs` in your browser to view the API documentation
   - Use the curl command above to test the prediction endpoint
