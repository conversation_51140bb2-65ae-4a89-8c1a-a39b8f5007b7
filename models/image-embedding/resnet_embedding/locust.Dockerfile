#
# BUILDER CONTAINER IMAGE
#
# The purpose of this container is to produce a standalone virtualenv directory that can be copied
# into the production environment in the second stage. The tools installed into this container to
# produce the virtualenv are not copied over into production, which keeps the production container
# as small as possible and isolates it from potential vulnerabilities in extraneous tools that
# aren't needed to run the service.
#
FROM python:3.10-slim

RUN apt-get update && \
    apt-get -y upgrade && \
    apt-get install -y --no-install-recommends \
    curl build-essential wget zlib1g-dev libssl-dev libffi-dev libbz2-dev libsqlite3-dev libreadline-dev liblzma-dev && \
    # Clean up cache and temporary files
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Version 1.3.1 has the fix for https://security-tracker.debian.org/tracker/CVE-2023-45853
ENV ZLIB_VERSION=1.3.1

# Download and extract zlib source since debian bullseye still uses 1.2.13
RUN wget http://zlib.net/zlib-$ZLIB_VERSION.tar.gz && \
    tar -xzf zlib-$ZLIB_VERSION.tar.gz && \
    rm zlib-$ZLIB_VERSION.tar.gz && \
    cd zlib-$ZLIB_VERSION && \
    ./configure --prefix=/usr --libdir=/usr/lib/aarch64-linux-gnu && \
    make && \
    make install && \
    ldconfig && \
    rm -rf /zlib-$ZLIB_VERSION

# Download and build Python from source so it picks up the updated zlib
ENV PYTHON_VERSION=3.10.15
RUN wget https://www.python.org/ftp/python/$PYTHON_VERSION/Python-$PYTHON_VERSION.tgz && \
    tar -xzf Python-$PYTHON_VERSION.tgz && \
    rm Python-$PYTHON_VERSION.tgz && \
    cd Python-$PYTHON_VERSION && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    rm -rf /Python-$PYTHON_VERSION

# Set the newly compiled Python version as default
RUN ln -sf /usr/local/bin/python3.10 /usr/local/bin/python3 && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip3

# Clean up to reduce image size
WORKDIR /
RUN apt-get purge -y build-essential wget && \
    apt-get autoremove -y && \
    rm -rf /zlib-$ZLIB_VERSION && \
    rm -rf /var/lib/apt/lists/*

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 - --version 1.7.1
ENV PATH="/root/.local/bin:$PATH"
RUN poetry self add keyrings.google-artifactregistry-auth

# Upgrade Setuptools in Poetry's virtualenv to satisfy Vanta
RUN /root/.local/share/pypoetry/venv/bin/pip install --upgrade setuptools

RUN python -m pip install --upgrade pip

WORKDIR /build

# Copy in the bare minimum to convince Poetry to install the dependencies
COPY pyproject.toml poetry.lock ./
RUN touch README.md

# Do not create a virtualenv since docker containers are already isolated
RUN poetry config virtualenvs.create false

RUN mkdir -p $HOME/.config/gcloud
RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/root/.config/gcloud/application_default_credentials.json \
    poetry install --only locust --no-root

    
COPY tests/locust resnet_embedding/locust
COPY resnet_embedding/test-image.jpg resnet_embedding/test-image.jpg

ENV PYTHONUNBUFFERED=1
ENTRYPOINT ["poetry", "run", "locust"]