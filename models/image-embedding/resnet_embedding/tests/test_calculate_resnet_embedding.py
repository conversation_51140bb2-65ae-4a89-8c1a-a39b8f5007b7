import pytest
from torch import no_grad

from resnet_embedding.calculate_resnet_embedding import (
    CalculateResnetEmbedding,
    choose_device,
)
import torch
from torchvision import models
from unittest.mock import patch


@pytest.mark.parametrize(
    "model_supports_mps, expected",
    [
        (True, torch.device("mps")),
        (
            False,
            torch.device("cuda:0")
            if torch.cuda.is_available()
            else torch.device("cpu"),
        ),
    ],
)
def test_choose_device(model_supports_mps, expected):
    with patch("torch.backends.mps.is_available", return_value=True), patch(
        "torch.backends.mps.is_built", return_value=True
    ):
        assert choose_device(model_supports_mps) == expected


def test_choose_device_no_mps_and_no_cuda():
    with patch("torch.backends.mps.is_available", return_value=False), patch(
        "torch.backends.mps.is_built", return_value=False
    ), patch("torch.cuda.is_available", return_value=False):
        assert choose_device(True) == torch.device("cpu")


def test_choose_device_no_mps_but_cuda():
    with patch("torch.backends.mps.is_available", return_value=False), patch(
        "torch.backends.mps.is_built", return_value=False
    ), patch("torch.cuda.is_available", return_value=True):
        assert choose_device(True) == torch.device("cuda:0")


class TestCalulateResnetEmbedding:
    @pytest.fixture(autouse=True)
    def setup_class(self):
        weights = models.ResNet34_Weights.IMAGENET1K_V1
        renset_model = models.resnet34(weights=weights)
        self.model_obj = CalculateResnetEmbedding(renset_model)

    def test_init_model(self):
        # Test if init_model initializes the model correctly
        self.model_obj._init_model()

        # Check if model is an instance of torch.nn.Sequential
        assert isinstance(self.model_obj.model, torch.nn.Sequential), "Model type error"

        # Check if device is either 'cuda', 'cpu' or 'mps'
        valid_devices = ["cuda", "cpu", "mps"]
        assert str(self.model_obj.device) in valid_devices, "Invalid device type"

        # Check if the model is in evaluation mode
        assert (
            self.model_obj.model.training is False
        ), "Model should be in evaluation mode"

        # Check if model has the correct number of layers(self.model.children() should be less by 1
        # after removing the final layer)
        model_layers = list(self.model_obj.model.children())
        assert (
            len(model_layers) == 9
        ), "Model layers mismatch. Check final layer removal"

        # Check if the weights of the model are loaded correctly by inferencing with a dummy tensor
        with no_grad():
            dummy_input = torch.ones((1, 3, 224, 224), device=self.model_obj.device)
            output = self.model_obj.model(dummy_input)
            assert output.shape == (1, 512, 1, 1), "Model weights loading error"

    def test_init_image_transforms(self):
        assert (
            self.model_obj.image_transforms is not None
        ), "Image Transforms are not initialized"

    def test_compute_embedding(self):
        image_file = torch.rand(3, 256, 256)  # Create a random image tensor
        output = self.model_obj.compute_embedding(image_file)
        assert output.model_name == "resnet34"
        assert output.model_version == "v0.1"
        assert output.embedding is not None, "Embedding"

    def test_compute_embedding_for_test_image(self):
        """
        Test the `compute_embedding` method for a test image.

        we found that on different machines we can get very different embeddings. To reduce the
        chances of unexpected behavior going undetected, we can use a test image to compute the
        embedding vs the expected embedding.

        :return: None
        """
        output = self.model_obj.test_embedding()
        assert output.model_name == "resnet34"
        assert output.model_version == "v0.1"
