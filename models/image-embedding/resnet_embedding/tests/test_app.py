import pytest
from app.endpoints.predict.predict_api import ResnetEmbeddingService
from PIL import Image
from unittest.mock import patch, MagicMock
from fastapi import UploadFile
from io import BytesIO


class TestResnetEmbedding:
    @pytest.fixture
    def setup_embedding(self):
        with patch(
            "app.endpoints.predict.predict_api.CalculateResnetEmbedding"
        ) as mock_calculate_resnet_embedding, patch(
            "app.endpoints.predict.predict_api.init_resnet_model"
        ) as mock_init_resnet_model, patch(
            "app.endpoints.predict.predict_api.download_model_weights"
        ) as mock_download_model_weights:
            # Mock the model to return a simple MagicMock
            mock_model = MagicMock()
            mock_init_resnet_model.return_value = mock_model

            embedding_instance = ResnetEmbeddingService()
            mock_calculate_resnet_embedding.assert_called_once_with(mock_model)
            mock_download_model_weights.assert_called_once()
        return embedding_instance, mock_calculate_resnet_embedding

    def test_get_embeddings_for_image(self, setup_embedding):
        embedding_instance, mock_calculate_resnet_embedding = setup_embedding
        test_image = Image.new("RGB", (60, 30), color="red")

        with patch("torchvision.transforms.PILToTensor") as mock_transform:
            # Mock the transform to return a tensor
            mock_tensor = MagicMock()
            mock_transform.return_value.return_value = mock_tensor

            embedding_instance.compute_embedding(test_image)

            # Verify the transform was called with the test image
            mock_transform.return_value.assert_called_once_with(test_image)

            # Verify the embedding computation was called with the transformed tensor
            mock_calculate_resnet_embedding.return_value.compute_embedding.assert_called_once_with(
                mock_tensor
            )

    @pytest.mark.asyncio
    async def test_predict_async(self, setup_embedding):
        embedding_instance, mock_calculate_resnet_embedding = setup_embedding
        test_image = Image.new("RGB", (60, 30), color="red")

        # Create a mock UploadFile with proper file attribute
        mock_file = MagicMock()
        mock_file.read.return_value = b"fake_image_data"
        mock_upload_file = MagicMock(spec=UploadFile)
        mock_upload_file.file = mock_file

        # Mock the executor to return our test image
        with patch("concurrent.futures.ThreadPoolExecutor") as mock_executor, patch(
            "app.endpoints.predict.predict_api.Image.open"
        ) as mock_image_open, patch(
            "torchvision.transforms.PILToTensor"
        ) as mock_transform:
            # Set up the mocks
            mock_image_open.return_value = test_image
            mock_tensor = MagicMock()
            mock_transform.return_value.return_value = mock_tensor
            mock_embedding_result = MagicMock()
            mock_calculate_resnet_embedding.return_value.compute_embedding.return_value = mock_embedding_result

            # Mock the executor's run_in_executor behavior
            mock_executor.return_value.__enter__.return_value.submit.side_effect = [
                MagicMock(result=lambda: test_image),  # For get_image
                MagicMock(
                    result=lambda: mock_embedding_result
                ),  # For compute_embedding
            ]

            # Call the predict method
            result = await embedding_instance.predict(mock_upload_file)

            # Verify the image was loaded correctly
            mock_file.read.assert_called_once()
            # Verify Image.open was called with a BytesIO containing our fake data
            mock_image_open.assert_called_once()
            call_args = mock_image_open.call_args[0][0]
            assert isinstance(call_args, BytesIO)
            assert call_args.getvalue() == b"fake_image_data"

            # Verify the transform was called with the test image
            mock_transform.return_value.assert_called_once_with(test_image)

            # Verify the embedding computation was called with the transformed tensor
            mock_calculate_resnet_embedding.return_value.compute_embedding.assert_called_once_with(
                mock_tensor
            )

            # Verify the result
            assert result == mock_embedding_result
