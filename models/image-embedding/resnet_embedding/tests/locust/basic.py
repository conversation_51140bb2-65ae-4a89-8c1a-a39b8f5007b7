import logging
import io
from PIL import Image
from locust import HttpUser, task, events, constant

logger = logging.getLogger(__name__)


@events.init_command_line_parser.add_listener
def _(parser):
    parser.add_argument(
        "--test-image-path", type=str, default="resnet_embedding/test-image.jpg"
    )


class ResnetEmbedderUser(HttpUser):
    """
    Basic test Resnet Embedder User user http client.
    """

    wait_time = constant(0.1)

    def on_start(self):
        buffer = io.BytesIO()
        image = Image.open(self.environment.parsed_options.test_image_path)
        image.save(buffer, format="JPEG")
        self.image_data = buffer.getvalue()

    @task
    def get_image_embedding(self) -> None:
        fields = {
            "image": ("image.jpeg", self.image_data, "image/jpeg"),
        }
        result = self.client.post("/predict", files=fields).json()
        assert result["model_name"] is not None
