# Load Testing

You can run load tests with [locust](https://docs.locust.io/en/stable/what-is-locust.html).


## Local load testing

You can load test dev or a local server by running locust locally.

Accessing the dev Resnet Embedding Service from you own machine requires
using kubectl for setting up a tunnel. To do so, make sure you have kubectl installed
(`gcloud components install kubectl`) and then run:

```
$ gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f
$ kubectl -n resnet-embedding port-forward service/resnet-embedding 9999:80
```

Then you can execute the basic test using locust

```
poetry run locust -f tests/locust/basic.py -H http://localhost:9999 --headless -u 10 -t 3m
```

The test can also be run from docker. Set up the tunnel as before and then run:
```
$ make locust-test-run-local
```

## Load testing from inside the k8s cluster

Docker images are built automatically when pull requests are created or updated, and these images are then deployed using ArgoCD. To run your test on the internal cluster, create a PR, and then run:

`$ kubectl port-forward svc/resnet-embedding-locust-tests -n resnet-embedding-locust-tests 8089:8089`

and then access the Locust UI from http://0.0.0.0:8089/.