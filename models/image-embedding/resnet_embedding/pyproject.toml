[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
explicit_package_bases = true

[[tool.mypy.overrides]]
module = [
    "torchvision.*",
    "google.cloud.*",
]
ignore_missing_imports = true

[tool.poetry]
name = "resnet_embedding"
version = "0.1.0"
description = "An example resnet-34 image embedding service."
authors = ["Apella Engineering <<EMAIL>>", "Data Science & Machine Learning <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
fastapi = {extras = ["standard"], version = "^0.115.0"}
prometheus-fastapi-instrumentator = "^7.0.0"
torch = "^1.12"
torchvision = "~0.14.1"
transformers = "^4.39.3"
pydantic = "^2.7.0"
numpy = "~1.24.4"
serving_utils = {path = "../../../serving_utils", develop = true }
google-cloud-storage = "^2.18.2"

[tool.pytest.ini_options]
asyncio_mode = "auto"

[tool.poetry.group.locust]
optional = true

[tool.poetry.group.locust.dependencies]
python = "~3.10"
locust = "^2.29.0"
pillow = "^10.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.1.1"
pytest-asyncio = "^0.24.0"
ruff = "^0.4.1"
pytest-cov = "^5.0.0"
mypy = "^1.9.0"
locust = "^2.29.0"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
