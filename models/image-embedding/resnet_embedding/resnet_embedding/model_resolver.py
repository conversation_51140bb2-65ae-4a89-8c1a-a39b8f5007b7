import os
from pathlib import Path

from google.cloud import storage
from serving_utils.config import get_dev_stub
import torch
from torchvision import models
from logging import Logger

# version info
MODEL_BLOB = "resnet_embedding/resnet34_IMAGENET1K_V1.pt"
PROD_BUCKET_NAME = "prod-inference-models"
DEV_BUCKET_NAME = "dev-inference-models"

LOCAL_MODEL_DIR = "/tmp/downloaded_models"


def generate_model_local_path() -> Path:
    local_model_file = Path(os.path.join(LOCAL_MODEL_DIR, "model.pt"))
    return local_model_file


def download_model_weights(logger: Logger) -> None:
    # Downloads model weights
    bucket_name = DEV_BUCKET_NAME if get_dev_stub() == "1" else PROD_BUCKET_NAME

    local_model_file = generate_model_local_path()
    logger.info(
        f"Downloading stored model from GCS ({MODEL_BLOB}) to {local_model_file}"
    )
    download_file_from_gcs(bucket_name, MODEL_BLOB, local_model_file)


def init_resnet_model(logger: Logger) -> models.ResNet:
    # Inits a resnet model based on the weights. Note that we later remove the final layer to get embeddings.
    local_model_file = generate_model_local_path()
    logger.info(f"Loading model from {local_model_file}")

    model = models.resnet34()
    model.load_state_dict(torch.load(local_model_file, weights_only=True))
    return model


def download_file_from_gcs(
    bucket_name: str, source_blob_name: str, destination_file_name: Path
) -> None:
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(source_blob_name)
    os.makedirs(destination_file_name.parent, exist_ok=True)
    blob.download_to_filename(destination_file_name)
