from __future__ import annotations


import torch
from PIL import Image
from torchvision import transforms, models

from resnet_embedding.const import EXPECTED_IMAGE_EMBEDDING
from resnet_embedding.models import EmbeddingModel

from serving_utils.setup_json_logger import setup_json_logger

logger = setup_json_logger(logger_name="calculate_resnet_embedding")


def calculate_embedding_differences(a: list[float], b: list[float]) -> list[float]:
    return [abs(an - bn) for an, bn in zip(a, b)]


def choose_device(model_supports_mps: bool) -> torch.device:
    """
    :param model_supports_mps: A boolean flag indicating whether the model supports the use of MPS
           (Multi-Process Service).

    :return: The selected torch.device based on the availability of MPS, CUDA, and CPU.

    """
    # this ensures that mps is available on the current machine.
    mps_available = torch.backends.mps.is_available()
    # this ensures that the current PyTorch installation was built with MPS activated.
    mps_is_build = torch.backends.mps.is_built()
    if mps_available and mps_is_build and model_supports_mps:
        logger.info("Using MPS")
        return torch.device("mps")
    elif torch.cuda.is_available():
        logger.info("Using CUDA")
        return torch.device("cuda:0")
    else:
        logger.info("Using CPU")
        return torch.device("cpu")


class CalculateResnetEmbedding:
    resnet_model: models.ResNet
    model: torch.nn.Sequential
    device: torch.device
    image_transforms: transforms.Compose

    def __init__(self, resnet_model) -> None:
        self.resnet_model = resnet_model
        self._init_model()
        self._init_image_transforms()

    def _init_model(self):
        """
        Initializes the model by loading a pre-trained ResNet-34 model, removing the final layer,
        and setting it to evaluation mode.

        :return: None
        """
        self.model = self.resnet_model
        # Remove the final layer to get embeddings instead of predictions
        self.model = torch.nn.Sequential(*(list(self.model.children())[:-1]))
        # set up the best available device
        self.device = choose_device(True)
        self.model = self.model.to(self.device)
        # Set the model to evaluation mode
        self.model.eval()

    def _init_image_transforms(self):
        """
        Initializes the image transforms for preprocessing.

        :return: None
        """
        self.image_transforms = transforms.Compose(
            [
                transforms.Resize(256),  # Resize the image to 256x256 pixels
                transforms.CenterCrop(
                    224
                ),  # Crop the image to 224x224 pixels from the center
                transforms.ConvertImageDtype(torch.float),
                # Normalize the image
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
                ),
            ]
        )

    def test_embedding(self) -> EmbeddingModel:
        """
        Test the embedding computation.

        we found that on different machines we can get very different embeddings. To reduce the
        chances of unexpected behavior going undetected, we can use a test image to compute the
        embedding vs the expected embedding.


        :return: The output dictionary containing the image embedding.
        """
        image_path = "resnet_embedding/test-image.jpg"
        image: Image.Image = Image.open(image_path).convert("RGB")
        image_tensor: torch.Tensor = transforms.PILToTensor()(image)
        output = self.compute_embedding(image_tensor)
        assert output.embedding is not None
        differences = calculate_embedding_differences(
            output.embedding, EXPECTED_IMAGE_EMBEDDING
        )
        assert all(diff < 1.0 for diff in differences)
        return output

    def preprocess_image(self, image_tensor: torch.Tensor) -> torch.Tensor:
        return self.image_transforms(image_tensor)

    def compute_embedding(self, image_tensor: torch.Tensor) -> EmbeddingModel:
        """
        Compute the embedding for the given image file using the pretrained model.

        :param image_tensor: The input image.
        :type image_tensor: torch.Tensor
        :return: A dictionary containing the embedding, model name, model version, and shape.
        :rtype: dict[str, Any]
        """
        # Pass the image for preprocessing and the image preprocessed
        img_preprocessed = self.preprocess_image(image_tensor)
        # Reshape, crop, and normalize the input tensor for feeding into network for evaluation
        img_tensor = torch.unsqueeze(img_preprocessed, 0)
        # move the tensor to the GPU if available
        img_tensor = img_tensor.to(self.device)
        # evaluate
        with torch.no_grad():
            model = self.model(img_tensor)
        img_embedding: list[float] = model.cpu().flatten().tolist()
        output = EmbeddingModel(
            embedding=img_embedding,
            model_name="resnet34",
            model_version="v0.1",
        )
        return output
