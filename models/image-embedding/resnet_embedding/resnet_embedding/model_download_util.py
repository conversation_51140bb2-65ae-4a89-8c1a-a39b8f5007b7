# A utility cli for downloading the ResNet-34 model weights and saving the model to a file.

import torch
from torchvision import models

# Initialize the ResNet-34 model
weights = models.ResNet34_Weights.IMAGENET1K_V1
model = models.resnet34(weights=weights)

save_path = f"/tmp/resnet34_{weights.name}.pt"

# Save the model weights
model_state = model.state_dict()
torch.save(model.state_dict(), save_path)

print(f"Model weights saved to {save_path}. Copy it to the models bucket.")
