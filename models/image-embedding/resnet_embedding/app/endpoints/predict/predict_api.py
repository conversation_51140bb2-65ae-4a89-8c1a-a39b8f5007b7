from http import HTTPStatus
from io import BytesIO
from PIL import Image
from fastapi import APIRouter, Response, UploadFile, File
from datetime import datetime
from serving_utils.bentoml_helpers import instrument_tracing
import torch
from torchvision import transforms
from concurrent.futures import ThreadPoolExecutor
import asyncio

from resnet_embedding.model_resolver import download_model_weights, init_resnet_model
from resnet_embedding.calculate_resnet_embedding import CalculateResnetEmbedding
from resnet_embedding.models import EmbeddingModel
from serving_utils.setup_json_logger import setup_json_logger

_logger = setup_json_logger(logger_name="ResnetEmbeddingService")


class ResnetEmbeddingService:
    """Service class for ResNet model inference."""

    def __init__(self) -> None:
        instrument_tracing()
        download_model_weights(_logger)
        self.prediction_timestamp = datetime.now()
        self.model = init_resnet_model(_logger)
        self.embedding = CalculateResnetEmbedding(self.model)
        self.embedding.test_embedding()

        # Used so that the predict method calls are put in a separate queue than livez calls
        self.predict_executor = ThreadPoolExecutor(
            max_workers=1, thread_name_prefix="predict"
        )

        self.router = APIRouter()
        self.router.add_api_route("/predict", self.predict, methods=["POST"])
        self.router.add_api_route("/livez", self.livez, methods=["GET"])
        self.router.add_api_route("/readyz", self.readyz, methods=["GET"])

    def get_image(self, input_image: UploadFile = File(...)) -> Image.Image:
        image_contents = input_image.file.read()
        return Image.open(BytesIO(image_contents))

    def compute_embedding(self, image: Image.Image) -> EmbeddingModel:
        image_tensor: torch.Tensor = transforms.PILToTensor()(image)
        return self.embedding.compute_embedding(image_tensor)

    async def predict(self, image: UploadFile = File(...)) -> EmbeddingModel:
        loop = asyncio.get_running_loop()
        # Run image loading in a separate thread
        pil_image = await loop.run_in_executor(
            self.predict_executor, self.get_image, image
        )
        # Run embedding computation in a separate thread
        result = await loop.run_in_executor(
            self.predict_executor, self.compute_embedding, pil_image
        )
        self.prediction_timestamp = datetime.now()
        return result

    def livez(self) -> Response:
        """Health check endpoint that verifies the service is running."""
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    def generate_dummy_image_file(self) -> UploadFile:
        dummy_img = BytesIO()
        Image.new("RGB", (1, 1)).save(dummy_img, format="JPEG")
        dummy_img.seek(0)
        return UploadFile(filename="dummy.jpg", file=dummy_img)

    async def readyz(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        if not hasattr(self, "model") or self.model is None:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR, content="Model not loaded"
            )

        if not hasattr(self.embedding, "device"):
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                content="Device not configured",
            )

        try:
            # this will be put in the same queue as normal predict calls
            await self.predict(self.generate_dummy_image_file())
            self.prediction_timestamp = datetime.now()
            return Response(status_code=HTTPStatus.OK, content="OK")
        except Exception as e:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                content=f"Model inference failed: {str(e)}",
            )
