"""
Defines the API endpoints for all operations
© Apella Inc 2025
"""

from fastapi import FastAPI
from serving_utils.add_fastapi_routes import add_fastapi_routes
from serving_utils.timeout_middleware import timeout_middleware

from .predict.predict_api import ResnetEmbeddingService


def create_api() -> FastAPI:
    """Creates the FastAPI application with all routes, instrumentation, and DI."""

    api: FastAPI = FastAPI(
        title="resnet-embedding",
        description="ResNet-34 image embedding model service for generating embeddings of images",
    )

    api.middleware("http")(timeout_middleware)

    add_fastapi_routes(api, ResnetEmbeddingService.__name__)
    return api


__all__ = [
    "create_api",
]
