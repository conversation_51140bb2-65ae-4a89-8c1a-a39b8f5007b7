SHELL := /bin/bash
BENTOML_SERVICE := $(shell grep '^service:' bentofile.yaml | cut -d ':' -f 2- | tr -d " '")

fix-lint:
	poetry run ruff --fix .
	poetry run ruff format .

lint:
	poetry run ruff .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-bentoml-service:
	echo "Running BentoML service at http://localhost:3000/ ($(BENTOML_SERVICE))"
	poetry run bentoml serve $(BENTOML_SERVICE) --development --reload
