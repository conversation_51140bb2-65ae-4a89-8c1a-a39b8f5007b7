import logging
from datetime import datetime, timedelta
from typing import List
from pydantic import BaseModel

import bentoml

class APIInputs(BaseModel):
    # Change this accordingly
    org_id: str

class PredictedOutput(BaseModel):
    prediction: float

@bentoml.service(
    traffic={"timeout": 10},
)
class ModelInferenceService:
    # def __init__(self) -> None:
        # Set your model name
        # instantiate ModelStorage if it's store
        # read any cached data

    @bentoml.api  # type: ignore[misc]
    def predict(self, inputs: APIInputs) -> PredictedOutput:
        # Your predict method
        return PredictedOutput(prediction=3.14)

    @bentoml.api  # type: ignore[misc]
    def model_version_deployed(self) -> str:
        return "my_version"

    
