[tool.ruff]
ignore = [
   # Trust black to get line length right.
   # Without this, there are cases where black won't reflow a
   # line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]
line-length = 100

[tool.mypy]
strict = true

[[tool.mypy.overrides]]
module = [
    "google.*",
    "sklearn.*",
    "joblib"
]
ignore_missing_imports = true

[tool.poetry]
name = "$project_name"
version = "0.1.0"
description = ""
authors = ["Apella Engineering <<EMAIL>>", "Data Science & Machine Learning <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
pandas = "^2.2.1"
google-cloud-storage = "^2.15.0"
pydantic = "^2.6.3"
cloudpickle = "^3.0.0"
dill = "^0.3.7"

[tool.poetry.group.dev.dependencies]
isort = "^5.13.2"
ruff = "^0.3.0"
pytest = "^8.0.2"
pytest-cov = "^4.1.0"
mypy = "^1.8.0"
pandas-stubs = "^2.2.0.240218"

[tool.poetry.group.serving.dependencies]
bentoml = "~1.2.4"

[tool.poetry.group.training.dependencies]
google-cloud-bigquery = "^3.18.0"
google-cloud-secret-manager = "^2.18.2"
db-dtypes = "^1.2.0"
training-utils = {path = "$training_utils_path", develop = true}
tenacity = "^8.2.3"

[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

