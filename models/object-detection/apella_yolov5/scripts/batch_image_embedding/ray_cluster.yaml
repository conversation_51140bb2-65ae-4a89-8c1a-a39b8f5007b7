apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: ray-yolo-cluster
spec:
  enableInTreeAutoscaling: true
  headGroupSpec:
    rayStartParams:
      dashboard-host: 0.0.0.0
      num-cpus: '0'
    serviceType: ClusterIP
    template:
      metadata:
        annotations: {}
        labels:
          app.kubernetes.io/instance: ray-cluster
          app.kubernetes.io/managed-by: Helm
          helm.sh/chart: ray-cluster-1.1.0
      spec:
        affinity: {}
        containers:
          - image: >-
              us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/object-detection/apella_yolov5/bentoml@sha256:bb5c08a46261f13022da8874d9c88bf3e29eb681ca94dce9d67044d5dd2684b8
            name: ray-head
            resources:
              limits:
                cpu: 1
                memory: 8G
              requests:
                cpu: 1
                memory: 8G
            securityContext: {}
            volumeMounts:
              - mountPath: /tmp/ray
                name: log-volume
        imagePullSecrets: []
        nodeSelector: {}
        serviceAccountName: ray-sa
        tolerations: []
        volumes:
          - emptyDir: {}
            name: log-volume
  workerGroupSpecs:
    - groupName: t4Worker
      maxReplicas: 10
      minReplicas: 1
      numOfHosts: 1
      rayStartParams:
        resources: '"{\"4xt4\": 1}"'
      replicas: 1
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: ray-cluster
            app.kubernetes.io/managed-by: Helm
            helm.sh/chart: ray-cluster-1.1.0
        spec:
          containers:
            - image: >-
                us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/object-detection/apella_yolov5/bentoml@sha256:bb5c08a46261f13022da8874d9c88bf3e29eb681ca94dce9d67044d5dd2684b8
              imagePullPolicy: IfNotPresent
              name: ray-worker
              resources:
                limits:
                  cpu: 32
                  memory: 64G
                  nvidia.com/gpu: 4
                requests:
                  cpu: 17
                  memory: 32G
                  nvidia.com/gpu: 4
          imagePullSecrets: []
          nodeSelector:
            cloud.google.com/gke-accelerator: nvidia-tesla-t4
          serviceAccountName: ray-sa
    - groupName: v100Worker
      maxReplicas: 10
      minReplicas: 0
      numOfHosts: 1
      rayStartParams:
        resources: '"{\"v100\": 1}"'
      replicas: 0
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: ray-cluster
            app.kubernetes.io/managed-by: Helm
            helm.sh/chart: ray-cluster-1.1.0
        spec:
          containers:
            - image: >-
                us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/object-detection/apella_yolov5/bentoml@sha256:bb5c08a46261f13022da8874d9c88bf3e29eb681ca94dce9d67044d5dd2684b8
              imagePullPolicy: IfNotPresent
              name: ray-worker
              resources:
                limits:
                  cpu: 1
                  memory: 16G
                  nvidia.com/gpu: 1
                requests:
                  cpu: 1
                  memory: 4G
                  nvidia.com/gpu: 1
          imagePullSecrets: []
          nodeSelector:
            cloud.google.com/gke-accelerator: nvidia-tesla-v100
          serviceAccountName: ray-sa
    - groupName: workergroup
      maxReplicas: 40
      minReplicas: 0
      numOfHosts: 1
      rayStartParams: {}
      replicas: 0
      template:
        metadata:
          annotations: {}
          labels:
            app.kubernetes.io/instance: ray-cluster
            app.kubernetes.io/managed-by: Helm
            helm.sh/chart: ray-cluster-1.1.0
        spec:
          affinity: {}
          containers:
            - image: >-
                us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ml-services/models/object-detection/apella_yolov5/bentoml@sha256:bb5c08a46261f13022da8874d9c88bf3e29eb681ca94dce9d67044d5dd2684b8
              name: ray-worker
              resources:
                limits:
                  cpu: 16
                  memory: 8G
                requests:
                  cpu: 1
                  memory: 8G
              securityContext: {}
              volumeMounts:
                - mountPath: /tmp/ray
                  name: log-volume
          imagePullSecrets: []
          nodeSelector: {}
          serviceAccountName: ray-sa
          tolerations: []
          volumes:
            - emptyDir: {}
              name: log-volume