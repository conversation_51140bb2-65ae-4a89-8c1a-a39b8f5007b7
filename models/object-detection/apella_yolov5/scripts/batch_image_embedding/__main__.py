"""
This is Ray job for batch image embedding. It reads from a BigQuery table, downloads images from GCS, runs inference on the images using YOLO model, and writes the embeddings back to a BigQuery table.
"""

import sys
from typing import Any
import datetime
from serving_utils.config import get_dev_stub
import ray
import os
import yaml  # type: ignore
import argparse
import pyarrow as pa
from google.cloud import bigquery, storage  # type: ignore
from google.api_core import retry, exceptions
from typing import List, Optional

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.append(project_root)

# E402 noqas needed because of the order of imports
from app.endpoints.predict.predict_api import YoloInferenceService  # noqa: E402


class ImageDownloader:
    def __init__(self):
        self.client = storage.Client()

    def __call__(self, batch: dict[str, Any]):
        images_bytes = []
        for frame_url in batch["frame_url"]:
            bucket_name = frame_url.split("/")[2]
            blob_name = "/".join(frame_url.split("/")[3:])
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(blob_name)

            # Download to memory
            images_bytes.append(blob.download_as_bytes())
        batch["image_bytes"] = images_bytes
        return batch


class BigQueryWriter:
    def __init__(
        self,
        project_id: str,
        dataset_id: str,
        table_id: str,
        schema: Optional[List[bigquery.SchemaField]] = None,
        write_disposition: str = "WRITE_APPEND",
    ):
        """
        Initialize BigQuery writer for use with Ray's map_batches.

        Args:
            project_id: GCP project ID
            dataset_id: BigQuery dataset ID
            table_id: BigQuery table ID
            schema: Optional BigQuery schema definition
            write_disposition: BigQuery write disposition
        """
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.table_id = table_id
        self.schema = schema
        self.write_disposition = write_disposition

        # Initialize these in __call__ to ensure proper serialization
        self.client = None
        self.job_config = None
        self.table_ref = None

    def _initialize_client(self):
        """Initialize BigQuery client and config (called per worker)"""
        if self.client is None:
            self.client = bigquery.Client(project=self.project_id)
            self.table_ref = f"{self.project_id}.{self.dataset_id}.{self.table_id}"
            schema = self.client.get_table(self.table_ref).schema
            # Replace only the "embedding" field in the schema
            for field in schema:
                if field.name == "embedding":
                    field._properties["mode"] = "REPEATED"
                    field._properties["type"] = "FLOAT64"
            # Create LoadJobConfig with modified schema
            self.job_config = bigquery.LoadJobConfig(
                schema=schema,
                write_disposition=self.write_disposition,
                source_format=bigquery.SourceFormat.PARQUET,
            )
            parquet_options = bigquery.format_options.ParquetOptions()
            # strangely, this needs to be assigned as a property of the job_config instead of instantiated with the job_config
            parquet_options.enable_list_inference = True
            self.job_config.parquet_options = parquet_options

    @retry.Retry()
    def __call__(self, batch: dict[str, Any]) -> dict[str, Any]:
        """
        Process a batch of data. This method is called by Ray's map_batches.

        Args:
            batch: A batch of data to process

        Returns:
            batch: The processed batch
        """
        self._initialize_client()
        table = pa.Table.from_pydict(batch)

        # Write table to temporary Parquet file in memory
        with pa.BufferOutputStream() as output_stream:
            # use_compliant_nested_type=True is needed to write nested types in a way that BigQuery can read
            pa.parquet.write_table(table, output_stream, use_compliant_nested_type=True)
            data = output_stream.getvalue()

        # needed for type checking
        assert self.client is not None
        # Load the data into BigQuery
        job = self.client.load_table_from_file(
            pa.BufferReader(data), self.table_ref, job_config=self.job_config
        )
        job.result()  # Wait for the job to complete
        return batch


def start_stop_generator(
    start: datetime.datetime, stop: datetime.datetime, interval: datetime.timedelta
):
    while start < stop:
        yield start, start + interval
        start += interval


def get_project_and_dataset() -> tuple[str, str]:
    # if running on mac or in dev; dev_stub = "1" means running in dev
    if sys.platform == "darwin" or get_dev_stub() == "1":
        # read from dev bigquery table
        project_id = "dev-ml-794354"
        dataset = "dev_realtime"
    else:
        # read from prod bigquery table
        project_id = "prod-ml-2fc132"
        dataset = "prod_realtime"
    return project_id, dataset


@ray.remote
def fn(config: dict[str, Any], start: datetime.datetime, stop: datetime.datetime):
    project_id, dataset = get_project_and_dataset()

    query = f"""
        SELECT guid, org_id, room_id, camera_id, source, frame_url, frame_time
        FROM `{project_id}.{dataset}.image_processing_output`
        WHERE frame_time BETWEEN TIMESTAMP('{start}') AND TIMESTAMP('{stop}')
    """

    print(query)
    ds = (
        ray.data.read_bigquery(
            query=query, project_id=project_id, concurrency=config["bigquery_reader"]["concurrency"]
        )
        .repartition(config["bigquery_reader"]["num_partitions"])
        .map_batches(
            ImageDownloader,
            batch_size=config["img_downloader"]["batch_size"],
            concurrency=config["img_downloader"]["concurrency"],
        )
        .map_batches(
            YoloInferenceService,
            batch_size=config["yolo_inference"]["batch_size"],
            concurrency=config["yolo_inference"]["concurrency"],
            # all the cpus will have been claimed by the ImageDownloader so we need to set num_cpus=0 since YoloInferenceService only uses GPU
            num_cpus=0,
            num_gpus=config["yolo_inference"]["num_gpus"],
        )
        .map_batches(
            BigQueryWriter,
            concurrency=config["bigquery_writer"]["concurrency"],
            fn_constructor_kwargs={
                "project_id": project_id,
                "dataset_id": dataset,
                "table_id": config["write_table"],
            },
        )
    )
    ds.materialize()


if __name__ == "__main__":
    # parse cli args
    parser = argparse.ArgumentParser(description="Batch image embedding job")
    parser.add_argument(
        "--config",
        type=str,
        required=False,
        help="Path to config file, which will be merged with defaults.yaml",
    )
    parser.add_argument(
        "--start", type=str, required=False, help="Start time in ISO format (YYYY-MM-DD HH:MM:SS)"
    )
    parser.add_argument(
        "--stop", type=str, required=False, help="Stop time in ISO format (YYYY-MM-DD HH:MM:SS)"
    )
    args = parser.parse_args()

    with open("./scripts/batch_image_embedding/defaults.yaml", "r") as f:
        config = yaml.safe_load(f)

    if args.config:
        with open(args.config, "r") as f:
            config.update(yaml.safe_load(f))

    if args.start:
        start = datetime.datetime.fromisoformat(args.start)
    else:
        start = config["start"]
    if args.stop:
        stop = datetime.datetime.fromisoformat(args.stop)
    else:
        stop = config["stop"]

    # if running on a mac, set num_gpus to 0
    if sys.platform == "darwin":
        config["yolo_inference"]["num_gpus"] = 0
        config["yolo_inference"]["resources"] = {}

    project_id, dataset = get_project_and_dataset()
    client = bigquery.Client(project=project_id)

    write_table = f"{project_id}.{dataset}.{config['write_table']}"

    # check if the write_table exists, and if it doesn't create it using the schema from the image_embeddings table
    try:
        client.get_table(write_table)
    except exceptions.NotFound:
        # copy the schema from the image_embeddings table and use it to create the write_table
        source_table_ref = f"{project_id}.{dataset}.image_embeddings"
        source_table = client.get_table(source_table_ref)
        table = bigquery.Table(write_table, schema=source_table.schema)
        client.create_table(table)

    ray.get(
        [
            # this causes each fn call to run on a separate node with the given GPU type
            fn.options(resources={**config["yolo_inference"]["resources"]}).remote(
                config, start, stop
            )
            for start, stop in start_stop_generator(
                start, stop, datetime.timedelta(seconds=config["batch_interval"])
            )
        ]
    )
