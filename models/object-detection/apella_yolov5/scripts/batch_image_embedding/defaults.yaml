start: 2025-02-03 00:00:00
stop: 2025-02-03 00:01:00
# the batch interval in seconds used for the WHERE clause for gathering frame_urls
# batch_interval: 60 means that all images in 60 second intervals will be processed
# there are currently ~14k images/minute in prod
batch_interval: 60
write_table: yolo_embeddings
bigquery_reader:
  concurrency: 1
  num_partitions: 8
bigquery_writer:
  concurrency: 1
img_downloader:
  batch_size: 100
  concurrency: 8
yolo_inference:
  batch_size: 100
  concurrency: 4
  num_gpus: 1
  # "4xt4" a t4 worker with 4xt4s in the pod; it's defined in our K8s ray-cluster CRD
  resources: {"4xt4": 1}