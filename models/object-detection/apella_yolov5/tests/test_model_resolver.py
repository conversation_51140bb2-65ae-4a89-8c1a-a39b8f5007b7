from pathlib import Path
from unittest.mock import Mock, patch

from app.utils import version_config
from app.utils.model_resolver import ModelInfo, resolve_model


class TestModelResolver:
    @patch("os.environ", {"YOLO_MODEL_SIZE": "x"})
    def test_withXLSizeFlag_resolvesToXLBaseModel(self) -> None:
        expected_model_info = ModelInfo(
            model_filename=Path("yolo11x.pt"), model_version="yolo11x.pt"
        )

        assert resolve_model() == expected_model_info

    @patch("os.environ", {"DEV_STUB": "1"})
    @patch("google.cloud.storage.Client")
    def test_withDevStubFlag_resolvesToBaseModel(self, mock_gcs_client: Mock) -> None:
        resolved_model = resolve_model()

        assert resolved_model.model_version == version_config.BASE_MODEL_VERSION

    @patch("app.utils.version_config.PROD_MODEL_VERSION", "prod_model_version")
    @patch("app.utils.version_config.PROD_MODEL_URI", "prod_model_uri")
    @patch("app.utils.version_config.PROD_BUCKET_NAME", "prod_bucket_name")
    @patch("google.cloud.storage.Client")
    def test_withNoVersionFlags_resolvesToProdModel(self, mock_gcs_client: Mock) -> None:
        resolved_model = resolve_model()
        print(resolved_model.model_filename)
        assert resolved_model.model_filename.match("*prod_model_uri")
        assert resolved_model.model_version == "prod_model_version"
        mock_gcs_client.return_value.bucket.assert_called_with("prod_bucket_name")
        assert mock_gcs_client.return_value.bucket.return_value.blob.return_value.download_to_filename.called
