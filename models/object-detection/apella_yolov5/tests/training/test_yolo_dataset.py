from pathlib import Path
import pandas as pd
import yaml
import tempfile
from typing import cast
import pandera as pa

from apella_yolov5.training import yolo_dataset
from apella_yolov5.training.data_selector import DSType, ImageLabelsSchema


def test_write_config_file(tmp_path: Path) -> None:
    """Test that config file has the right structure/data.
    In this test, we are not testing every aspect of the config file, we test just that a few attributes do exist
    and have the correct values
    """
    labels_2_idx = {"label_a": 0, "label_b": 1}
    yolo_dataset.write_config_file(tmp_path, labels_2_idx)

    with open(tmp_path / "config.yaml", "r") as fid:
        content = yaml.safe_load(fid)

    assert content["names"][0] == "label_a"
    assert content["names"][1] == "label_b"
    assert content["test"] == "images/test"
    assert content["train"] == "images/train"
    assert content["val"] == "images/val"


def test_create_images_symbolic_links() -> None:
    """Test that symbolic links are created."""
    dataset_with_pv_locations = pd.DataFrame(
        {
            "anonymized_image_uri": [
                "gs://bucket/images/1.jpg",
                "gs://bucket/images/2.jpg",
                "gs://bucket/images/3.jpg",
            ],
            "pv_path": [
                # on Mac, /tmp gets resolved to /private/tmp, so we use that to avoid platform issues
                Path("/private/tmp/1.jpg"),
                Path("/private/tmp/2.jpg"),
                Path("/private/tmp/3.jpg"),
            ],
        }
    )

    with tempfile.TemporaryDirectory() as data_dir:
        data_dir_path = Path(data_dir)
        # create the symbolic links
        yolo_dataset.create_images_symbolic_links(
            data_dir_path, dataset_with_pv_locations, DSType.TEST
        )

        # check that the symbolic links were created
        assert (data_dir_path / "images/test").is_dir()
        assert (data_dir_path / "images/test/1.jpg").is_symlink()
        assert (data_dir_path / "images/test/2.jpg").is_symlink()
        assert (data_dir_path / "images/test/3.jpg").is_symlink()
        assert (data_dir_path / "images/test/1.jpg").resolve() == Path("/private/tmp/1.jpg")
        assert (data_dir_path / "images/test/2.jpg").resolve() == Path("/private/tmp/2.jpg")
        assert (data_dir_path / "images/test/3.jpg").resolve() == Path("/private/tmp/3.jpg")


def test_generate_pv_locations() -> None:
    dataset = pd.DataFrame(
        {
            "anonymized_image_uri": [
                "gs://bucket/images/1.jpg",
                "gs://bucket/images/2.jpg",
                "gs://bucket/images/3.jpg",
            ],
            "image_bin_type": ["train", "test", "val"],
            "object_labels": [[], [], []],
            "site_id": ["site1", "site2", "site3"],
            "org_id": ["org1", "org2", "org3"],
        }
    )
    schema_df = cast(pa.typing.DataFrame[ImageLabelsSchema], ImageLabelsSchema.validate(dataset))

    df_with_pv_dir = yolo_dataset.generate_pv_locations(schema_df, Path("/tmp/images"))
    assert "pv_path" in df_with_pv_dir.columns
    assert df_with_pv_dir["pv_path"][0] == Path("/tmp/images/1.jpg")
    assert df_with_pv_dir["pv_path"][1] == Path("/tmp/images/2.jpg")
    assert df_with_pv_dir["pv_path"][2] == Path("/tmp/images/3.jpg")


def test_filter_out_existing_images() -> None:
    """Test that existing images are filtered out."""
    with tempfile.TemporaryDirectory() as data_dir:
        pv_images_path = Path(data_dir)

        dataset_with_pv_locations = pd.DataFrame(
            {
                "file_name": ["1.jpg", "2.jpg", "3.jpg"],
                "anonymized_image_uri": [
                    "gs://bucket/images/1.jpg",
                    "gs://bucket/images/2.jpg",
                    "gs://bucket/images/3.jpg",
                ],
            }
        )

        # create some dummy files
        (pv_images_path / "1.jpg").touch()
        (pv_images_path / "2.jpg").touch()

        only_new_images = yolo_dataset._filter_out_existing_images(
            dataset_with_pv_locations, pv_images_path
        )
        assert only_new_images == ["gs://bucket/images/3.jpg"]
