from apella_yolov5.training.data_selector import DataSelector
import pandas as pd


def test_filter_labels() -> None:
    df = pd.DataFrame(
        {
            "object_labels": [
                [{"label_name": "label1"}, {"label_name": "label2"}, {"label_name": "label3"}],
                [{"label_name": "label2"}, {"label_name": "label3"}],
                [{"label_name": "label1"}, {"label_name": "label3"}],
            ]
        }
    )
    label_names_to_keep = ["label1", "label3"]
    expected_df = pd.DataFrame(
        {
            "object_labels": [
                [{"label_name": "label1"}, {"label_name": "label3"}],
                [{"label_name": "label3"}],
                [{"label_name": "label1"}, {"label_name": "label3"}],
            ]
        }
    )
    assert DataSelector.filter_labels(df, label_names_to_keep).equals(expected_df)
