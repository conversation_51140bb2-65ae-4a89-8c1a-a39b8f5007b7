import json
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any, List, Tuple
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

from fastapi import UploadFile
import pandas as pd
import pytest
import torch
import torch.nn as nn
from torch import Tensor

from app.endpoints.predict.predict_api import (
    YoloInferenceService,
    convert_to_consumable_format,
    PredictResponse,
)
from app.utils.model_resolver import ModelInfo


# When loaded, the YOLOv11 model has many nested modules, requiring many mock classes for testing.
class MockLayer(nn.Module):
    def __init__(self) -> None:
        super().__init__()

    def forward(self, x: Tensor) -> Tensor:
        return torch.randn(1, 64, 8, 8)  # Return dummy feature map


class MockSequentialModel(nn.Module):
    def __init__(self) -> None:
        super().__init__()
        # Create mock layers that will be actual nn.Module instances
        self.layers: nn.ModuleList = nn.ModuleList([MockLayer() for _ in range(24)])

    def __getitem__(self, idx: int) -> nn.Module:
        return self.layers[idx]


class MockDetectionModel:
    def __init__(self) -> None:
        self.model: MockSequentialModel = MockSequentialModel()


class MockYOLO:
    def __init__(self) -> None:
        self.model: MockDetectionModel = MockDetectionModel()


class MockYolov11Detections:
    def __init__(self) -> None:
        self.xyxyn: List[pd.DataFrame] = [
            pd.DataFrame(
                {
                    "xmin": [0.1],
                    "ymin": [0.2],
                    "xmax": [0.3],
                    "ymax": [0.4],
                    "name": ["person"],
                    "confidence": [0.8],
                }
            )
        ]

    def __str__(self) -> str:
        return '[{"xmin":0.1,"ymin":0.2,"xmax":0.3,"ymax":0.4,"name":"person","confidence":0.8}]'

    def to_json(self) -> str:
        return self.__str__()

    def to_df(self, normalize: bool = False) -> pd.DataFrame:
        return pd.DataFrame(
            {
                "name": ["tv", "person"],
                "class": [62, 0],
                "confidence": [0.52141, 0.25524],
                "box": [
                    {"x1": 0.10815034, "y1": 0.08527917, "x2": 0.18734183, "y2": 0.15630316},
                    {"x1": 0.31135171, "y1": 0.22774771, "x2": 0.38183108, "y2": 0.53783345},
                ],
            }
        )


class MockYolov11Model:
    def __init__(self) -> None:
        self.model: MockDetectionModel = MockDetectionModel()

    def __call__(
        self, source: Any, imgsz: int, device: str, **kwargs: Any
    ) -> List[MockYolov11Detections]:
        # Generate mock feature maps that will trigger the hooks
        mock_feature: Tensor = torch.randn(1, 64, 8, 8)
        for idx in [16, 19, 22]:  # The indices we care about
            layer = self.model.model.layers[idx]
            if hasattr(layer, "_forward_hooks"):
                hooks = getattr(layer, "_forward_hooks")
                for hook in hooks.values():
                    hook(layer, (mock_feature,), mock_feature)
        return [MockYolov11Detections()]

    def info(self) -> Tuple[int, int, int, float]:
        return (319, 2624080, 2624064, 6.6)  # Fake model config


class MockYoloLoad:
    def __call__(self, model_filename: Path) -> MockYolov11Model:
        return MockYolov11Model()


class TestYoloInferenceService:
    @patch("app.endpoints.predict.predict_api.YOLO")
    @patch("app.endpoints.predict.predict_api.resolve_model")
    def test_loadsResolvedModel(self, mock_resolver: Mock, mock_yolov5_load: Mock) -> None:
        yolov5_instance = MagicMock()
        yolov5_instance.info = MagicMock()
        yolov5_instance.info.return_value = (319, 2624080, 2624064, 6.6)  # Fake model config
        mock_yolov5_load.return_value = yolov5_instance

        expected_file_path: Path = Path("resolved_model.pt")
        mock_resolver.return_value = ModelInfo(
            model_filename=expected_file_path, model_version="resolved_version"
        )
        yolo_inference_service = YoloInferenceService()
        assert Path("resolved_model.pt") in mock_yolov5_load.call_args.args
        assert yolo_inference_service.model_info.model_filename == expected_file_path
        assert yolo_inference_service.model_info.model_version == "resolved_version"

    @patch("app.endpoints.predict.predict_api.resolve_model")
    def test_whenFileNotFound_raisesValueError(self, mock_resolver: Mock) -> None:
        mock_resolver.return_value = ModelInfo(
            model_filename=Path("model_without_file.pt"), model_version="model_version"
        )
        with pytest.raises(ValueError):
            YoloInferenceService()

    @patch("app.endpoints.predict.predict_api.YOLO")
    @patch("app.endpoints.predict.predict_api.select_device")
    @patch("app.endpoints.predict.predict_api.resolve_model")
    def test_loadsModelWithDetectedDevice(
        self,
        mock_resolver: Mock,
        mock_select_device: Mock,
        mock_yolov5_load: Mock,
    ) -> None:
        yolov5_instance = MagicMock()
        yolov5_instance.info = MagicMock()
        yolov5_instance.info.return_value = (319, 2624080, 2624064, 6.6)  # Fake model config
        mock_yolov5_load.return_value = yolov5_instance

        mock_select_device.return_value = "detected_device"
        yolo_inference_service = YoloInferenceService()
        assert yolo_inference_service.detected_device == "detected_device"

    @pytest.mark.asyncio
    @patch("app.endpoints.predict.predict_api.YOLO", MockYoloLoad())
    @patch("app.endpoints.predict.predict_api.resolve_model")
    async def test_predict_returnsModelVersionWithPredictionsAndEmbedding(
        self, mock_resolver: Mock
    ) -> None:
        mock_resolver.return_value = ModelInfo(
            model_filename=Path("resolved_model.pt"), model_version="resolved_model_version-v1.0.0"
        )
        service = YoloInferenceService()

        mock_upload_file = MagicMock(spec=UploadFile)
        with open("tests/fixtures/floor.png", "rb") as f:
            mock_upload_file.file = BytesIO(f.read())
        mock_upload_file.filename = "floor.png"
        response = await service.predict(input_image=mock_upload_file)

        assert isinstance(response, PredictResponse)

        # Check required fields are present
        assert response.predictions
        assert response.model_version
        assert response.embedding
        assert response.prediction_objects

        # Check values
        assert response.model_version == "resolved_model_version-v1.0.0"
        assert isinstance(response.embedding, list)  # Embedding should be converted to list
        assert isinstance(response.predictions, str)  # Predictions should be a JSON string (legacy)
        assert isinstance(response.prediction_objects, list)  # Prediction objects should be a list

        assert len(response.prediction_objects) == 2
        assert response.prediction_objects[0].name == "tv"
        assert response.prediction_objects[0].confidence == 0.52141
        assert response.prediction_objects[0].xmin == 0.10815034
        assert response.prediction_objects[0].ymin == 0.08527917
        assert response.prediction_objects[0].xmax == 0.18734183
        assert response.prediction_objects[0].ymax == 0.15630316

        assert response.prediction_objects[1].name == "person"
        assert response.prediction_objects[1].confidence == 0.25524
        assert response.prediction_objects[1].xmin == 0.31135171
        assert response.prediction_objects[1].ymin == 0.22774771
        assert response.prediction_objects[1].xmax == 0.38183108
        assert response.prediction_objects[1].ymax == 0.53783345

    def test_convert_to_consumable_format_converts_correctly(self) -> None:
        # Create test DataFrame with normalized coordinates (0-1 range)
        test_df = pd.DataFrame(
            {
                "name": ["person", "tv"],
                "confidence": [0.89161, 0.52141],
                "box": [
                    {"x1": 0.361, "y1": 0.201, "x2": 0.523, "y2": 0.609},
                    {"x1": 0.108, "y1": 0.085, "x2": 0.187, "y2": 0.156},
                ],
            }
        )

        # Convert to legacy format
        prediction_objects, prediction_str = convert_to_consumable_format(test_df)

        # Parse result back to Python object for assertion
        result_dict = json.loads(prediction_str)

        # Verify structure and values
        assert len(result_dict) == 2
        assert result_dict[0] == {
            "name": "person",
            "confidence": 0.89161,
            "xmin": 0.361,
            "ymin": 0.201,
            "xmax": 0.523,
            "ymax": 0.609,
        }
        assert result_dict[1] == {
            "name": "tv",
            "confidence": 0.52141,
            "xmin": 0.108,
            "ymin": 0.085,
            "xmax": 0.187,
            "ymax": 0.156,
        }

        assert len(prediction_objects) == 2
        assert prediction_objects[0].name == "person"
        assert prediction_objects[1].name == "tv"

    def test_convert_to_consumable_format_converts_handle_no_predictions(self) -> None:
        # Create test DataFrame with normalized coordinates (0-1 range)
        test_df = pd.DataFrame({})

        # Convert to legacy format
        prediction_objects, result = convert_to_consumable_format(test_df)

        # Parse result back to Python object for assertion
        result_dict = json.loads(result)

        # Verify structure and values
        assert len(result_dict) == 0
        assert len(prediction_objects) == 0
