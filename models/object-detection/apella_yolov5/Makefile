SHELL := /bin/bash
PORT := 9981

# Default Google credentials path for Mac
GOOGLE_CREDS ?= $(HOME)/.config/gcloud/application_default_credentials.json

.PHONY: build-container

build-container:
	docker build \
		--build-arg PROJECT_DIR=models/object-detection/apella_yolov5 \
		--build-arg PROJECT_NAME=apella_yolov5 \
		--secret id=google-application-credentials,src=$(GOOGLE_CREDS) \
		-t apella_yolov5:latest \
		-f Dockerfile \
		../../..

fix-lint:
	poetry run ruff check --fix .
	poetry run ruff format .

ruff-lint:
	poetry run ruff check .
	poetry run ruff format --check .

mypy-lint:
	poetry run mypy .

lint: ruff-lint mypy-lint

format: fix-lint

test:
	poetry run python -m pytest tests -v

run-local:
	echo "Running YoloInferenceService at http://localhost:$(PORT)/"
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	DEV_STUB=1 poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

locust-test-container:
	docker build --secret id=google-application-credentials,src=${HOME}/.config/gcloud/application_default_credentials.json -t apella_yolov5_locust_tests -f locust.Dockerfile .

locust-test-run-local: locust-test-container
	docker run \
		--rm \
		-p 8000:8000 \
		apella_yolov5_locust_tests  \
		-f apella_yolov5/locust/basic.py \
		-H http://host.docker.internal:9999 \
		--headless \
		-u 10 \
		-t 1m

run-training-locally:
	poetry run python -u -m apella_yolov5.training --train --evaluate --config-module-name apella_yolov5.configs.experiment_config

run-async-trainer:
	@if [ -z "$(MODEL_CONFIG_MODULE_NAME)" ]; then \
		echo "Error: MODEL_CONFIG_MODULE_NAME is required"; \
		echo "Usage: make run-async-trainer MODEL_CONFIG_MODULE_NAME=your.module.name"; \
		exit 1; \
	fi
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml --model-config-module-name $(MODEL_CONFIG_MODULE_NAME)
