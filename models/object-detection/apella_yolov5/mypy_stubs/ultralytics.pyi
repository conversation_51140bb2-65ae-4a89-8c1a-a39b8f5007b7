from PIL import Image, ImageFile
from typing import TypeVar
from pathlib import Path
from typing import Any, Callable, List, Tuple, Union
from torch import nn

class YOLO:
    def __init__(self, model_path: Union[str, Path]) -> None: ...
    def __call__(
        self,
        source: Union[str, Image.Image, ImageFile.ImageFile],
        conf: float,
        iou: float,
        agnostic_nms: bool,
        max_det: int,
        imgsz: int,
        device: str,
        verbose: bool,
        **kwargs: Any,
    ) -> List[Results]: ...
    def info(self) -> Tuple[int, ...]: ...
    model: DetectionModel

class DetectionModel:
    model: Sequential

T = TypeVar("T", bound=nn.Module)

class Sequential(nn.Sequential):
    def register_forward_hook(
        self,
        hook: Callable[[T, tuple[Any, ...], Any], Any | None]
        | Callable[[T, tuple[Any, ...], dict[str, Any], Any], Any | None],
        *,
        prepend: bool = ...,
        with_kwargs: bool = ...,
        always_call: bool = ...,
    ) -> Any: ...

class Results:
    def __len__(self) -> int: ...
    def to_df(self, normalize: bool = False) -> Any: ...  # Returns pandas DataFrame
    def __getitem__(self, idx: int) -> "Results": ...

def select_device(device: str = "") -> str: ...
