"""
Defines the API endpoints for all operations
© Apella Inc 2024
"""

from fastapi import FastAPI
from serving_utils.add_fastapi_routes import add_fastapi_routes
from serving_utils.timeout_middleware import timeout_middleware

from .predict.predict_api import YoloInferenceService


def create_api() -> FastAPI:
    """Creates the FastAPI application with all routes, instrumentation, and DI."""

    api: FastAPI = FastAPI(
        title="apella_yolo",
        description="YOLO (v11) object detection model service for detecting objects in images",
    )

    api.middleware("http")(timeout_middleware)

    add_fastapi_routes(api, YoloInferenceService.__name__)

    return api


__all__ = [
    "create_api",
]
