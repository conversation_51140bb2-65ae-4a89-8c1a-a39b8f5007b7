"""
Defines the API endpoints for the predict endpoint
© Apella Inc 2024
"""

import asyncio
from typing import Any
from http import HTTPStatus
import json
from io import BytesIO
from PIL import Image
from PIL.Image import Image as ImageFile
import pandas as pd
from fastapi import APIRouter, Response, UploadFile, File
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import torch
from torch import Tensor, concat, nn
from ultralytics import YOLO
from ultralytics.engine.results import Results  # type: ignore
from ultralytics.utils.torch_utils import select_device  # type: ignore

from app.utils.model_resolver import ModelInfo, resolve_model
from serving_utils.setup_json_logger import setup_json_logger
from pydantic import BaseModel, ConfigDict

_logger = setup_json_logger(logger_name="YoloInferenceService")

# default model config values copied from official yolov11 example page
default_model_config = {
    "conf": 0.25,  # NMS confidence threshold
    "iou": 0.45,  # NMS IoU threshold
    "agnostic_nms": False,  # NMS class-agnostic
    "max_det": 1000,  # maximum number of detections per image
}


class PredictionObject(BaseModel):
    xmin: float
    ymin: float
    xmax: float
    ymax: float
    name: str
    confidence: float


class PredictResponse(BaseModel):
    prediction_objects: list[PredictionObject]
    predictions: str
    model_version: str
    embedding: list[float]

    model_config = ConfigDict(protected_namespaces=())


def convert_to_consumable_format(
    predictions_df: pd.DataFrame,
) -> tuple[list[PredictionObject], str]:
    """Convert predictions to legacy (YoloV5) format."""
    if predictions_df.empty:
        return [], json.dumps([])

    prediction_objects = [
        PredictionObject(
            xmin=row["box"]["x1"],
            ymin=row["box"]["y1"],
            xmax=row["box"]["x2"],
            ymax=row["box"]["y2"],
            name=row["name"],
            confidence=row["confidence"],
        )
        for _, row in predictions_df.iterrows()
    ]

    result_json = json.dumps([obj.model_dump() for obj in prediction_objects])

    return prediction_objects, result_json


class YoloInferenceService:
    """Service class for YOLO model inference."""

    def __init__(self) -> None:
        self.detected_device = select_device()
        _logger.info(f"Using detected device: {self.detected_device}")
        self.model_info: ModelInfo = resolve_model()
        _logger.info(f"Using model {self.model_info.model_version}")

        try:
            self.model = YOLO(self.model_info.model_filename)
            # See YoloV11 configurations at https://github.com/ultralytics/ultralytics/blob/da15e27a4db75dfc21090f5fc321c8cde53fcc50/ultralytics/cfg/models/11/yolo11.yaml
            layers, *rest = self.model.info()

            assert layers in [
                319,
                409,
                631,
            ], f"Unexpected number of layers for YoloV11 model: {layers}"
        except FileNotFoundError as e:
            raise ValueError("Unable to find model file.") from e

        # manual warm-up to avoid cold start latency and the first embedding including YOLO's warm-up embedding
        self.model(
            torch.zeros(1, 3, 640, 640),  # type: ignore
            imgsz=640,
            device=self.detected_device,
        )

        self.prediction_timestamp = datetime.now()

        # Used so that the predict method calls are put in a separate queue than livez calls
        self.predict_executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix="predict")

        self.router = APIRouter()
        self.router.add_api_route("/predict", self.predict, methods=["POST"])
        self.router.add_api_route("/livez", self.livez, methods=["GET"])
        self.router.add_api_route("/readyz", self.ready, methods=["GET"])

    def __call__(self, batch: dict[str, Any]) -> dict[str, list]:
        """Used by ray.data.map_batches to run inference on a batch of images."""
        embeddings = []
        model_versions = []
        for img_bytes in batch["image_bytes"]:
            img_file = Image.open(BytesIO(img_bytes))
            prediction = self._predict(img_file).model_dump()
            embeddings.append(prediction["embedding"])
            model_versions.append(prediction["model_version"])
        batch["embedding"] = embeddings
        batch["model_version"] = model_versions
        # model_name is used by the event-detection model
        batch["model_name"] = model_versions
        del batch["image_bytes"]
        return batch

    def get_image(self, input_image: UploadFile) -> ImageFile:
        image_contents = input_image.file.read()
        return Image.open(BytesIO(image_contents))

    async def predict(self, input_image: UploadFile = File(...)) -> PredictResponse:
        loop = asyncio.get_running_loop()
        image = await loop.run_in_executor(self.predict_executor, self.get_image, input_image)
        result = await loop.run_in_executor(self.predict_executor, self._predict, image)
        self.prediction_timestamp = datetime.now()
        return result

    def _predict(self, image: ImageFile) -> PredictResponse:
        hook_output = []

        def append_avg_pool_output_hook(module: nn.Module, input: Tensor, output: Tensor) -> None:
            """There are three stages in the neck of YOLO that are fed in into the Detect module. These correspond to small, medium, and large objects. This function appends the output of the global average pool result of each stage's output to the hook_output list."""
            _logger.debug(f"{type(module)=} {input[0].shape=} {output.shape=}")
            # Average's each feature to a single value
            pooled = nn.AdaptiveAvgPool2d(1)(output).squeeze()
            _logger.debug(f"{pooled.shape=}\n")
            hook_output.append(pooled)

        # YOLOv11's model is a nested structure of nn.Modules:
        # type(self.model)=<class 'YOLO'>
        # type(self.model.model)=<class 'DetectionModel'>
        # type(self.model.model.model)=<class 'Sequential'>
        sequential_model = self.model.model.model

        # The three stages from the YOLO "neck" that are fed into the Detect module are at indices 16, 19, and 22 of the sequential model. These correspond to small, medium, and large objects.
        handle1 = sequential_model[16].register_forward_hook(append_avg_pool_output_hook)
        handle2 = sequential_model[19].register_forward_hook(append_avg_pool_output_hook)
        handle3 = sequential_model[22].register_forward_hook(append_avg_pool_output_hook)

        try:
            results: Results = self.model(
                source=image,
                conf=default_model_config["conf"],
                iou=default_model_config["iou"],
                agnostic_nms=bool(default_model_config["agnostic_nms"]),
                max_det=int(default_model_config["max_det"]),
                imgsz=640,
                device=self.detected_device,
                verbose=False,
            )
        finally:
            # hooks maintain references to tensors which can cause memory leaks
            handle1.remove()
            handle2.remove()
            handle3.remove()

        # Concatenating the pooled outputs of the three stages to create the feature embedding
        feature_embedding = concat(hook_output, dim=0)
        if feature_embedding.shape[0] != 1920:
            _logger.warning(
                f"Expected a feature embedding length of 1920, but observed {feature_embedding.shape[0]}"
            )

        assert len(results) == 1, "Expected exactly one result"

        predictions_df = results[0].to_df(normalize=True)
        prediction_objects, predictions_legacy = convert_to_consumable_format(predictions_df)

        return PredictResponse(
            prediction_objects=prediction_objects,
            model_version=self.model_info.model_version,
            embedding=feature_embedding.tolist(),
            predictions=predictions_legacy,
        )

    def livez(self) -> Response:
        """Health check endpoint that verifies recent prediction activity."""
        # Check if the last prediction was made within the past 11 seconds because the readiness probe runs every 10 second

        if datetime.now() - self.prediction_timestamp > timedelta(seconds=11):
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                content="No recent predictions made within the past 11 seconds",
            )
        else:
            return Response(status_code=HTTPStatus.OK, content="OK")

    async def ready(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        if not hasattr(self, "model") or self.model is None:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR, content="Model not loaded"
            )

        if not hasattr(self, "detected_device"):
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                content="Device not configured",
            )

        try:
            # the dummy_image_file creation is here rather than __init__ b/c after a while this type of error occurs if it's created in __init__: `cannot identify image file <_io.BytesIO object at 0x152252cf0>`. Unclear why this happens.
            # Create an in-memory binary stream
            dummy_img = BytesIO()
            # Create a new image and save it to the stream
            Image.new("RGB", (1, 1)).save(dummy_img, format="JPEG")
            # Reset the file pointer to the beginning of the stream; it's at the end after saving the image
            dummy_img.seek(0)
            # convert to UploadFile to match the expected input type for `predict`
            dummy_image_file = UploadFile(filename="dummy.jpg", file=dummy_img)
            # this will be put in the same queue as normal predict calls
            await self.predict(dummy_image_file)
            self.prediction_timestamp = datetime.now()
            return Response(status_code=HTTPStatus.OK, content="OK")
        except Exception as e:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
                content=f"Model inference failed: {str(e)}",
            )
