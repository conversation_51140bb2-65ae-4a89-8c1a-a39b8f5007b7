import os
from pathlib import Path

from google.cloud import storage  # type: ignore
from pydantic import BaseModel, ConfigDict
from serving_utils.config import get_dev_stub

from app.utils import version_config

LOCAL_MODEL_DIR = "/tmp/downloaded_models"


class ModelInfo(BaseModel):
    model_filename: Path
    model_version: str

    model_config = ConfigDict(protected_namespaces=())


# resolves the model to be used based on the environment variables and downloads the model from GCS
def resolve_model() -> ModelInfo:
    if get_dev_stub() == "1":
        return ModelInfo(
            model_filename=Path(version_config.BASE_MODEL_VERSION),
            model_version=version_config.BASE_MODEL_VERSION,
        )
    elif os.getenv("YOLO_MODEL_SIZE"):
        model_name = f"yolo11{os.getenv('YOLO_MODEL_SIZE')}.pt"
        return ModelInfo(model_filename=Path(model_name), model_version=model_name)
    else:
        local_model_file = Path(os.path.join(LOCAL_MODEL_DIR, version_config.PROD_MODEL_URI))
        download_file_from_gcs(
            version_config.PROD_BUCKET_NAME,
            version_config.PROD_MODEL_URI,
            local_model_file,
        )
        return ModelInfo(
            model_filename=local_model_file,
            model_version=version_config.PROD_MODEL_VERSION,
        )


def download_file_from_gcs(
    bucket_name: str, source_blob_name: str, destination_file_name: Path
) -> None:
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(source_blob_name)
    os.makedirs(destination_file_name.parent, exist_ok=True)
    blob.download_to_filename(destination_file_name)
