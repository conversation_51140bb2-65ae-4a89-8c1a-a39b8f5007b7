"""
© Apella Inc 2024
"""

import os
from prometheus_fastapi_instrumentator import Instrumentator

from .endpoints import create_api

app = create_api()
Instrumentator().instrument(app).expose(app)

if os.environ.get("LOCAL_DEBUG", False):
    import asyncio

    from hypercorn.asyncio import serve  # type: ignore [import-not-found]
    from hypercorn.config import Config  # type: ignore [import-not-found]

    if __name__ == "__main__":
        configuration = Config()
        config_path = "../server_config.toml"
        if os.path.exists(config_path):
            configuration = configuration.from_toml(config_path)
        asyncio.run(serve(app, configuration))
