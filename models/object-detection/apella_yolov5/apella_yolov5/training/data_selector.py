import logging
from typing import Any, cast
from enum import Enum

import pandas as pd
import pandera as pa
from google.cloud.bigquery import Client as BQClient

from apella_yolov5.configs.prod_config import DataSelectionConfig

logger = logging.getLogger(__name__)


class DSType(Enum):
    TEST = "test"
    TRAIN = "train"
    VALIDATION = "val"


class ImageLabelsSchema(pa.DataFrameModel):
    class Config:
        coerce = True
        strict = True

    anonymized_image_id: pa.typing.Index[str]
    anonymized_image_uri: pa.typing.Series[str]
    image_bin_type: pa.typing.Series[str]
    # a list of dicts, where each dict has the bounding boxes information, including a `label_name` key we can use for filtering
    object_labels: pa.typing.Series[Any]
    site_id: pa.typing.Series[str] = pa.Field(nullable=True)  # older data might not have this field
    org_id: pa.typing.Series[str]


class DataSelector:
    def __init__(self, bq_client: BQClient, config: DataSelectionConfig):
        self.bq_client = bq_client
        self.config = config

    def generate_data_for_fit(self) -> pa.typing.DataFrame[ImageLabelsSchema]:
        df = self.query_for_data(
            self.bq_client,
            self.config.labels_table_id,
            self.config.label_names_to_keep,
            self.config.limit_rows,
        )
        df.set_index("anonymized_image_id", inplace=True)

        return cast(pa.typing.DataFrame[ImageLabelsSchema], ImageLabelsSchema.validate(df))

    @classmethod
    def query_for_data(
        cls,
        bq_client: BQClient,
        table_id: str,
        label_names_to_keep: list[str],
        limit_rows: int | None = None,
    ) -> pd.DataFrame:
        query = f"""
            SELECT
                anonymized_image_id,
                anonymized_image_uri,
                image_bin_type,
                object_labels,
                site_id,
                org_id
            FROM `{table_id}`
        """
        if limit_rows:
            # Samples the dataset to get a subset of the data, but ensures that we get representatives from each image_bin_type.
            query += f" QUALIFY ROW_NUMBER() OVER(PARTITION BY image_bin_type ORDER BY frame_time_period, anonymized_image_id) <= {limit_rows} / (select count(distinct image_bin_type) FROM  `{table_id}`)"

        df = bq_client.query(query).to_dataframe(max_results=limit_rows)

        # filter and keep only requested labels
        df = cls.filter_labels(df, label_names_to_keep)

        # ensure all requested labels are present in the dataset. If num_images is not None, we skip this check because we might not have enough images to satisfy the condition
        if limit_rows is None:
            unique_labels_in_dataset = cls.unique_labels(df)
            assert (
                set(label_names_to_keep) == unique_labels_in_dataset
            ), f"Labels {set(label_names_to_keep) - unique_labels_in_dataset} not found in the dataset"

        # ensure there are no duplicate URIs
        duplicates = df["anonymized_image_uri"].duplicated()
        assert not duplicates.any(), "Duplicate URIs detected"

        # ensure the dataset is not empty
        if df.empty:
            raise ValueError("Dataset is empty")

        return df

    @staticmethod
    def unique_labels(df: pd.DataFrame) -> set[str]:
        """Extract unique labels from the `object_labels` column. The `object_labels` column is a list of dicts, where each dict has the boundxing boxes information, including a `label_name` key we can use for filtering."""
        unique_labels = set()
        for object_labels in df["object_labels"]:
            for object_label in object_labels:
                unique_labels.add(object_label["label_name"])
        return unique_labels

    @staticmethod
    def filter_labels(df: pd.DataFrame, label_names_to_keep: list[str]) -> pd.DataFrame:
        """Filter out labels that are not in label_names_to_keep. The `object_labels` column is a list of dicts, where each dict has the boundxing boxes information, including a `label_name` key we can use for filtering."""
        df["object_labels"] = df["object_labels"].apply(
            lambda object_labels: [
                object_label
                for object_label in object_labels
                if object_label["label_name"] in label_names_to_keep
            ]
        )
        return df
