import torch


def is_mps_supported() -> bool:
    """
    Check if the current machine supports MPS (Apple silicon GPU) for PyTorch.
    Returns:
        bool: True if MPS is supported, False otherwise.
    """
    # this ensures that mps is available on the current machine.
    mps_available = torch.backends.mps.is_available()
    # this ensures that the current PyTorch installation was built with MPS activated.
    mps_is_build = torch.backends.mps.is_built()
    if mps_available and mps_is_build:
        return True
    else:
        return False
