import argparse
import logging
from datetime import datetime
from importlib import import_module
import shutil
from pathlib import Path
import json

from google.cloud.bigquery import Client as BQClient
from google.cloud.secretmanager import SecretManagerServiceClient
from google.cloud.storage import Client as GCSClient
from training_utils.clearml_reporter import (
    ClearMLReporter,
    get_clearml_task,
)
from training_utils.utils import ensure_gcp_auth_is_ready, get_repo_info

from ultralytics import Y<PERSON><PERSON>

from apella_yolov5.configs.prod_config import ModelTrainingConfig
from apella_yolov5.training.yolo_dataset import copy_new_images

from apella_yolov5.model.evaluator import Evaluator
from apella_yolov5.training.data_selector import DataSelector
from apella_yolov5.training.trainer import ModelTrainer, MODEL_FILENAME

from training_utils.model_storage import ModelStorage


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DEFAULT_CONFIG_MODULE = "apella_yolov5.configs.prod_config"
MODEL_TYPE = "apella_yolo_model"


def run_pipeline(args: argparse.Namespace) -> None:
    # Load utilities
    logger.info(
        "Ensuring GCP auth is ready (if this gets stuck locally, you need run `gcloud auth login --update-adc`)"
    )
    ensure_gcp_auth_is_ready()
    bq_client = BQClient()
    gcs_client = GCSClient()

    # Load the TrainingConfig
    model_training_config: ModelTrainingConfig = import_module(args.config_module_name).config
    if args.is_automated_training:
        # set the variables for an automated fit
        fit_time = datetime.now()
        repo = get_repo_info()
        model_identifier = f"automatic_production_training/{fit_time.date()}"
        model_training_config.clearml_config.project_name = (
            f"{model_training_config.clearml_config.project_name}: Automated Fit"
        )
        model_training_config.clearml_config.task_name = f"{fit_time.date()} Production Fit"
        model_training_config.clearml_config.tags = {
            "date": str(fit_time),
            "branch_name": repo.active_branch.name,
            "sha": repo.head.object.hexsha,
            "short_sha": repo.git.rev_parse(repo.head.object.hexsha, short=7),
            "model_type": MODEL_TYPE,
            "model_identifier": model_identifier,
        }
        model_training_config.model_identifier = model_identifier

    # Load the ClearML credentials
    secret_manager_client = SecretManagerServiceClient()
    clearml_task = get_clearml_task(secret_manager_client, model_training_config.clearml_config)
    reporter = ClearMLReporter(model_training_config.model_dump(), clearml_task)

    # Load the data
    logger.info("Loading the data using the DataSelector")
    data_selector = DataSelector(bq_client, model_training_config.data_selection_config)
    data = data_selector.generate_data_for_fit()
    logger.info("Data loading complete")

    # Download missing images to the PV. This is usually done when training locally.
    if model_training_config.training_config.download_missing_images:
        logger.info("Downloading missing images to the persistent volume (PV)")
        copy_new_images(data, Path(model_training_config.training_config.pv_images_dir))

    images_path = Path(model_training_config.training_config.pv_images_dir)
    if not images_path.exists():
        raise ValueError(
            f"The persistent volume (PV) images directory {model_training_config.training_config.pv_images_dir} does not exist. Please mount the PV to the container."
        )

    # Sanity check - count the number of files in the PV. If there are none, it means we didn't mount it
    num_files = len([f for f in images_path.glob("**/*") if f.is_file()])
    logger.info(f"Number of files in the persistent volume (PV): {num_files}")
    if num_files == 0:
        raise ValueError(
            f"The persistent volume (PV) images directory {model_training_config.training_config.pv_images_dir} is empty. Please mount the PV to the container and check that it's not empty."
        )

    # Set up the model trainer
    trainer = ModelTrainer(
        model_training_config.training_config,
        data,
    )

    # Set up the model storage
    model_storage = ModelStorage(
        gcs_client,
        model_type=MODEL_TYPE,
        model_identifier=model_training_config.model_identifier,
    )
    logger.info(f"Model storage: {str(model_storage)}")

    if args.train:
        logger.info("Training model")
        # Train our model
        model_weights_path = trainer.train()
        logger.info(f"Model weights saved to {model_weights_path}")

        local_path = model_storage._get_local_data_file_location(MODEL_FILENAME)
        local_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy(model_weights_path, local_path)

        config_local_file_path = model_storage._get_local_data_file_location("config_file.json")
        json.dump(model_training_config.model_dump(), open(config_local_file_path, "w"))

        logger.info("Uploading model weights to Google Storage")
        model_storage.upload_to_google_storage()
        logger.info("Upload complete")

    if args.evaluate:
        logger.info("Evaluating model")

        # Load the saved model
        logger.info(f"Downloading model weights from Google Storage ({str(model_storage)})")

        model_storage.load_config_file()  # triggers model download to a local dir
        model_path = model_storage.find_raw_local_path(MODEL_FILENAME)
        logger.info(f"Model local path: {model_path}")
        model = YOLO(model_path)

        # Evaluate results
        evaluator = Evaluator(
            model,
            data,
            model_training_config.evaluation_config,
            model_training_config.training_config,
            reporter,
        )
        evaluator.evaluate_model()


def main() -> None:
    # Initialize logging
    parser = argparse.ArgumentParser()

    parser.add_argument("--train", help="Run training", action="store_true")
    parser.add_argument("--evaluate", help="Evaluate prior run", action="store_true")
    parser.add_argument(
        "--config-module-name",
        type=str,
        help="absolute module name, starting from PACKAGE_ROOT and using '.' as delimiter, not '/'",
        default=DEFAULT_CONFIG_MODULE,
    )
    parser.add_argument(
        "--is-automated-training",
        default=False,
        action="store_true",
        help="Run automated training. This will set the config to use the current date as the model identifier and mark the task as automated training.",
    )

    args = parser.parse_args()
    if args.train:
        logger.info("We will run the training")
    else:
        logger.info("Will not train")

    run_pipeline(args)


if __name__ == "__main__":
    main()
