import logging
from pathlib import Path

from pandera.typing import <PERSON>Frame as PanderaDataFrame
from datetime import datetime

from ultralytics import <PERSON>OL<PERSON>

from apella_yolov5.configs.prod_config import TrainingConfig
from apella_yolov5.training.yolo_dataset import create_yolo_format_dataset
from apella_yolov5.training.data_selector import ImageLabelsSchema, DSType
from apella_yolov5.training.mps_detector import is_mps_supported

logger = logging.getLogger(__name__)
MODEL_FILENAME = "best.pt"


class ModelTrainer:
    def __init__(
        self,
        training_config: TrainingConfig,
        dataset: PanderaDataFrame[ImageLabelsSchema],
    ):
        self.training_config = training_config
        self.dataset = dataset
        self.run_data_root_path = self._create_unique_folder()

    # we need to generate a unique folder for each run, using the current timestamp
    def _create_unique_folder(self):
        unique_path = Path(self.training_config.data_root) / Path(
            f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        unique_path.mkdir(parents=True, exist_ok=True)
        return unique_path

    def train(self) -> Path:
        logger.info("Preparing the dataset")
        yolo_config_file = self.generate_yolo_dataset()

        logger.info("Training the model")
        model = YOLO(self.training_config.original_model_weights)
        model.info()

        # If apple silicon GPU is supported, use it. Otherwise, use YOLO's defaults
        if is_mps_supported():
            kwargs = {
                "device": "mps",
            }
            logger.info("Training on MPS (Apple silicon GPU)")
        else:
            kwargs = {}

        opt = model.train(  # type: ignore[attr-defined]
            imgsz=self.training_config.image_size,
            epochs=self.training_config.epochs,
            data=yolo_config_file,
            batch=self.training_config.batch_size,
            project=self.run_data_root_path / "runs",
            name="train",
            **kwargs,
        )

        logger.info("Completed training")
        train_results_folder = Path(opt.save_dir)
        logger.info(f"Train results saved to {train_results_folder}")
        return train_results_folder / "weights" / MODEL_FILENAME

    def generate_yolo_dataset(self) -> Path:
        logger.info(f"Generating training YOLO dataset in {self.run_data_root_path}")
        yolo_config_file = create_yolo_format_dataset(
            self.dataset,
            Path(self.run_data_root_path),
            Path(self.training_config.pv_images_dir),
            [DSType.TRAIN, DSType.VALIDATION],  # only training and validation for the training
        )
        return yolo_config_file
