"""
Add all necessary code to generate a yolo dataset

Yo<PERSON> is very picky about where files are stored. It expects a certain directory structure. This module will help you create that structure.
"""

from typing import cast
from itertools import chain
from logging import getLogger
from pathlib import Path
from typing import Dict
import shutil
import subprocess

import pandas as pd
from pandera.typing import DataFrame as PanderaDataFrame
import yaml

from apella_yolov5.training.data_selector import DSType, ImageLabelsSchema

logger = getLogger(__name__)


def get_labels_rel_path(dataset_type: DSType) -> Path:
    """We need to create 3 folders (train/test/val) according to the value of dataset_type"""
    label_path = Path(f"labels/{dataset_type.value}")
    return label_path


def get_images_rel_path(dataset_type: DSType) -> Path:
    """We need to create 3 folders (train/test/val) according to the value of type_"""
    images_path = Path(f"images/{dataset_type.value}")
    return images_path


def write_config_file(
    data_root: Path,
    labels_to_idx: Dict[str, int],
    config_filename_prefix: str = "config",
) -> None:
    idx_to_labels = {idx: label for label, idx in labels_to_idx.items()}
    config_dict = {
        # path is the base path. Relative paths `train/val/test` are relative to `path`
        # and it has to correct in the env where we are running this. Locally, the following path will be wrong and
        # has to be edited to match local_path
        # We set up `path` like in the following because that is what `ml/yolo_training_tools/train_job.yaml` expects
        "path": f"{data_root}",
        "names": idx_to_labels,
        "train": str(get_images_rel_path(DSType("train"))),
        "val": str(get_images_rel_path(DSType("val"))),
        "test": str(get_images_rel_path(DSType("test"))),
    }

    config_file = data_root / f"{config_filename_prefix}.yaml"
    with open(config_file, "w") as outf:
        yaml.dump(config_dict, outf)


def get_label_to_idx_dict(
    dataset_with_pv_locations: pd.DataFrame,
) -> Dict[str, int]:
    """
    Extract the unique names of the classes (objects) seen in this dataset represented by `query_df`
    Each row in `query_df` represents an image that might have any number of labels (0 or more)
    The `object_labels` col has a list of dictionaries representing the objects seen. Each dictionary has a 'label_name' key
    """
    # query_df.labels is a pd.Series, each value in it, is a list of dictionaries. Just concat all the lists
    # into a big iterable
    label_dicts = chain.from_iterable(dataset_with_pv_locations["object_labels"])
    labels = set(map(lambda d: str(d["label_name"]), label_dicts))
    return {label: idx for idx, label in enumerate(sorted(labels))}


def write_label_file(row: pd.Series, output_path: Path, labels_to_idx: Dict[str, int]) -> bool:
    """
    Write the content of a row from query_df into a single data file.
    row has columns: ['anonymized_image_uri', 'object_labels',]
    """
    filename = row["anonymized_image_uri"].rsplit("/", 1)[1]
    filename = filename.replace(".jpg", ".txt")
    image_df = pd.DataFrame(row["object_labels"])
    if image_df.empty:
        # if there are no labels, there is no need to output a `label` file
        return False
    image_df["label_idx"] = image_df["label_name"].apply(lambda x: labels_to_idx[x])
    image_df["x_center"] = (image_df.x1 + image_df.x2) / 2
    image_df["y_center"] = (image_df.y1 + image_df.y2) / 2
    image_df["width"] = image_df.x2 - image_df.x1
    image_df["height"] = image_df.y2 - image_df.y1
    image_df.drop_duplicates(inplace=True)
    full_label_path = output_path / filename
    with open(full_label_path, "w") as fid:
        fid.write(
            image_df.to_string(
                columns=["label_idx", "x_center", "y_center", "width", "height"],
                index=False,
                header=False,
            )
        )
    return True


def write_label_files(
    dataset_with_pv_locations: pd.DataFrame,
    dataset_type: DSType,
    data_root: Path,
    labels_to_idx: Dict[str, int],
) -> None:
    output_path = data_root / get_labels_rel_path(dataset_type)
    output_path.mkdir(exist_ok=True, parents=True)
    dataset_with_pv_locations.apply(
        lambda row: write_label_file(row, output_path, labels_to_idx), axis=1
    )
    num_generated_files = len(list(output_path.glob("*.txt")))
    # background files (those with no objects) generate no labels file
    assert num_generated_files <= len(dataset_with_pv_locations)


def create_images_symbolic_links(
    data_root: Path,
    dataset_with_pv_locations: pd.DataFrame,
    dataset_type: DSType,
) -> None:
    """
    Create symbolic links that points from the data dir (where images are organized in the structure required by YOLO) to the actual images we keep in the persistent volume.
    """
    dataset_path = data_root / get_images_rel_path(
        dataset_type
    )  # e.g /data/model_name/images/train
    dataset_path.mkdir(exist_ok=True, parents=True)

    for pv_path in dataset_with_pv_locations["pv_path"]:
        symlink_path: Path = dataset_path / pv_path.name
        if symlink_path.exists():
            continue
        symlink_path.symlink_to(pv_path)


def split_and_save_dataset(
    dataset_with_pv_locations: pd.DataFrame,
    dataset_type: DSType,
    data_root: Path,
    labels_to_idx: Dict[str, int],
) -> int:
    filtered_df = dataset_with_pv_locations[
        dataset_with_pv_locations["image_bin_type"] == dataset_type.name
    ]
    logger.info("split_and_save_dataset -- writing label files")
    write_label_files(filtered_df, dataset_type, data_root, labels_to_idx)
    logger.info(f"split_and_save_dataset -- creating {len(filtered_df)} images symbolic links")
    create_images_symbolic_links(data_root, filtered_df, dataset_type)
    return len(filtered_df)


def generate_pv_locations(
    image_labels: PanderaDataFrame[ImageLabelsSchema], pv_images_path: Path
) -> pd.DataFrame:
    """
    Add a column to the dataset that has the full path to the image in the persistent volume.
    """
    dataset_with_pv_locations = image_labels.copy()
    dataset_with_pv_locations["file_name"] = dataset_with_pv_locations[
        "anonymized_image_uri"
    ].apply(lambda x: x.rsplit("/")[-1])
    dataset_with_pv_locations["pv_path"] = dataset_with_pv_locations.apply(
        lambda row: pv_images_path / row["file_name"], axis=1
    )
    return dataset_with_pv_locations


def create_yolo_format_dataset(
    image_labels: PanderaDataFrame[ImageLabelsSchema],
    local_data_location: Path,
    pv_images_path: Path,
    dataset_types: list[DSType],
    config_filename_prefix: str = "config",
) -> Path:
    """
    Returns the main yolo config file path
    """
    if image_labels.empty:
        raise ValueError("Dataset is empty")
    else:
        logger.info(f"Dataset size: {len(image_labels)}")

    dataset_with_pv_locations = generate_pv_locations(image_labels, pv_images_path)

    logger.info("Writing config file")
    labels_to_idx = get_label_to_idx_dict(dataset_with_pv_locations)
    write_config_file(local_data_location, labels_to_idx, config_filename_prefix)

    for dataset_type in DSType:
        if dataset_type in dataset_types:
            logger.info(f"Writing {dataset_type} data set")
            split_and_save_dataset(
                dataset_with_pv_locations,
                dataset_type,
                local_data_location,
                labels_to_idx,
            )
        else:
            # Yolo evaluation expects the images dir to exist, even if it's not using it
            logger.info(f"Creating empty images dir for data set: {dataset_type}")
            (local_data_location / get_images_rel_path(dataset_type)).mkdir(
                exist_ok=True, parents=True
            )
    return local_data_location / f"{config_filename_prefix}.yaml"


def _filter_out_existing_images(
    dataset_with_pv_locations: pd.DataFrame,
    pv_images_path: Path,
) -> list[str]:
    """Filter out images that already exist in the persistent volume"""
    existing_images = set(pv_images_path.glob("*"))
    existing_images_filenames = {image.name for image in existing_images}
    only_new_images = dataset_with_pv_locations[
        ~dataset_with_pv_locations["file_name"].isin(existing_images_filenames)
    ]
    result = only_new_images["anonymized_image_uri"].astype(str).to_list()
    return cast(list[str], result)


def _copy_uris(
    src_uris: list[str],
    dst_uris_path: Path,
    start_fresh: bool = False,
    skip_existing: bool = False,
    timeout_sec: int | None = None,
) -> None:
    """
    copy all the uris from google storage to a local folder. Uses the gsutil command line tool since it's the fastest known way to do this.

    Args:
        src_uris: list of uris to copy
        dst_uris_path: the path to a local folder (will be created if it doesn't exist) to download the resources
        dst_temp_txt_path: path to a local file to store the list of resources to download
        start_fresh: if set, deletes contents of dst_uris_path at start
        skip_existing: if set, skips copying files that already exist in dst_uris_path
        logger [Optional]: The logger to use for writing out messages
        timeout_sec [Optional]: The timeout in seconds for the copy operation. If not provided, it will wait indefinitely for the copy to complete.
    Returns:
        None, 2 artifacts are created, the folder with all the resources and the file listing the resources
    Raises:
        subprocess.TimeoutExpired if the copy operation times out

    """

    # NOTE: We could probably implement this function in terms of download_blobs_from_bucket

    if start_fresh and dst_uris_path.is_dir():
        shutil.rmtree(dst_uris_path)

    dst_uris_path.mkdir(exist_ok=True, parents=True)
    if len(src_uris) == 0:
        return

    file_list = "\n".join(src_uris)
    cmd = ["gsutil", "-m", "cp", "-I"]
    if skip_existing:
        cmd += ["-n"]
    cmd += [str(dst_uris_path)]
    result = subprocess.run(
        cmd, input=file_list, capture_output=True, text=True, timeout=timeout_sec
    )

    exit_code = result.returncode
    if exit_code:
        logger.error(cmd)
        logger.error(f"Output: {result.stdout}")
        logger.error(f"Error: {result.stderr}")
        raise RuntimeError("Failed to copy some or all images")


def copy_new_images(
    image_labels: PanderaDataFrame[ImageLabelsSchema],
    pv_images_path: Path,
) -> None:
    """Copies missing images from GCS to the persistent volume images dir."""
    logger.info(f"Total number of images requested: {len(image_labels)}")
    dataset_with_pv_locations = generate_pv_locations(image_labels, pv_images_path)

    # filter out images we already have
    new_uris = _filter_out_existing_images(dataset_with_pv_locations, pv_images_path)

    logger.info(f"Copying {len(new_uris)} new images to {pv_images_path}")
    _copy_uris(new_uris, pv_images_path)
