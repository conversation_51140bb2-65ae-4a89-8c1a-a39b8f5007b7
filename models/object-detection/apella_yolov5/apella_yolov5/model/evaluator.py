from pathlib import Path
from datetime import datetime
import logging

from pandera.typing import <PERSON><PERSON>ram<PERSON> as PanderaDataFrame
from ultralytics import <PERSON>OL<PERSON>
from training_utils.clearml_reporter import ClearMLReporter

from apella_yolov5.configs.prod_config import EvaluationConfig, TrainingConfig
from apella_yolov5.training.data_selector import ImageLabelsSchema, DSType
from apella_yolov5.training.yolo_dataset import create_yolo_format_dataset
from apella_yolov5.training.mps_detector import is_mps_supported

logger = logging.getLogger(__name__)


class Evaluator:
    def __init__(
        self,
        model: YOLO,
        dataset: PanderaDataFrame[ImageLabelsSchema],
        evaluation_config: EvaluationConfig,
        training_config: TrainingConfig,
        reporter: ClearMLReporter,
    ):
        self.model = model
        self.dataset = dataset
        self.evaluation_config = evaluation_config
        self.training_config = training_config
        self.reporter = reporter
        self.run_data_root_path = self._create_unique_folder()

    # we need to generate a unique folder for each run, using the current timestamp
    def _create_unique_folder(self):
        unique_path = Path(self.training_config.data_root) / Path(
            f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        unique_path.mkdir(parents=True, exist_ok=True)
        return unique_path

    def evaluate_model(self) -> None:
        logger.info("Preparing the dataset")
        yolo_config_file = self.generate_yolo_dataset()

        # If apple silicon GPU is supported, use it. Otherwise, use YOLO's defaults
        if is_mps_supported():
            kwargs = {
                "device": "mps",
            }
            logger.info("Training on MPS (Apple silicon GPU)")
        else:
            kwargs = {}

        logger.info("Evaluating the model")
        val_results = self.model.val(  # type: ignore[attr-defined]
            split="test",
            project=self.run_data_root_path / "runs",
            name="test",
            imgsz=self.evaluation_config.image_size,
            data=yolo_config_file,
            **kwargs,
        )

        eval_results_folder = Path(val_results.save_dir)
        logger.info(f"Evaluation results saved to {eval_results_folder}")

        logger.info("Reporting evaluation results to ClearML")
        self._report_evaluation_results(val_results)

        logger.info("Writing evaluation plots to ClearML")
        self._report_evaluation_plots(val_results)

    def _report_evaluation_results(self, val_results) -> None:
        """Writes evaluation summary to ClearML as new values (with the `test` suffix)."""
        for k, v in val_results.results_dict.items():
            new_key = f"{k} (test)"
            self.reporter.clearml_task.get_logger().report_single_value(new_key, v)

    def _report_evaluation_plots(self, val_results) -> None:
        """Reports evaluation plots to ClearML."""
        files = [
            "results.png",
            "confusion_matrix.png",
            "confusion_matrix_normalized.png",
            *(f"{x}_curve.png" for x in ("F1", "PR", "P", "R")),
        ]
        file_paths = [
            (val_results.save_dir / f) for f in files if (val_results.save_dir / f).exists()
        ]
        for f in file_paths:
            self.reporter.log_image_as_plot(title=f"{f.stem} (test)", plot_path=f)

    def generate_yolo_dataset(self) -> Path:
        logger.info(f"Generating evaluation YOLO dataset in {self.run_data_root_path}")
        yolo_config_file = create_yolo_format_dataset(
            self.dataset,
            Path(self.run_data_root_path),
            Path(self.training_config.pv_images_dir),
            [DSType.TEST],  # only validation for the evaluation
        )
        return yolo_config_file
