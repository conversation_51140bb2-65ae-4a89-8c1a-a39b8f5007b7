from apella_yolov5.configs.prod_config import config

config = config.model_copy()

# A config for downloading images to the persistent volume. This configuration should only be used by a job that downloads the images but doesn't train the model.

# Download missing images to the persistent volume
config.training_config.download_missing_images = True

# Set a project and task name
config.clearml_config.project_name = "YOLO Training: Automated Images Download"
config.clearml_config.task_name = "Automated Images Download"

# Set the model identifier to the experiment name. We don't expect to train a model (we are using this config for a job that only downloads the images), so we don't care about the identifier
config.model_identifier = "yolo_model_none"
