from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel

# The size of the images to use. <PERSON><PERSON> will resize the images to this size. Normally, this should be the same for training, evaluation, and inference.
DEFAULT_IMAGE_SIZE = 640


class YoloModelConfig(BaseModel):
    # YOLO model configuration

    # The size of the images to use for inference.
    image_size: int = DEFAULT_IMAGE_SIZE


class TrainingConfig(BaseModel):
    # Training configuration. This is the part of the configuration that is specific to training, and should not be used for inference
    epochs: int = 30
    batch_size: int = 16
    original_model_weights: str = "yolo11x.pt"

    # When training on the cloud, we mount a Persistent Volume (PV) to the container. This PV should already contain all the images we labeled, so we can just create symbolic links to them.
    # This is the path to the images in the PV
    pv_images_dir: str = "/images_pv_mount/images"

    # This is the path where we generate the yolo dataset, run traning, and save the model
    data_root: str = "/data"

    # If set to True, the training pipeline will download missing images to the pv_images_dir. If you are using a persistent volume that can only be mounted for writing by a single node, you'll want to set this to False for the training jobs and set a different job to download the images
    download_missing_images: bool = True

    # The size of the images to use for training.
    image_size: int = DEFAULT_IMAGE_SIZE


class DataSelectionConfig(BaseModel):
    # Data selection configuration. This is the part of the configuration that is specific to selecting the data for training

    # The BigQuery table that contains the labels
    labels_table_id: str = "prod-data-platform-027529.gold.encord_object_model_labels_latest"

    # The number of rows to limit the dataset to. This is useful for testing the training pipeline on a small dataset
    limit_rows: int | None = None

    # A list of labels we want to train the model with. The original dataset may contain many labels, but we may want to train the model only on a subset of them
    label_names_to_keep: list[str] = [
        "back_table_open",
        "or_table_free",
        "or_table_occupied",
        "patient_draped",
        "unscrubbed",
        "scrubbed",
        "bed_free",
        "bed_occupied",
        "mop",
        "mop_head",
        "mop_bucket",
        "endo_pack_open",
    ]


class EvaluationConfig(BaseModel):
    # Evaluation configuration. This is the part of the configuration that is specific to evaluating the model

    # The size of the images to use for evaluation.
    image_size: int = DEFAULT_IMAGE_SIZE


class ModelTrainingConfig(BaseModel):
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLBaseModel = ClearMLBaseModel(
        project_name="YOLO Training",
        task_name="Production Fit",
        tags={"version": "0.1.0"},
        # Dataset preparation can take a while, so we allow for 30 minutes to pass before ClearML reverts to reporting time from start of the task instead of reporting iterations
        wait_for_first_iteration_to_start_sec=1800,
    )
    evaluation_config: EvaluationConfig = EvaluationConfig()
    model_identifier: str = "yolo_model"
    yolo_model_config: YoloModelConfig = YoloModelConfig()


config = ModelTrainingConfig()
