[tool.poetry]
name = "apella_yolo"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
# This is a service repo
package-mode = false

[tool.poetry.dependencies]
python = "~3.10"
fastapi = { extras = ["standard"], version = "^0.115.0" }
pandas = "^2.1.1"
# Locking to 8.3.74. Some newer versions raise Unexpected number of layers for YoloV11 model error
# To solve in ticket CV-144
ultralytics = "8.3.74"
google-cloud-storage = "^2.17.0"
asyncio = "^3.4.3"
prometheus-fastapi-instrumentator = "^7.0.0"

debugpy = "^1.8.11"
google-cloud-bigquery = "^3.27.0"
google-cloud-bigquery-storage = "^2.27.0"

ray = { version = "^2.40.0", extras = ["default", "tune"] }

[tool.poetry.group.serving.dependencies]
serving-utils = { path = "../../../serving_utils", develop = true }

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.2"
pytest-asyncio = "^0.24.0"
mypy = "^1.11.1"
ruff = "^0.6.1"
coverage = "^7.6.1"
pydantic = "^2.8.2"
pandas-stubs = "^2.2.3.241009"
types-pyyaml = "^6.0.12.20241230"

[tool.poetry.group.locust.dependencies]
python = "~3.10"
locust = "^2.29.0"
pillow = "^10.3.0"


[tool.poetry.group.training.dependencies]
google-cloud-bigquery = "^3.30.0"
google-cloud-secret-manager = "^2.23.2"
db-dtypes = "^1.4.2"
pandera = "^0.23.1"
training-utils = { path = "../../../training_utils", develop = true }

[tool.coverage.report]
fail_under = 80

[tool.ruff]
line-length = 100

[tool.pytest.ini_options]
asyncio_mode = "auto"

[[tool.mypy.overrides]]
module = ["google.cloud.*", "pyarrow.*", "ultralytics.*"]
ignore_missing_imports = true

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
