import argparse
from pathlib import Path
import shutil
import yaml
import os
import string
import subprocess
from dataclasses import dataclass

SKELETON_FILE_DIRECTORY = Path("inference_project_creation_skeleton")
PYPROJECT_TOML_SKELETON_FILE = "pyproject.toml.skeleton"


@dataclass
class InferenceProjectCreator:
    project_name: str
    parent_dir: str | None

    def __post_init__(self) -> None:
        self.training_utils_dir = Path("../../training_utils")
        self.inference_utils_dir = Path("../../inference_utils")        
        self.outdir = Path(self.project_name)
        self.skeleton_dir = SKELETON_FILE_DIRECTORY
        if self.parent_dir is not None:
            self.outdir = self.parent_dir / self.outdir
            self.training_utils_dir = ".." / self.training_utils_dir
            self.inference_utils_dir = ".." / self.inference_utils_dir            
            
    def create_full_project(self):
        self._create_parent_directory_if_needed()
        self.create_poetry_project()
        self.copy_remaining_files_over()
                
    def create_poetry_project(self) -> None:
        # Because poetry doesn't let you create a new project and specify a python version,
        #  we will create a new project, and replace the pyproject.toml file
        cwd = os.getcwd()
        if self.parent_dir: 
            os.chdir(self.parent_dir)
        subprocess.run(["poetry", "new", self.project_name], check=True)
        if self.parent_dir:
            os.chdir("..")
        self.copy_skeleton_file_over(self.skeleton_dir / PYPROJECT_TOML_SKELETON_FILE, self.outdir / "pyproject.toml")
        os.chdir(self.outdir)
        subprocess.run(["poetry", "update"], check=True)
        os.chdir(cwd)
        

        # os.rename("temp_file.yml", self.outdir / "async_training_config.yml")

    def copy_skeleton_file_over(self, input_file: str, output_file: str) -> None:
        # replace the relevant values
        contents = string.Template(Path(input_file).read_text()).substitute(
            **{
                "project_name": self.project_name,
                "training_utils_path": str(self.training_utils_dir),
                "inference_utils_path": str(self.inference_utils_dir)
            })
        Path(output_file).write_text(contents)

    def _create_parent_directory_if_needed(self) -> None:
        # create directory and cd into it
        if self.parent_dir is not None:
            Path(self.parent_dir).mkdir(parents=True, exist_ok=True)

    def copy_remaining_files_over(self) -> None:
        inference_files_to_port = [file for file in os.listdir(self.skeleton_dir / "inference_dir_skeleton") if file.endswith(".skeleton.py")]
        target_inference_dir = Path(self.outdir) / self.project_name
        target_inference_dir.mkdir(parents=True, exist_ok=True)
        for file_to_copy in inference_files_to_port:
            file_loc = self.skeleton_dir / "inference_dir_skeleton" / file_to_copy
            if file_to_copy.endswith("skeleton.py"):
                shutil.copyfile(file_loc, target_inference_dir / file_to_copy.replace(".skeleton.py", ".py"))
            elif file_to_copy.endswith("skeleton.yml"):
                shutil.copyfile(file_loc, target_inference_dir / file_to_copy.replace(".skeleton.yml", ".yml"))
            else:
                shutil.copyfile(file_loc, target_inference_dir / file_to_copy)


def get_project_name() -> str:
    print("What is your project name?")
    project_name = input("input project_name: ")
    print(f"Recorded project name as {project_name}")
    return project_name


def get_parent_dir() -> str | None:
    print("Does this project belong in a parent directory? (i.e., forecasting) [y/n]")
    parent_dir = "undefined"
    while parent_dir == "undefined":
        answer = input(">>")
        if answer == "y":
            print("What is the parent directory name?")
            parent_dir = input("input parent directory: ")
            print(f"Recorded parent directory as {parent_dir}")
        elif answer == "n":
            print("No parent directory needed.")
            parent_dir = None
        else:
            print("please enter y or n")

    return parent_dir


def get_gpu_status() -> str | None:
    print("Do you need a GPU for training? [y/n]")
    gpu_val = input("[y/n]: ")
    gpu_type = None
    if gpu_val == "y":
        gpu_type = "nvidia-tesla-v100"
        print(f"Setting GPU requirement to {is_gpu}")
    return gpu_type


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--parent-dir",
        help="Directory of project",
    )
    parser.add_argument(
        "--project-name",
        help="Name of the project",
    )    
    parser.add_argument(
        "--no-parent-dir",
        dest='no_parent_dir', action='store_true')
    args = parser.parse_args()

    # If you don't pass it as command line, go through interactive menu
    if args.parent_dir is None and not args.no_parent_dir:
        parent_dir = get_parent_dir()
    else:
        parent_dir = None

    if args.project_name is None:
        project_name = get_project_name()
    else:
        project_name = args.project_name

    project_creator = InferenceProjectCreator(project_name, parent_dir)

    project_creator.create_full_project()
