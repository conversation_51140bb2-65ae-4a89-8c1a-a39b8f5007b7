[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
strict = true
explicit_package_bases = true  # Resolve error `app/endpoints/__init__.py: error: Source file found twice under different module names: "endpoints" and "app.endpoints"`

[[tool.mypy.overrides]]
module = [
    "google.*",
    "sklearn.*",
    "joblib",
    "pandasql.*"
]
ignore_missing_imports = true

[tool.poetry]
name = "CaseSlotRanker"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "case_slot_ranker"}]

[tool.poetry.dependencies]
python = "~3.10"
pydantic = "^2.5.2"
google-cloud-storage = "^2.12.0"
numpy = "^1.26.4"  # Prevents numpy from being upgraded to 2.0.0
hypercorn = "^0.15.0"
types-pytz = "^2023.3.1.1"
httpx = "^0.25.2"
cloudpickle = "^3.0.0"
dill = "^0.3.8"
tenacity = "^8.2.3"
pandera = "^0.23.1"
feature-store = { path="../../../feature_store", develop = true }
category-encoders = "^2.6.4"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.0"
pytest = "^7.4.2"
mypy = "^1.6.0"
pytest-cov = "^4.1.0"
dagster-graphql = "^1.4.7"
gitpython = "^3.1.40"
pandas-stubs = "^2.1.4.231227"

[tool.poetry.group.training.dependencies]
db-dtypes = "^1.1.1"  # For BigQuery to pandas
google-cloud-bigquery = "^3.12.0"
scikit-learn = "^1.3.2"
pandas = "^2.1.1"
pandasql = "^0.7.3"
sqlalchemy = "1.4.46"
google-cloud-secret-manager = "^2.16.4"
matplotlib = "^3.8.0"
training-utils = {path = "../../../training_utils", develop = true}

[tool.poetry.group.serving.dependencies]
types-cachetools = "^5.3.0.7"
serving_utils = {path = "../../../serving_utils", develop = true }

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
   # Trust ruff format to get line length right. Without this, there are cases where ruff won't
   # reflow a line that's too long (e.g. comments) and ruff check complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
