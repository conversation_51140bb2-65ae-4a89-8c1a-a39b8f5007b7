from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, model_validator
from serving_utils.utils import VersionedModel
from typing_extensions import Self


class Category(str, Enum):
    CASE_DURATION = "case_duration"
    IMMEDIACY = "immediacy"
    SUITABILITY = "suitability"


class Procedure(BaseModel):
    procedure_name: str = Field(description="The name of the procedure")


class Surgeon(BaseModel):
    surgeon_name: str = Field(description="The name of the surgeon")


class Site(BaseModel):
    site_id: str = Field(description="The ID of the site")


class Organization(BaseModel):
    org_id: str = Field(description="The ID of the organization")


class StandaloneInput(BaseModel):
    primary_procedure: Procedure = Field(description="The primary procedure for the case")
    primary_surgeon: Surgeon = Field(description="The primary surgeon for the case")
    additional_procedures: list[Procedure] = Field(
        default_factory=list, description="List of additional procedures for the case"
    )
    site: Site = Field(description="The site where the case is scheduled")
    organization: Organization = Field(description="The organization responsible for the case")


class StandaloneAPIRequest(BaseModel):
    standalone_input: StandaloneInput


class SlotCategory(BaseModel):
    category_name: Category = Field(description="The name of the category")
    category_score: float = Field(description="The actual score of the slot for the category")
    category_rank: int = Field(description="The indexed rank of the slot in this category")
    category_is_golflike: bool = Field(description="Whether a lower score is a better score")


class Room(BaseModel):
    room_id: str = Field(description="The ID of the room")


class SlotStart(BaseModel):
    start_time: datetime = Field(description="The start time of the slot")


class SlotEnd(BaseModel):
    end_time: datetime = Field(description="The end time of the slot")


class ForecastCaseDuration(BaseModel):
    forecast_case_duration_minutes: float = Field(
        description="The forecasted case duration in minutes"
    )
    service_version: str = Field(description="The version of the service used for forecasting")
    model_version: str = Field(description="The version of the model used for forecasting")
    version: str = Field(description="The version of the forecast")


class CaseSlot(BaseModel):
    room: Room = Field(description="The room where the slot is located")
    slot_start: SlotStart = Field(description="Information about the start of the slot")
    slot_end: SlotEnd = Field(description="Information about the end of the slot")
    forecast_case_duration: ForecastCaseDuration = Field(
        description="The forecasted case duration for the slot"
    )
    slot_categories: list[SlotCategory] = Field(
        default_factory=list, description="List of categories for the slot"
    )

    def get_slot_rank(self, category: Category) -> int:
        for slot_category in self.slot_categories:
            if slot_category.category_name == category:
                return slot_category.category_rank
        raise ValueError(f"Category {category} not found in slot categories.")

    @model_validator(mode="after")
    def validate_slot_times(self) -> Self:
        if self.slot_start.start_time >= self.slot_end.end_time:
            raise ValueError("Slot start time must be before slot end time.")
        return self


class RankedCategory(BaseModel):
    category_name: Category = Field(description="The name of the category for ranking")
    slots: list[CaseSlot] = Field(
        default_factory=list, description="List of ranked for the category"
    )

    @model_validator(mode="after")
    def validate_slots(self) -> Self:
        self.slots.sort(key=lambda slot: slot.get_slot_rank(self.category_name))
        return self


class RankedCaseSlotOutput(VersionedModel):
    top_scorers: list[RankedCategory] = Field(default_factory=list)
