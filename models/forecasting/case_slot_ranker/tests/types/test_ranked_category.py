from datetime import datetime, timezone

from utils.types import (
    CaseSlot,
    Category,
    ForecastCaseDuration,
    RankedCategory,
    Room,
    SlotCategory,
    SlotEnd,
    SlotStart,
)


class TestRankedCategory:
    def test_validate_slots(self) -> None:
        """
        Test that RankedCategory.validate_slots correctly sorts slots based on category_rank.
        """
        # Create slots with unordered category_rank for the SUITABILITY category
        slot1 = CaseSlot(
            room=Room(room_id="room_1"),
            slot_start=SlotStart(start_time=datetime(2023, 10, 10, 12, 0, tzinfo=timezone.utc)),
            slot_end=SlotEnd(end_time=datetime(2023, 10, 10, 13, 0, tzinfo=timezone.utc)),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=60,
                service_version="1.0",
                model_version="1.0",
                version="1.0",
            ),
            slot_categories=[
                SlotCategory(
                    category_name=Category.SUITABILITY,
                    category_score=0.8,
                    category_rank=2,  # Higher rank
                    category_is_golflike=False,
                ),
                SlotCategory(
                    category_name=Category.IMMEDIACY,
                    category_score=60,
                    category_rank=1,
                    category_is_golflike=True,
                ),
            ],
        )
        slot2 = CaseSlot(
            room=Room(room_id="room_2"),
            slot_start=SlotStart(start_time=datetime(2023, 10, 10, 14, 0, tzinfo=timezone.utc)),
            slot_end=SlotEnd(end_time=datetime(2023, 10, 10, 15, 0, tzinfo=timezone.utc)),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=120,
                service_version="1.0",
                model_version="1.0",
                version="1.0",
            ),
            slot_categories=[
                SlotCategory(
                    category_name=Category.SUITABILITY,
                    category_score=1.0,
                    category_rank=1,  # Lower rank
                    category_is_golflike=False,
                ),
                SlotCategory(
                    category_name=Category.IMMEDIACY,
                    category_score=180,
                    category_rank=2,
                    category_is_golflike=True,
                ),
            ],
        )

        # Instantiate RankedCategory with slots in unordered rank
        ranked_category = RankedCategory(
            category_name=Category.SUITABILITY,
            slots=[slot1, slot2],
        )

        # After validation, slots should be sorted by category_rank in ascending order
        assert ranked_category.slots[0].room.room_id == "room_2"
        assert ranked_category.slots[1].room.room_id == "room_1"
