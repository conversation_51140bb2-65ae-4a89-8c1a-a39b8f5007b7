from datetime import datetime, timedelta, timezone

import pytest

from utils.types import CaseSlot, ForecastCaseDuration, Room, SlotEnd, SlotStart


class TestCaseSlot:
    def test_validate_slot_times_valid(self) -> None:
        """
        Test that a CaseSlot with valid slot_start and slot_end times is created successfully.
        """
        start_time = datetime(2023, 10, 10, 12, 0, tzinfo=timezone.utc)
        end_time = start_time + timedelta(hours=1)
        case_slot = CaseSlot(
            room=Room(room_id="room_1"),
            slot_start=SlotStart(start_time=start_time),
            slot_end=SlotEnd(end_time=end_time),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=60,
                service_version="1.0",
                model_version="1.0",
                version="1.0",
            ),
            slot_categories=[],
        )
        assert case_slot.slot_start.start_time < case_slot.slot_end.end_time

    def test_validate_slot_times_invalid(self) -> None:
        """
        Test that a CaseSlot with slot_start time after slot_end time raises a ValueError.
        """
        start_time = datetime(2023, 10, 10, 12, 0, tzinfo=timezone.utc)
        end_time = start_time - timedelta(hours=1)
        with pytest.raises(ValueError, match="Slot start time must be before slot end time."):
            CaseSlot(
                room=Room(room_id="room_1"),
                slot_start=SlotStart(start_time=start_time),
                slot_end=SlotEnd(end_time=end_time),
                forecast_case_duration=ForecastCaseDuration(
                    forecast_case_duration_minutes=60,
                    service_version="1.0",
                    model_version="1.0",
                    version="1.0",
                ),
                slot_categories=[],
            )
