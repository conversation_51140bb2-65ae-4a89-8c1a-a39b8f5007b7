import datetime
from typing import Final
from unittest.mock import MagicMock, patch

from app.endpoints.ranking.ranking_api import RankingService
from utils.types import (
    CaseSlot,
    Category,
    ForecastCaseDuration,
    Organization,
    Procedure,
    RankedCaseSlotOutput,
    RankedCategory,
    Room,
    Site,
    SlotCategory,
    SlotEnd,
    SlotStart,
    StandaloneAPIRequest,
    StandaloneInput,
    Surgeon,
)

FIXED_NOW: Final = datetime.datetime(2023, 10, 10, 12, 0, 0, tzinfo=datetime.timezone.utc)


@patch("serving_utils.config.get_dev_stub", return_value="1")
@patch("app.endpoints.ranking.ranking_api.datetime.datetime", autospec=True)
def test_rank_standalone_dev_stub(mock_datetime: MagicMock, mock_get_dev_stub: MagicMock) -> None:
    # Set a fixed current datetime
    mock_datetime.now.return_value = FIXED_NOW

    # Initialize the RankingService
    service = RankingService()

    # Create a dummy APIRequest (populate fields as necessary)
    request = StandaloneAPIRequest(
        standalone_input=StandaloneInput(
            primary_procedure=Procedure(
                procedure_name="procedure_1",
            ),
            primary_surgeon=<PERSON><PERSON>(
                surgeon_name="surgeon_1",
            ),
            additional_procedures=[Procedure(procedure_name="Procedure B")],
            site=Site(site_id="site_1"),
            organization=Organization(org_id="org_1"),
        )
    )

    case_slots = [
        CaseSlot(
            room=Room(room_id="room_1"),
            slot_start=SlotStart(start_time=FIXED_NOW + datetime.timedelta(hours=1)),
            slot_end=SlotEnd(end_time=FIXED_NOW + datetime.timedelta(hours=2)),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=60,
                service_version=service.version_information["service_version"],
                model_version=service.version_information["model_version"],
                version=service.version_information["version"],
            ),
            slot_categories=[
                SlotCategory(
                    category_name=Category.IMMEDIACY,
                    category_score=60,
                    category_rank=1,
                    category_is_golflike=True,
                ),
                SlotCategory(
                    category_name=Category.SUITABILITY,
                    category_score=0.8,
                    category_rank=3,
                    category_is_golflike=False,
                ),
                SlotCategory(
                    category_name=Category.CASE_DURATION,
                    category_score=60,
                    category_rank=2,
                    category_is_golflike=True,
                ),
            ],
        ),
        CaseSlot(
            room=Room(room_id="room_2"),
            slot_start=SlotStart(start_time=FIXED_NOW + datetime.timedelta(hours=3)),
            slot_end=SlotEnd(end_time=FIXED_NOW + datetime.timedelta(hours=4)),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=120,
                service_version=service.naive_version_information["service_version"],
                model_version=service.naive_version_information["model_version"],
                version=service.naive_version_information["version"],
            ),
            slot_categories=[
                SlotCategory(
                    category_name=Category.IMMEDIACY,
                    category_score=180,
                    category_rank=2,
                    category_is_golflike=True,
                ),
                SlotCategory(
                    category_name=Category.SUITABILITY,
                    category_score=1.0,
                    category_rank=1,
                    category_is_golflike=False,
                ),
                SlotCategory(
                    category_name=Category.CASE_DURATION,
                    category_score=120,
                    category_rank=3,
                    category_is_golflike=True,
                ),
            ],
        ),
        CaseSlot(
            room=Room(room_id="room_3"),
            slot_start=SlotStart(start_time=FIXED_NOW + datetime.timedelta(hours=5)),
            slot_end=SlotEnd(end_time=FIXED_NOW + datetime.timedelta(hours=5, minutes=30)),
            forecast_case_duration=ForecastCaseDuration(
                forecast_case_duration_minutes=30,
                service_version=service.version_information["service_version"],
                model_version=service.version_information["model_version"],
                version=service.version_information["version"],
            ),
            slot_categories=[
                SlotCategory(
                    category_name=Category.IMMEDIACY,
                    category_score=300,
                    category_rank=3,
                    category_is_golflike=True,
                ),
                SlotCategory(
                    category_name=Category.SUITABILITY,
                    category_score=0.9,
                    category_rank=2,
                    category_is_golflike=False,
                ),
                SlotCategory(
                    category_name=Category.CASE_DURATION,
                    category_score=30,
                    category_rank=1,
                    category_is_golflike=True,
                ),
            ],
        ),
    ]
    # Define the expected RankedCaseSlotOutput.datetime
    expected_output = RankedCaseSlotOutput(
        top_scorers=[
            RankedCategory(
                category_name=Category.IMMEDIACY,
                slots=sorted(case_slots, key=lambda slot: slot.get_slot_rank(Category.IMMEDIACY)),
            ),
            RankedCategory(
                category_name=Category.SUITABILITY,
                slots=sorted(case_slots, key=lambda slot: slot.get_slot_rank(Category.SUITABILITY)),
            ),
            RankedCategory(
                category_name=Category.CASE_DURATION,
                slots=sorted(
                    case_slots, key=lambda slot: slot.get_slot_rank(Category.CASE_DURATION)
                ),
            ),
        ],
        **service.naive_version_information,
    )

    # Call the method under test
    actual_output = service.rank_standalone(request)

    # Assert that the actual output matches the expected output
    assert actual_output == expected_output
