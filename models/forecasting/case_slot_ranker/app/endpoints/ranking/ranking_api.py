import datetime
from http import HTTPStatus

import serving_utils.config as config
from fastapi import APIRouter, Response
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import get_service_version

from utils.types import (
    CaseSlot,
    Category,
    ForecastCaseDuration,
    RankedCaseSlotOutput,
    RankedCategory,
    Room,
    SlotCategory,
    SlotEnd,
    SlotStart,
    StandaloneAPIRequest,
)

logger = setup_json_logger(logger_name="CaseSlotRankerService")


# This is used by the forecast combiner to properly match the outputs to the case
# as well as adding the versioning information to the output


class RankingService:
    is_dev_stub: bool
    """Service class for case slot ranking."""

    def __init__(self) -> None:
        self.is_dev_stub = config.get_dev_stub() == "1"

        logger.info("Loading models. This may take a while")

        if self.is_dev_stub:
            logger.warning("Running in dev stub mode. Not loading models.")
            self.model_identifier = "None"

        else:
            pass
            # logger.info("Done loading models")
            # self.model_identifier = self.model_training_config.model_identifier

        logger.info("CaseSlotRankerService initialized")
        self.version_information = self.version_info()
        self.naive_version_information = self.naive_version_info()

        self.router = APIRouter(
            prefix="/v4",
            tags=["v4"],
        )
        self.router.add_api_route("/livez", self.livez, methods=["GET"], tags=["Health"])
        self.router.add_api_route("/readyz", self.readyz, methods=["GET"], tags=["Health"])

        self.router.add_api_route(
            "/rank/standalone", self.rank_standalone, methods=["POST"], tags=["Ranking"]
        )

        self.router.add_api_route(
            "/model_version_deployed",
            self.model_version_deployed,
            methods=["GET"],
            tags=["Service Version Info"],
        )
        self.router.add_api_route(
            "/version_info", self.version_info, methods=["GET"], tags=["Service Version Info"]
        )
        self.router.add_api_route(
            "/naive_version_info",
            self.naive_version_info,
            methods=["GET"],
            tags=["Service Version Info"],
        )

    def rank_standalone(
        self,
        request: StandaloneAPIRequest,
    ) -> RankedCaseSlotOutput:
        """
        Rank the case slots for a single case.
        Args:
            request (StandaloneAPIRequest): The request object containing the input data.
        Returns:
            RankedCaseSlotOutput: The ranked case slot output.
        """
        if self.is_dev_stub:
            now = datetime.datetime.now(tz=datetime.timezone.utc)
            case_slots = [
                CaseSlot(
                    room=Room(room_id="room_1"),
                    slot_start=SlotStart(start_time=now + datetime.timedelta(hours=1)),
                    slot_end=SlotEnd(end_time=now + datetime.timedelta(hours=2)),
                    forecast_case_duration=ForecastCaseDuration(
                        forecast_case_duration_minutes=60,
                        service_version=self.version_information["service_version"],
                        model_version=self.version_information["model_version"],
                        version=self.version_information["version"],
                    ),
                    slot_categories=[
                        SlotCategory(
                            category_name=Category.IMMEDIACY,
                            category_score=60,
                            category_rank=1,
                            category_is_golflike=True,
                        ),
                        SlotCategory(
                            category_name=Category.SUITABILITY,
                            category_score=0.8,
                            category_rank=3,
                            category_is_golflike=False,
                        ),
                        SlotCategory(
                            category_name=Category.CASE_DURATION,
                            category_score=60,
                            category_rank=2,
                            category_is_golflike=True,
                        ),
                    ],
                ),
                CaseSlot(
                    room=Room(room_id="room_2"),
                    slot_start=SlotStart(start_time=now + datetime.timedelta(hours=3)),
                    slot_end=SlotEnd(end_time=now + datetime.timedelta(hours=4)),
                    forecast_case_duration=ForecastCaseDuration(
                        forecast_case_duration_minutes=120,
                        service_version=self.naive_version_information["service_version"],
                        model_version=self.naive_version_information["model_version"],
                        version=self.naive_version_information["version"],
                    ),
                    slot_categories=[
                        SlotCategory(
                            category_name=Category.IMMEDIACY,
                            category_score=180,
                            category_rank=2,
                            category_is_golflike=True,
                        ),
                        SlotCategory(
                            category_name=Category.SUITABILITY,
                            category_score=1.0,
                            category_rank=1,
                            category_is_golflike=False,
                        ),
                        SlotCategory(
                            category_name=Category.CASE_DURATION,
                            category_score=120,
                            category_rank=3,
                            category_is_golflike=True,
                        ),
                    ],
                ),
                CaseSlot(
                    room=Room(room_id="room_3"),
                    slot_start=SlotStart(start_time=now + datetime.timedelta(hours=5)),
                    slot_end=SlotEnd(end_time=now + datetime.timedelta(hours=5, minutes=30)),
                    forecast_case_duration=ForecastCaseDuration(
                        forecast_case_duration_minutes=30,
                        service_version=self.version_information["service_version"],
                        model_version=self.version_information["model_version"],
                        version=self.version_information["version"],
                    ),
                    slot_categories=[
                        SlotCategory(
                            category_name=Category.IMMEDIACY,
                            category_score=300,
                            category_rank=3,
                            category_is_golflike=True,
                        ),
                        SlotCategory(
                            category_name=Category.SUITABILITY,
                            category_score=0.9,
                            category_rank=2,
                            category_is_golflike=False,
                        ),
                        SlotCategory(
                            category_name=Category.CASE_DURATION,
                            category_score=30,
                            category_rank=1,
                            category_is_golflike=True,
                        ),
                    ],
                ),
            ]

            return RankedCaseSlotOutput(
                top_scorers=[
                    RankedCategory(
                        category_name=Category.IMMEDIACY,
                        slots=case_slots,
                    ),
                    RankedCategory(
                        category_name=Category.SUITABILITY,
                        slots=case_slots,
                    ),
                    RankedCategory(
                        category_name=Category.CASE_DURATION,
                        slots=case_slots,
                    ),
                ],
                **self.naive_version_information,
            )
        # Implement the ranking logic here
        raise NotImplementedError("Ranking logic not implemented")

    def livez(self) -> Response:
        """Health check endpoint that verifies the service is running."""
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    async def readyz(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        return Response(status_code=HTTPStatus.OK, content="OK")

    def model_version_deployed(self) -> str:
        return self.model_identifier

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": "naive_ranker_model",
        }
