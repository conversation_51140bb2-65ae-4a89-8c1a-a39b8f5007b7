# What this service is for

This service returns a list of slots for a case that have been ranked by their quality

To reduce the complexity of features needed for the scheduling tool, it only these features:
- first_primary_surgeon
- first_primary_procedure

# Model Description

TODO


# Configurations

TODO

## Run locally

```
make run-local
```

Then you can make a request to the service by running the following command:

```
curl -X POST "http://localhost:3000/predict" -H 'Content-Type: application/json' -d '{"inputs": {"case_id": "0f5d5fd5-85a6-4ab4-aed1-a3e1f226e5ef"}}'
```

or visit the auto-generated landing page at http://localhost:3000/ to see the list of endpoints and examples of how to use them.

## Test making a call to the service in k8s

Configure your Kubectl to connect to the correct cluster:

* dev: `gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`
* prod: `gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b`

Get the list of contexts with the following command:
```
kubectl config get-contexts

# gke_dev-internal-b2aa9f_us-central1_dev-internal-gke
# gke_prod-internal-c5ac6b_us-central1_prod-internal-gke
```

Connect to the correct cluster using the following command:
```
kubectl config use-context gke_<project-id>_<region>_<cluster-name>
```

Create a tunnel so you can call the service from your machine. In this example, it'll make the
service available on localhost:9998:

```
kubectl -n service-case-slot-ranker port-forward service/service-case-slot-ranker 9970:80
```

Visit the auto-generated landing page at http://localhost:9998/ to see the list of endpoints and examples of how to use them.
