SHELL := /bin/bash
PORT := 9970

format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-async-trainer:
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml

dev-local:
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-local:
	poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml
