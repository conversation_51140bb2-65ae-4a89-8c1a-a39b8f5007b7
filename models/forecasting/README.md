## Testing and Releasing Changes to Schedule Forecasting

### 1. Validate that the Forecast Combiner can successfully call all subservices
Start the Forecast Combiner and all relevant subservices. Then go to http://localhost:3001/#/Service%20APIs/ModelInferenceService__predict_for_site and run the predict for site endpoint against 3 different sites with today's date. Use both the current default forecast variant, and whichever variant you are testing for.

### 2. If you are making changes to sub service API Contracts
Any changes to the API contracts of subservices will require sequencing the releases to prevent errors.
Here's a list of scenarios and the proper sequencing:
1. Removing a required field
    - You cannot remove a required field in 1 PR. It has to be 2 PRs.
        1. PR 1: Change the field to optional and update the Forecast Combiner call to drop the field.
            1. Release the new version of the subservice.
            2. Release the new version of the Forecast Combiner.
        2. PR 2: Remove the field from the Subservice input API contract.
            1. Release the new version of the subservice.
2. Adding a required field to a subservice
    1. Release the new version of the Forecast Combiner.
    2. Release the new version of the subservice.
3. Adding a new optional field to a subservice
    - Release order does not matter.
4. Adding a new return field from a subservice
    1. Release the new version of the subservice.
    2. Release the new version of the Forecast Combiner.
5. Removing a return field from a subservice
    1. Release the new version of the Forecast Combiner.
    2. Release the new version of the subservice.
#### Do Not update the usage of any contract fields in place, a new field must be made

### 3. If you are making changes to the Forecast Combiner API Contracts
Any changes to the API contracts of the Forecast Combiner will require sequencing releases with the [Realtime Dags](https://github.com/Apella-Technology/realtime-dags) project and any future additional consumers of the service.
Here's a list of scenarios and the proper sequencing:
1. Removing a required field
    - You cannot remove a required field in 1 PR. It has to be 2 PRs.
        1. PR 1: Change the field to optional and update the Forecast Combiner call to drop the field.
            1. Release the new version of the Forecast Combiner.
            2. Release the new version of the Realtime Dagster jobs.
        2. PR 2: Remove the field from the Forecast Combiner input API contract.
            1. Release the new version of the Forecast Combiner.
2. Adding a required field to the Forecast Combiner
    1. Release the new version of the Realtime Dagster jobs.
    2. Release the new version of the Forecast Combiner.
3. Adding a new optional field to the Forecast Combiner
    - Release order does not matter.
4. Adding a new return field from the Forecast Combiner
    1. Release the new version of the Forecast Combiner.
    2. Release the new version of the Realtime Dagster jobs.
5. Removing a return field from the Forecast Combiner
    1. Release the new version of the Realtime Dagster jobs.
    2. Release the new version of the Forecast Combiner.
#### Do Not update the usage of any contract fields in place, a new field must be made

#### Additionally you must test the forecast combiner against Realtime Dags on local. To do this 
1. Start the Forecast Combiner and all relevant subservices. 
2. Follow the instructions to [Run Dagster Locally](https://github.com/Apella-Technology/realtime-dags?tab=readme-ov-file#running-locally-against-devprod) against dev.
3. Run a [same day prediction](http://127.0.0.1:3000/locations/repository.py/jobs/same_day_forecast_job/playground) using todays date.
4. Verify that there are no errors.

### 4. Post Merge
After merging the PR please check the following dashboards to ensure that the changes are working as expected.
1. Go to [here](https://dagster.dev.internal.apella.io/locations/realtime-dags/jobs/same_day_forecast_job/runs) and verify that the jobs aren't failing.
2. Go to [forecasting service Dashboard](https://app.datadoghq.com/dashboard/euj-uj3-i85/service---forecasting?clustering_pattern_field_path=message&fromUser=true&tpl_var_cluster-name%5B0%5D=dev-internal-gke&tpl_var_env%5B0%5D=dev&from_ts=1738982092713&to_ts=1738982992713&live=true) and verify that there are no errors with the Forecast Combiner or any of the Subservices you merged.

### 5. Release
#### Be sure to follow the release order as described in the above sections.
#### For each service to be released
1. Go to [forecasting service Dashboard](https://app.datadoghq.com/dashboard/euj-uj3-i85/service---forecasting?clustering_pattern_field_path=message&fromUser=true&tpl_var_cluster-name%5B0%5D=dev-internal-gke&tpl_var_env%5B0%5D=dev&from_ts=1738982092713&to_ts=1738982992713&live=true) to verify that there are no errors with the released service and that all running pods are using the latest release.
2. Go to [here](https://dagster.internal.apella.io/locations/realtime-dags/jobs/same_day_forecast_job/runs) and verify that the jobs are running as expected.