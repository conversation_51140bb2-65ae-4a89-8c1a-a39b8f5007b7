import os
from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import numpy as np
import pandas as pd
import pytest
from aiohttp import ClientTimeout
from serving_utils.utils import get_service_version
from zoneinfo import ZoneInfo

from app.endpoints.predict.predict_api import (
    ForecastCombinerService,
)
from forecast_combiner.types import (
    APIInputs,
    APIRequest,
    DebugInfo,
    ForecastedCaseOutput,
    ForecastVariant,
    Output,
    RoomOutput,
    Temporality,
    VersionInfo,
)
from tests.test_helpers import get_mock_future

case_ids = ["A", "B"]

cases_to_predict = pd.DataFrame(
    [
        {
            "case_id": "A",
            "scheduled_start_time": datetime.fromisoformat("2024-01-01 10:00:00"),
            "scheduled_end_time": datetime.fromisoformat("2024-01-01 11:00:00"),
            "room_id": "room_id_1",
            "site_id": "site_id_1",
            "status": "status",
            "preceding_case_id": "preceding_case_id",
        },
        {
            "case_id": "B",
            "scheduled_start_time": datetime.fromisoformat("2024-01-01 10:00:00"),
            "scheduled_end_time": datetime.fromisoformat("2024-01-01 11:00:00"),
            "room_id": "room_id_2",
            "site_id": "site_id_1",
            "status": "status",
        },
    ]
)


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("app.endpoints.predict.predict_api.get_scheduled_cases_for_site")
@patch("app.endpoints.predict.predict_api.get_timezone_for_site")
@patch("apella_cloud_api.Client")
@pytest.mark.asyncio
async def test_predict_for_site_has_cases(
    mock_apella_client: Mock,
    mock_get_timezone_for_site: Mock,
    get_scheduled_cases_for_site: Mock,
    mock_bt_client: Mock,
) -> None:
    mock_get_timezone_for_site.return_value = ZoneInfo("UTC")
    get_scheduled_cases_for_site.return_value = pd.DataFrame([{"case_id": 1}, {"case_id": 2}])

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        service = ForecastCombinerService()
        service.http_session = MagicMock()

        mocked_predictions_output = Output(
            rooms={
                "room_1": RoomOutput(
                    cases=[
                        ForecastedCaseOutput(
                            case_id="1",
                            start_time=datetime(2024, 1, 1),
                            end_time=datetime(2024, 1, 2),
                            debug_info=DebugInfo(),
                            version_info=VersionInfo(
                                case_start_offset_model_version="v1",
                                case_duration_model_version="v2",
                                case_turnover_duration_model_version="v4",
                                event_model_forecasts_model_version="v5",
                                case_dynamic_end_model_version="v6",
                            ),
                        ),
                        ForecastedCaseOutput(
                            case_id="2",
                            start_time=datetime(2024, 2, 1),
                            end_time=datetime(2024, 2, 2),
                            debug_info=DebugInfo(),
                            version_info=VersionInfo(
                                case_start_offset_model_version="v1",
                                case_duration_model_version="v2",
                                case_turnover_duration_model_version="v4",
                                event_model_forecasts_model_version="v5",
                                case_dynamic_end_model_version="v6",
                            ),
                        ),
                    ]
                )
            },
            forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
            service_version=get_service_version(),
        )

        with patch.object(service, "_get_predictions", return_value=mocked_predictions_output) as _:
            predictions_output = await service.predict_for_site(
                APIRequest(
                    inputs=APIInputs(site_id="unit_test_site", date=datetime(2024, 1, 1).date())
                )
            )
            assert predictions_output == mocked_predictions_output
            predictions_output_dict = predictions_output.model_dump()
            assert predictions_output_dict["service_version"] == "v0.1.2"


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("app.endpoints.predict.predict_api.get_scheduled_cases_for_site")
@patch("app.endpoints.predict.predict_api.get_timezone_for_site")
@patch("apella_cloud_api.Client")
@pytest.mark.asyncio
async def test_predict_for_site_has_no_cases(
    mock_apella_client: Mock,
    mock_get_timezone_for_site: Mock,
    get_scheduled_cases_for_site: Mock,
    mock_bt_client: Mock,
) -> None:
    mock_get_timezone_for_site.return_value = ZoneInfo("UTC")
    get_scheduled_cases_for_site.return_value = None

    mocked_predictions_output = get_mock_future(None)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        service = ForecastCombinerService()
        service.http_session = MagicMock()

        # this won't get called, but just to be safe mock it out
        with patch.object(
            service, "_get_predictions", return_value=mocked_predictions_output
        ) as mock_get_predictions:
            predictions_output = await service.predict_for_site(
                APIRequest(
                    inputs=APIInputs(site_id="unit_test_site", date=datetime(2024, 1, 1).date())
                )
            )

            mock_get_predictions.assert_not_called()
            assert predictions_output == Output(
                rooms={},
                forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
                service_version="v0.1.2",
            )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("app.endpoints.predict.predict_api.TCPConnector")
@patch("app.endpoints.predict.predict_api.ClientSession")
@patch("app.endpoints.predict.predict_api.ManyCaseSubServiceFetcher")
@patch("apella_cloud_api.Client")
@pytest.mark.asyncio
async def test_metrics(
    mock_apella_client: Mock,
    mock_submodel_fetcher: Mock,
    mock_client_session: Mock,
    mock_tcp_connector: Mock,
    mock_bt_client: Mock,
) -> None:
    import datetime

    FAKE_NOW = pd.Timestamp("2024-01-01 16:00:00-00:00")
    datetime_mock = MagicMock(wraps=datetime.datetime)
    datetime_mock.now.return_value = FAKE_NOW

    case_results_df = pd.DataFrame(
        [
            {
                "case_id": "Case A",
                "case_duration": 1,
                "feature_timestamps": {
                    "case_duration_bigtable_timestamp": datetime.datetime(
                        2024, 1, 1, 1, tzinfo=datetime.timezone.utc
                    ).timestamp(),
                    "case_start_offset_bigtable_timestamp": None,
                },
            },
            {
                "case_id": "Case B",
                "case_duration": np.nan,
                "feature_timestamps": {
                    "case_duration_bigtable_timestamp": datetime.datetime(
                        2024, 1, 1, 12, tzinfo=datetime.timezone.utc
                    ).timestamp(),
                    "case_start_offset_bigtable_timestamp": datetime.datetime(
                        2024, 1, 1, 13, tzinfo=datetime.timezone.utc
                    ).timestamp(),
                },
            },
            {
                "case_id": "Case C",
                "case_duration": 3,
                "feature_timestamps": {
                    "case_duration_bigtable_timestamp": datetime.datetime(
                        2024, 1, 1, tzinfo=datetime.timezone.utc
                    ).timestamp(),
                    "case_start_offset_bigtable_timestamp": datetime.datetime(
                        2024, 1, 1, 3, tzinfo=datetime.timezone.utc
                    ).timestamp(),
                },
            },
        ]
    )
    non_naive_inc = MagicMock()
    non_naive_labels = MagicMock(return_value=non_naive_inc)
    non_naive_counter = MagicMock(labels=non_naive_labels)

    naive_inc = MagicMock()
    naive_labels = MagicMock(return_value=naive_inc)
    naive_counter = MagicMock(labels=naive_labels)

    feature_age_obs = MagicMock()
    feature_age_labels = MagicMock(return_value=feature_age_obs)
    feature_age_counter = MagicMock(labels=feature_age_labels)

    with patch(
        "app.endpoints.predict.predict_api._NAIVE_FORECASTS", naive_counter
    ) as naive_forecasts, patch(
        "app.endpoints.predict.predict_api._NON_NAIVE_FORECASTS", non_naive_counter
    ) as non_naive_forecasts, patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
        },
    ), patch("app.endpoints.predict.predict_api._FEATURE_AGE_METRIC", feature_age_counter), patch(
        "app.endpoints.predict.predict_api.pd.Timestamp", datetime_mock
    ):
        model_inference_service = ForecastCombinerService()
        naive_forecasts.labels = MagicMock(inc=MagicMock())
        await model_inference_service._update_naivety_metrics(
            case_results_df,
            "case_duration",
            "test_site",
            "future",
        )
        assert non_naive_forecasts.labels.call_count == 1
        non_naive_forecasts.labels.assert_called_with(
            site_id="test_site",
            temporality="future",
            feature="case_duration",
        )
        assert non_naive_forecasts.labels.return_value.inc.call_count == 1
        non_naive_forecasts.labels.return_value.inc.assert_called_with(
            amount=2,
        )
        assert naive_forecasts.labels.call_count == 1
        naive_forecasts.labels.assert_called_with(
            site_id="test_site",
            temporality="future",
            feature="case_duration",
        )
        assert naive_forecasts.labels.return_value.inc.call_count == 1
        naive_forecasts.labels.return_value.inc.assert_called_with(
            amount=1,
        )

        assert feature_age_counter.labels.return_value.observe.call_count == 0


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("app.endpoints.predict.predict_api.TCPConnector")
@patch("app.endpoints.predict.predict_api.ClientSession")
@patch("app.endpoints.predict.predict_api.ManyCaseSubServiceFetcher")
@patch("app.endpoints.predict.predict_api.PerCaseSubServiceFetcher")
@patch("apella_cloud_api.Client")
@pytest.mark.asyncio
async def test_get_predictions_df(
    mock_apella_client: Mock,
    mock_per_case_subservice_fetcher: Mock,
    mock_sub_service_fetcher: Mock,
    mock_client_session: Mock,
    mock_tcp_connector: Mock,
    mock_bt_client: Mock,
) -> None:
    (
        mock_submodel_fetcher_so_instance,
        mock_submodel_fetcher_td_instance,
        mock_submodel_fetcher_em_instance,
        mock_submodel_fetcher_de_instance,
        mock_submodel_fetcher_ba_instance,
    ) = create_and_initialize_mocks()

    mock_sub_service_fetcher.side_effect = [
        mock_submodel_fetcher_so_instance,
        mock_submodel_fetcher_td_instance,
        mock_submodel_fetcher_em_instance,
        mock_submodel_fetcher_de_instance,
    ]

    mock_per_case_subservice_fetcher.side_effect = [mock_submodel_fetcher_ba_instance]

    service = ForecastCombinerService()
    await service.start_session()

    result = await service._get_predictions_df(
        case_ids,
        cases_to_predict,
        "site_0",
        Temporality.FUTURE,
        ForecastVariant.BAYESIAN_STATIC_FORECAST,  # prediction_tag will be None
    )

    mock_client_session.assert_called_once_with(
        timeout=ClientTimeout(total=5), connector=mock_tcp_connector.return_value
    )

    expected_df = pd.DataFrame(
        [
            {
                "case_id": "A",
                "scheduled_start_time": datetime.fromisoformat("2024-01-01 10:00:00"),
                "scheduled_end_time": datetime.fromisoformat("2024-01-01 11:00:00"),
                "room_id": "room_id_1",
                "site_id": "site_id_1",
                "status": "status",
                "preceding_case_id": "preceding_case_id",
                "start_offset": 10,
                "turnover_duration": 5,
                "event_model_forecast": np.datetime64("2024-01-01"),
                "dynamic_case_end": 100,
                "dynamic_case_end_prediction_tag": "tag",
                "bayesian_case_duration": 1,
                "case_duration": 1,
            },
            {
                "case_id": "B",
                "scheduled_start_time": datetime.fromisoformat("2024-01-01 10:00:00"),
                "scheduled_end_time": datetime.fromisoformat("2024-01-01 11:00:00"),
                "room_id": "room_id_2",
                "site_id": "site_id_1",
                "status": "status",
                "preceding_case_id": pd.NA,
                "start_offset": 20,
                "turnover_duration": 15,
                "event_model_forecast": np.datetime64("2024-01-02"),
                "dynamic_case_end": 200,
                "dynamic_case_end_prediction_tag": "unknown",
                "bayesian_case_duration": 2,
                "case_duration": 2,
            },
        ]
    ).replace({pd.NA: None})
    pd.testing.assert_frame_equal(result, expected_df)


def create_and_initialize_mocks() -> list[Mock]:
    mock_submodel_fetcher_so_instance = Mock()
    mock_submodel_fetcher_td_instance = Mock()
    mock_submodel_fetcher_em_instance = Mock()
    mock_submodel_fetcher_de_instance = Mock()
    mock_submodel_fetcher_ba_instance = Mock()

    expected_results = (
        pd.DataFrame([{"case_id": "A", "start_offset": 10}, {"case_id": "B", "start_offset": 20}]),
        pd.DataFrame(
            [{"case_id": "A", "turnover_duration": 5}, {"case_id": "B", "turnover_duration": 15}]
        ),
        pd.DataFrame(
            [
                {"case_id": "A", "event_model_forecast": np.datetime64("2024-01-01")},
                {"case_id": "B", "event_model_forecast": np.datetime64("2024-01-02")},
            ]
        ),
        pd.DataFrame(
            [
                {"case_id": "A", "dynamic_case_end": 100, "dynamic_case_end_prediction_tag": "tag"},
                {
                    "case_id": "B",
                    "dynamic_case_end": 200,
                    "dynamic_case_end_prediction_tag": "unknown",
                },
            ]
        ),
        pd.DataFrame(
            [
                {"case_id": "A", "bayesian_case_duration": 1},
                {"case_id": "B", "bayesian_case_duration": 2},
            ]
        ),
    )

    mock_submodel_fetcher_so_instance.fetch.return_value = get_mock_future(expected_results[0])
    mock_submodel_fetcher_td_instance.fetch.return_value = get_mock_future(expected_results[1])
    mock_submodel_fetcher_em_instance.fetch.return_value = get_mock_future(expected_results[2])
    mock_submodel_fetcher_de_instance.fetch.return_value = get_mock_future(expected_results[3])
    mock_submodel_fetcher_ba_instance.fetch.return_value = get_mock_future(expected_results[4])

    return [
        mock_submodel_fetcher_so_instance,
        mock_submodel_fetcher_td_instance,
        mock_submodel_fetcher_em_instance,
        mock_submodel_fetcher_de_instance,
        mock_submodel_fetcher_ba_instance,
    ]


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("app.endpoints.predict.predict_api.TCPConnector")
@patch("app.endpoints.predict.predict_api.ClientSession")
@patch("app.endpoints.predict.predict_api.ManyCaseSubServiceFetcher")
@patch("app.endpoints.predict.predict_api.PerCaseSubServiceFetcher")
@patch("apella_cloud_api.Client")
@pytest.mark.asyncio
async def test_get_predictions_df_no_redundant_calls(
    mock_apella_client: Mock,
    mock_per_case_subservice_fetcher: Mock,
    mock_sub_service_fetcher: Mock,
    mock_client_session: Mock,
    mock_tcp_connector: Mock,
    mock_bt_client: Mock,
) -> None:
    for forecast_variant in ForecastVariant:
        await assert_no_redundant_calls_for_forecast_variant(
            mock_apella_client,
            mock_sub_service_fetcher,
            mock_per_case_subservice_fetcher,
            mock_client_session,
            mock_tcp_connector,
            forecast_variant,
        )


async def assert_no_redundant_calls_for_forecast_variant(
    mock_apella_client: Mock,
    mock_sub_service_fetcher: Mock,
    mock_per_case_subservice_fetcher: Mock,
    mock_client_session: Mock,
    mock_tcp_connector: Mock,
    forecast_variant: ForecastVariant,
) -> None:
    (
        mock_submodel_fetcher_so_instance,
        mock_submodel_fetcher_td_instance,
        mock_submodel_fetcher_em_instance,
        mock_submodel_fetcher_de_instance,
        mock_submodel_fetcher_ba_instance,
    ) = create_and_initialize_mocks()

    mock_sub_service_fetcher.side_effect = [
        mock_submodel_fetcher_so_instance,
        mock_submodel_fetcher_td_instance,
        mock_submodel_fetcher_em_instance,
        mock_submodel_fetcher_de_instance,
        # mock_submodel_fetcher_ba_instance,
    ]
    mock_per_case_subservice_fetcher.side_effect = [mock_submodel_fetcher_ba_instance]

    service = ForecastCombinerService()
    await service.start_session()

    await service._get_predictions_df(
        case_ids,
        cases_to_predict,
        "site_0",
        Temporality.FUTURE,
        forecast_variant,
    )

    expected_calls_to_so_submodel_fetcher = 1
    expected_calls_to_td_submodel_fetcher = 1
    expected_calls_to_em_submodel_fetcher = 1
    expected_calls_to_de_submodel_fetcher = 1
    expected_calls_to_ba_submodel_fetcher = (
        1 if forecast_variant == ForecastVariant.BAYESIAN_STATIC_FORECAST else 0
    )

    assert (
        mock_submodel_fetcher_so_instance.fetch.call_count == expected_calls_to_so_submodel_fetcher
    )
    assert (
        mock_submodel_fetcher_td_instance.fetch.call_count == expected_calls_to_td_submodel_fetcher
    )
    assert (
        mock_submodel_fetcher_em_instance.fetch.call_count == expected_calls_to_em_submodel_fetcher
    )
    assert (
        mock_submodel_fetcher_de_instance.fetch.call_count == expected_calls_to_de_submodel_fetcher
    )
    assert (
        mock_submodel_fetcher_ba_instance.fetch.call_count == expected_calls_to_ba_submodel_fetcher
    )
