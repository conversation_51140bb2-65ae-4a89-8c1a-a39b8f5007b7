from dataclasses import dataclass
from datetime import datetime
from typing import Tuple
from unittest.mock import MagicMock

import pandas as pd
import pytest
from zoneinfo import ZoneInfo

from forecast_combiner.forecast_model_combiner import (
    ForecastModelCombiner,
    convert_df_to_combiner_inputs,
    forecast_for_room,
)
from forecast_combiner.types import ForecastVariant, PhaseInfo

la_time = ZoneInfo(key="America/Los_Angeles")


################################################################################################
# What follows are functions to help with testing the actual business logic of these functions #
################################################################################################
@dataclass
class TestApellaCase:
    id: str
    sched_start_time: str
    sched_end_time: str
    static_forecasted_duration_minutes: float
    static_start_offset_minutes: float
    forecast_turnover_duration_minutes: float | None = None
    event_model_dynamic_end_time: str | None = None
    dynamic_case_end_time_minutes: float | None = None
    preceding_case_id: str = ""

    def __post_init__(self) -> None:
        start_time = datetime.strptime(self.sched_start_time, "%H:%M")
        self.start_datetime: datetime = datetime(
            2022, 10, 1, start_time.hour, start_time.minute, tzinfo=la_time
        )

        end_time = datetime.strptime(self.sched_end_time, "%H:%M")
        self.end_datetime: datetime = datetime(
            2022, 10, 1, end_time.hour, end_time.minute, tzinfo=la_time
        )

        if self.static_forecasted_duration_minutes is not None:
            self.forecasted_duration = self.static_forecasted_duration_minutes

        if self.static_start_offset_minutes is not None:
            self.forecasted_start_offset = self.static_start_offset_minutes

        if self.forecast_turnover_duration_minutes is not None:
            self.forecast_turnover_duration_minutes = self.forecast_turnover_duration_minutes

        if self.dynamic_case_end_time_minutes is not None:
            self.dynamic_case_end_time_minutes = self.dynamic_case_end_time_minutes

        self.dynamic_end_time = None
        if self.event_model_dynamic_end_time is not None:
            end_time = datetime.strptime(self.event_model_dynamic_end_time, "%H:%M")
            self.dynamic_end_time = datetime(
                2022, 10, 1, end_time.hour, end_time.minute, tzinfo=la_time
            )


def generate_inputs_for_test(
    cases: list[TestApellaCase],
) -> Tuple[
    str,  # room_id
    str,  # site_id
    datetime,  # now
    pd.DataFrame,  # room_df
]:
    room_id = "Room"
    site_id = "SITE"
    now = datetime(2022, 9, 28, 0, 0, tzinfo=la_time)

    # Function to take the TestApellaCases and turn them into the inputs of forecast_for_room
    room_info = []
    for case in cases:
        this_entry = {
            "case_id": case.id,
            "scheduled_start_time": case.start_datetime,
            "scheduled_end_time": case.end_datetime,
            "following_case_id": "",
            "preceding_case_id": case.preceding_case_id,
            "start_offset": case.forecasted_start_offset,
            "start_offset_version": "v1.2.3",
            "turnover_duration_version": "v2.3.4",
            "turnover_duration": case.forecast_turnover_duration_minutes,
            "event_model_forecast": case.dynamic_end_time,
            "event_model_forecast_version": "v3.4.5",
            "dynamic_case_end": case.dynamic_case_end_time_minutes,
            "dynamic_case_end_version": "v4.5.6",
            "dynamic_case_end_prediction_tag": "tag",
            "case_duration": case.forecasted_duration,
            "bayesian_case_duration": case.forecasted_duration,
            "bayesian_case_duration_original": [
                case.forecasted_duration - 10,
                case.forecasted_duration,
                case.forecasted_duration + 10,
            ],
            "bayesian_case_duration_version": "v5.6.7",
        }
        room_info.append(this_entry)
    room_df = pd.DataFrame(room_info)

    return room_id, site_id, now, room_df


# Many tests of determine_case_status
# test 1: no phases, all not started
# test 2: one incomplete phase, first one started
# test 3: 1 complete phase, 1 incomplete phase, first one complete, next not started
# test 4: 2 complete phases, 1 incomplete phase, last one has started
# test 5: 2 complete phases, complete/complete/not_started
@pytest.mark.parametrize(
    "phases,expected",
    [
        ([], {"a": "NOT_STARTED", "b": "NOT_STARTED", "c": "NOT_STARTED"}),
        (
            [
                PhaseInfo(
                    case_id="a",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 1, 2, 3),
                    end_time=None,
                )
            ],
            {"a": "STARTED", "b": "NOT_STARTED", "c": "NOT_STARTED"},
        ),
        (
            [
                PhaseInfo(
                    case_id="a",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 1, 2, 3),
                    end_time=datetime(2024, 5, 1, 2, 3, 4),
                ),
                PhaseInfo(
                    case_id="b",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 3, 4, 5),
                    end_time=None,
                ),
            ],
            {"a": "COMPLETE", "b": "STARTED", "c": "NOT_STARTED"},
        ),
        (
            [
                PhaseInfo(
                    case_id="a",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 1, 2, 3),
                    end_time=datetime(2024, 5, 1, 2, 3, 4),
                ),
                PhaseInfo(
                    case_id="b",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 3, 4, 5),
                    end_time=datetime(2024, 5, 1, 4, 5, 6),
                ),
                PhaseInfo(
                    case_id="c",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 5, 6, 7),
                    end_time=None,
                ),
            ],
            {"a": "COMPLETE", "b": "COMPLETE", "c": "STARTED"},
        ),
        (
            [
                PhaseInfo(
                    case_id="a",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 1, 2, 3),
                    end_time=datetime(2024, 5, 1, 2, 3, 4),
                ),
                PhaseInfo(
                    case_id="b",
                    room_id="ROOM",
                    start_time=datetime(2024, 5, 1, 3, 4, 5),
                    end_time=datetime(2024, 5, 1, 4, 5, 6),
                ),
            ],
            {"a": "COMPLETE", "b": "COMPLETE", "c": "NOT_STARTED"},
        ),
    ],
)
def test_determine_case_status(
    phases: list[PhaseInfo],
    expected: dict[str, str],
) -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=120,
        static_start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
    )

    _, _, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    _, _, _, _, _, _, _, _, cases = convert_df_to_combiner_inputs(room_df)

    combiner = ForecastModelCombiner(
        case_duration_minutes_by_case_id=MagicMock(),
        case_start_offset_minutes_by_case_id=MagicMock(),
        case_turnover_minutes_by_case_id=MagicMock(),
        case_event_model_forecasts_time_by_case_id=MagicMock(),
        case_dynamic_end_minutes_by_case_id=MagicMock(),
        case_dynamic_end_pred_tag_by_case_id=MagicMock(),
        bayesian_case_duration_minutes_by_case_id=MagicMock(),
        bayesian_case_duration_full_posterior_by_case_id=MagicMock(),
        forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
        cases=cases,
        predicted_phases=phases,
        site_id="site",
    )

    case_status = combiner.determine_case_status()
    assert case_status == expected


##############################
# Scenario 1: Forecasted time has not passed, nothing should change.
#
# Current time: 8:15 --  Case has not started -- Forecasted time has not been reached
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes
#
# expected:
# case A  8:30-9:30  (current time is before forecasted start, no update needed)
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario1() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 8, 15, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 2: Forecast time reached, need to add dynamic offset
#
# Current time: 8:30  -- Case has not started -- we now expect to add the dynamic_start_offset_site to the first case
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes
#
# expected:
# case A  8:35-9:35  (add 5 minutes from MINUTES_TO_EXTEND_CURRENT_CASE)
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario2() -> None:
    now = datetime(2022, 10, 1, 8, 30, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 35, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 2B: Forecast time reached, need to add dynamic offset, but the dynamic offset is only 1 minute, so it should add 5.
#
# Current time: 8:30  -- Case has not started -- we now expect to add the dynamic_start_offset_site to the first case
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes
#
# expected:
# case A  8:35-9:35  (add 5 minutes because dynamic start offset was only 1 minute from now)
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario2b() -> None:
    now = datetime(2022, 10, 1, 8, 30, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 35, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 3: Forecast time has been reached, dynamic offset has no value, add default time.
#
# Current time: 8:30  -- Case has not started -- Dynamic offset model did not produce a result. Add 5
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes
#
# expected:
# case A  8:35-9:35  (add 5 minutes because we have no model)
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario3() -> None:
    now = datetime(2022, 10, 1, 8, 30, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 35, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 3B: Forecast time has been reached, dynamic offset has value of 2, add default time.
#
# Current time: 8:30  -- Case has not started -- Dynamic offset model did not produce a result. Add 5
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A  8:35-9:35  (add 5 minutes because we have no model)
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario3b() -> None:
    now = datetime(2022, 10, 1, 8, 30, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )

    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 35, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 4:  Case already started. Shift everything accordingly
#
# Current time: 8:30  -- Case stared at 8:25 -- expected end to shift accordingly
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A  8:25-9:25
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario4() -> None:
    now = datetime(2022, 10, 1, 8, 30, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            case_id="a",
            room_id="ROOM",
            start_time=datetime(2022, 10, 1, 8, 25, tzinfo=la_time),
            end_time=None,
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 25, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 25, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 5: Super long delay and case hasn't started yet. Keep shifting the schedule.
#
# Current time: 10:00  -- Case has not started -- shift rest of cases as well
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A 10:05-11:05
# case B 11:35-13:15
# case C 13:45-15:15
def test_scenario5() -> None:
    now = datetime(2022, 10, 1, 10, 0, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases: list[PhaseInfo] = []
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 5, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 11, 5, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 11, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 13, 15, tzinfo=la_time)
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 45, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 15, 15, tzinfo=la_time)


##############################
# Scenario 6:  First case complete, second hasn't started yet.
#
# Current time: 10:00  -- First case completed at 9:25
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A NO FORECAST
# case B 10:50-12:30
# case C 13:10-14:40
def test_scenario6() -> None:
    now = datetime(2022, 10, 1, 10, 0, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            end_time=datetime(2022, 10, 1, 9, 25, tzinfo=la_time),
            start_time=datetime(2022, 10, 1, 9, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 50, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 30, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 7: First case complete, second one is past forecasted start time.
#
# Current time: 10:50  -- First case completed at 10:
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A NO FORECAST
# case B 10:55-12:35 (includes 5 mins from MINUTES_TO_EXTEND_CURRENT_CASE)
# case C 13:10-14:40
def test_scenario7() -> None:
    now = datetime(2022, 10, 1, 10, 50, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            end_time=datetime(2022, 10, 1, 10, tzinfo=la_time),
            start_time=datetime(2022, 10, 1, 9, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 55, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 8: First case complete, second one passed forecasted start but dynamic value is unrealistic.
#
# Current time: 10:50  -- First case completed at 10 -- dynamic offset produces bogus value:
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A NO FORECAST
# case B 10:55-12:35  (dynamic offset produces a time before now. so default to +5 from now)
# case C 13:10-14:40
def test_scenario8() -> None:
    now = datetime(2022, 10, 1, 10, 50, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            end_time=datetime(2022, 10, 1, 10, tzinfo=la_time),
            start_time=datetime(2022, 10, 1, 9, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 55, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 35, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 10, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 40, tzinfo=la_time)


##############################
# Scenario 9:  First case ended late. Second one needs to add dynamic start, but start time of second needs a minimum turnover time.
#
# Current time: 10:50  -- First case completed at 10:45
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# minimum_turnover = 30 minutes
# expected:
# case A NO FORECAST
# case B 11:15-12:55 (10:30 + 20 minutes start offset + 5 minutes dynamic start offset + 20 minutes additive turnover)
# case C 13:25-14:55 (13:00 + 10 minutes start offset + 5 minutes dynamic start offset + 10 minutes additive turnover)
def test_scenario9() -> None:
    now = datetime(2022, 10, 1, 10, 50, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            end_time=datetime(2022, 10, 1, 10, 45, tzinfo=la_time),
            start_time=datetime(2022, 10, 1, 9, 45, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 11, 15, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 55, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 25, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 55, tzinfo=la_time)


##############################
# Scenario 10:  First case ended late. Second one needs to add turnover
#
# Current time: 10:50  -- First case completed at 10:45
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes;
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 20 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 10 minutes;
#
# expected:
# case A NO FORECAST
# case B 11:15-12:55 (10:30 + 20 minutes start offset + 5 minutes dynamic start offset + 20 minutes additive turnover)
# case C 13:55-15:25 (13:00 + 10 minutes start offset + 5 minutes dynamic start offset + 40 minutes additive turnover)
def test_scenario10() -> None:
    now = datetime(2022, 10, 1, 10, 50, 0, tzinfo=la_time)
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
        forecast_turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        static_start_offset_minutes=20,
        forecast_turnover_duration_minutes=60,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=10,
        forecast_turnover_duration_minutes=30,
    )

    # set the phases in the dto for case starting
    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    predicted_phases = [
        PhaseInfo(
            end_time=datetime(2022, 10, 1, 10, 45, tzinfo=la_time),
            start_time=datetime(2022, 10, 1, 9, 45, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )
    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 11, 15, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 55, tzinfo=la_time)
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 55, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 15, 25, tzinfo=la_time)


##############################
# Scenario 11: Static forecasted end time has not passed, dynamic end time has been generated behind schedule, whole day shifts later
#
# Current time: 9:25
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes; dynamic_end_time 9:45
# case B scheduled 10:30-11:30, duration of 1:40 hour; start_offset 0 minutes; dynamic_end_time 9:45
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes; dynamic_end_time 9:45
#
# expected:
# case A  8:30-9:45 (end time from event model)
# case B 10:45-12:25 (10:30 + 0 minutes start offset + 10 minutes dynamic start offset + 5 minutes additive turnover [forecast_turnover_duration is 60 minutes from case A])
# case C 13:05-14:35 (13:00 + 0 minutes start offset + 5 minutes dynamic start offset + 0 minutes additive turnover [forecast_turnover_duration is 40 minutes from case B])
def test_scenario11() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
        event_model_dynamic_end_time="9:45",
        forecast_turnover_duration_minutes=60,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        event_model_dynamic_end_time="9:45",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
        event_model_dynamic_end_time="9:45",
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 25, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            end_time=None,
            start_time=datetime(2022, 10, 1, 8, 30, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 45, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[0].version_info.event_model_forecasts_model_version == "v3.4.5"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 45, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 25, tzinfo=la_time)
    assert results.forecasts[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 5, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 35, tzinfo=la_time)


##############################
# Scenario 12: Static forecasted time has not passed, dynamic end time has been generated ahead of schedule, shortening expected case duration, resulting in earlier than anticipated close of ORs
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes; dynamic_end_time 9:15
# case B scheduled 10:15-11:15, duration of 1:40 hour; start_offset 0 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes;
#
# expected:
# case A  8:30-9:15 (8:00 + 30 minutes start offset; end time from event model)
# case B 10:15-11:55
# case C 13:00-14:30
def test_scenario12() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        event_model_dynamic_end_time="9:15",
        forecast_turnover_duration_minutes=60,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        event_model_dynamic_end_time="9:15",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
        event_model_dynamic_end_time="9:15",
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 00, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            end_time=None,
            start_time=datetime(2022, 10, 1, 8, 30, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 15, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[0].version_info.event_model_forecasts_model_version == "v3.4.5"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 15, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 11, 55, tzinfo=la_time)
    assert results.forecasts[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 13: Static forecasted time has not passed, dynamic end time is in the past, no op
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:00 hour; start_offset 30 minutes; dynamic_end_time 8:55
# case B scheduled 10:15-11:15, duration of 1:40 hour; start_offset 0 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes;
#
# expected:
# case A  8:30-9:30
# case B 10:30-12:10 (10:15 + 0 minutes start offset + 0 minutes dynamic start offset + 15 minutes additive turnover [forecast_turnover_duration is 60 minutes from case A])
# case C 13:00-14:30
def test_scenario13() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=30,
        event_model_dynamic_end_time="8:55",
        forecast_turnover_duration_minutes=60,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        event_model_dynamic_end_time="8:55",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
        event_model_dynamic_end_time="8:55",
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 00, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            end_time=None,
            start_time=datetime(2022, 10, 1, 8, 30, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[0].version_info.event_model_forecasts_model_version == "v3.4.5"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 30, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 12, 10, tzinfo=la_time)
    assert results.forecasts[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 00, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 14: Static forecasted time has not passed, dynamic end time has been generated ahead of schedule
# Next case has a start offset, make sure the start offset and dynamic end time are correctly applied
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes; dynamic_end_time 9:10
# case B scheduled 10:15-11:15, duration of 1:40 hour; start_offset -10 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes;
#
# expected:
# case A  8:30-9:10
# case B 10:10-11:50 (10:15 - 10 minutes start offset + 5 minutes additive turnover [60 from case A])
# case C 13:00-14:30
def test_scenario14() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        event_model_dynamic_end_time="9:10",
        forecast_turnover_duration_minutes=60,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=-10,
        event_model_dynamic_end_time="9:10",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
        event_model_dynamic_end_time="9:10",
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 00, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            end_time=None,
            start_time=datetime(2022, 10, 1, 8, 30, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 3
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 8, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 9, 10, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[0].version_info.event_model_forecasts_model_version == "v3.4.5"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 10, 10, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 11, 50, tzinfo=la_time)
    assert results.forecasts[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results.forecasts[2].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results.forecasts[2].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 15: First case completed ahead of schedule day is back on track
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes; dynamic_end_time 9:10
# case B scheduled 10:15-11:15, duration of 1:40 hour; start_offset -10 minutes;
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes;
#
# expected:
# case B 10:05-11:45 (10:15 - 10 minutes start offset [0 additive turnover since case A is completed, limit is 10:00 {9:00 + 60 minutes}])
# case C 13:00-14:30
def test_scenario15() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        forecast_turnover_duration_minutes=60,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=-10,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 00, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 8, 0, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 9, 0, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 5, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 11, 45, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 16: First case completed ahead of schedule to follow case is moved up
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes
# case B scheduled 10:15-11:15, duration of 1:40 hour; to follow case
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes
#
# expected:
# case B 9:30-11:10 (9:00 [case A end time] + 30 minutes [case A turnover])
# case C 13:00-14:30
def test_scenario16() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        forecast_turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        preceding_case_id="a",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 00, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 8, 0, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 9, 0, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 11, 10, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 17: Cases are out of order
#
# Current time: 11:05
# case A scheduled  9:00-10:00; duration of 45 minutes; start_offset 15 minutes;
# case B scheduled 10:00-11:00, duration of 45 minutes; start_offset 15 minutes;
# case C scheduled 11:00-12:00, duration of 90 minutes; start_offset 15 minutes;
# case D scheduled 12:00-14:00, duration of 90 minutes; start_offset 15 minutes;
#
# predicted phases:
# case B 8:55-9:25 (completed, happens ahead of schedule, ahead of case A)
# case A 9:40-10:15 (completed)
# case D 10:40- (started ahead of schedule, ahead of case C)
#
# expected:
# case D 10:40-12:10
# case C 12:50-14:20
def test_scenario17() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="09:00",
        sched_end_time="10:00",
        static_forecasted_duration_minutes=45,
        static_start_offset_minutes=15,
        forecast_turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:00",
        sched_end_time="11:00",
        static_forecasted_duration_minutes=45,
        static_start_offset_minutes=15,
        forecast_turnover_duration_minutes=30,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="11:00",
        sched_end_time="12:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=15,
        forecast_turnover_duration_minutes=40,
    )
    caseD = TestApellaCase(
        id="d",
        sched_start_time="12:00",
        sched_end_time="14:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=15,
        forecast_turnover_duration_minutes=40,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC, caseD])
    now = datetime(2022, 10, 1, 11, 5, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 8, 55, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 9, 25, tzinfo=la_time),
            room_id="ROOM",
            case_id="b",
        ),
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 9, 40, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 10, 15, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        ),
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 10, 40, tzinfo=la_time),
            end_time=None,
            room_id="ROOM",
            case_id="d",
        ),
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 2

    assert results.forecasts[0].case_id == "d"
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 10, 40, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 12, 10, tzinfo=la_time)

    assert results.forecasts[1].case_id == "c"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 12, 50, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 20, tzinfo=la_time)


##############################
# Scenario 18: First case completed ahead of schedule however current time is after the to follow forecasted start end time
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes
# case B scheduled 10:15-11:15, duration of 1:40 hour; to follow case
# case C scheduled 13:00-15:00, duration of 1:30 hour; start_offset 0 minutes
#
# expected:
# case B 9:45-11:25 (9:00 [case A end time] + 45 minutes [Current time])
# case C 13:00-14:30
def test_scenario18() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        forecast_turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=100,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        preceding_case_id="a",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 45, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 8, 0, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 9, 0, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 9, 45, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 11, 25, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 19: First case completed ahead of schedule however current time is after the to follow forecasted start end time
#
# Current time: 9:00
# case A scheduled  8:00- 9:30; duration of 1:30 hour; start_offset 30 minutes
# case B scheduled 10:15-11:15, duration of 1:40 hour; to follow case
# case C scheduled 13:00-15:00, duration of 1:30 hour;
#
# expected:
# case B 9:45-11:45 (9:00 [case A end time] + 45 minutes [Current time])
# case C 12:25-13:25 (9:00 [case B end time] + 40 minutes [Case B turnover])
def test_scenario19() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        static_forecasted_duration_minutes=90,
        static_start_offset_minutes=30,
        forecast_turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:15",
        sched_end_time="11:15",
        static_forecasted_duration_minutes=120,
        forecast_turnover_duration_minutes=40,
        static_start_offset_minutes=0,
        preceding_case_id="a",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="11:45",
        sched_end_time="12:45",
        static_forecasted_duration_minutes=60,
        static_start_offset_minutes=0,
        forecast_turnover_duration_minutes=40,
    )

    room_id, site_id, _, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    now = datetime(2022, 10, 1, 9, 45, 0, tzinfo=la_time)
    predicted_phases: list[PhaseInfo] = [
        PhaseInfo(
            start_time=datetime(2022, 10, 1, 8, 0, tzinfo=la_time),
            end_time=datetime(2022, 10, 1, 9, 0, tzinfo=la_time),
            room_id="ROOM",
            case_id="a",
        )
    ]
    results = forecast_for_room(
        room_id, site_id, now, room_df, predicted_phases, ForecastVariant.BAYESIAN_STATIC_FORECAST
    )

    assert len(results.forecasts) == 2
    assert results.forecasts[0].forecast_start_time == datetime(2022, 10, 1, 9, 45, tzinfo=la_time)
    assert results.forecasts[0].forecast_end_time == datetime(2022, 10, 1, 11, 45, tzinfo=la_time)
    assert results.forecasts[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results.forecasts[1].forecast_start_time == datetime(2022, 10, 1, 12, 25, tzinfo=la_time)
    assert results.forecasts[1].forecast_end_time == datetime(2022, 10, 1, 13, 25, tzinfo=la_time)
