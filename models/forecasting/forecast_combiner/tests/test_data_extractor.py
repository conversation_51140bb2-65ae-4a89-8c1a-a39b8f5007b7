from datetime import datetime
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pandas as pd
from apella_cloud_api.new_client_schema import GQLCaseMatchingStatus

from forecast_combiner.data_extractor import (
    get_scheduled_cases_for_site,
)


def _get_mock_graphql_scheduled_case_node(
    case_id: str,
    case_status: str,
    case_matching_status: GQLCaseMatchingStatus,
    first_primary_procedure: str,
    first_primary_surgeon: str,
    org_id: str,
    preceding_case_id: str = "",
) -> MagicMock:
    return MagicMock(
        node=MagicMock(
            id=case_id,
            scheduled_start_time=datetime(2024, 1, 1),
            scheduled_end_time=datetime(2024, 1, 2),
            room=MagicMock(id="unit_test_room"),
            site=MagicMock(id="unit_test_site", organization_id=org_id),
            status=case_status,
            case_matching_status=case_matching_status,
            case_staff=[MagicMock(staff=MagicMock(name=first_primary_surgeon))],
            primary_case_procedures=[MagicMock(procedure=MagicMock(name=first_primary_procedure))],
            preceding_case=MagicMock(id=preceding_case_id),
        )
    )


@patch("forecast_combiner.data_extractor.retry_call")
def test_get_scheduled_cases_for_site_has_cases(
    mock_retry_call: Mock,
) -> None:
    mock_apella_client = MagicMock()
    mock_graphql_edges = [
        _get_mock_graphql_scheduled_case_node(
            case_id="case_1",
            case_status="cancelled",
            case_matching_status=GQLCaseMatchingStatus.CANCELED,
            first_primary_procedure="TestA",
            first_primary_surgeon="Johnny Tsunmai",
            org_id="OrgA",
        ),
        _get_mock_graphql_scheduled_case_node(
            case_id="case_2",
            case_status="something else",
            case_matching_status=GQLCaseMatchingStatus.NOT_A_CASE,
            first_primary_procedure="TestB",
            first_primary_surgeon="Joey Baloney",
            org_id="OrgA",
        ),
        _get_mock_graphql_scheduled_case_node(
            case_id="case_3",
            case_status="scheduled",
            case_matching_status=GQLCaseMatchingStatus.AUTOMATIC,
            first_primary_procedure="TestA",
            first_primary_surgeon="Johnny Tsunmai",
            org_id="OrgA",
        ),
        _get_mock_graphql_scheduled_case_node(
            case_id="case_4",
            case_status="just added",
            case_matching_status=GQLCaseMatchingStatus.OVERRIDE,
            first_primary_procedure="TestB",
            first_primary_surgeon="Joey Baloney",
            org_id="OrgA",
            preceding_case_id="case_3",
        ),
    ]

    mock_apella_client.query_graphql_from_schema.return_value = MagicMock(
        cases=MagicMock(edges=mock_graphql_edges)
    )

    mock_retry_call.return_value = mock_apella_client.query_graphql_from_schema.return_value

    result = get_scheduled_cases_for_site(
        mock_apella_client, datetime.now(), datetime.now(), site_id="unit_test_site"
    )

    mock_retry_call.assert_called_once()

    dict_list = [
        {
            "case_id": edge.node.id,
            "scheduled_start_time": edge.node.scheduled_start_time,
            "scheduled_end_time": edge.node.scheduled_end_time,
            "room_id": edge.node.room.id,
            "site_id": edge.node.site.id,
            "status": edge.node.status,
            "preceding_case_id": "",
        }
        for edge in mock_graphql_edges[-2:]
    ]
    dict_list[1]["preceding_case_id"] = dict_list[0]["case_id"]
    expected_result = pd.DataFrame(dict_list)

    assert result is not None and len(result) == 2
    # more readable than the standard DataFrame.equals in the logs, in case of failure
    assert result.to_dict() == expected_result.to_dict()


@patch("forecast_combiner.data_extractor.retry_call")
def test_get_scheduled_cases_for_site_no_cases(
    mock_retry_call: Mock,
) -> None:
    mock_apella_client = MagicMock()

    mock_apella_client.query_graphql_from_schema.return_value = MagicMock(cases=MagicMock(edges=[]))

    mock_retry_call.return_value = mock_apella_client.query_graphql_from_schema.return_value

    result = get_scheduled_cases_for_site(
        mock_apella_client, datetime.now(), datetime.now(), site_id="unit_test_site"
    )

    mock_retry_call.assert_called_once()

    assert result is None
