import asyncio
import os
from asyncio import TimeoutError
from datetime import datetime
from typing import Any
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pandas as pd
import pytest
from aiohttp import ClientError
from feature_store.feature_store import LoadFeaturesResult
from pandas import Timestamp
from pandera import DataFrameModel

from app.endpoints.predict.predict_api import get_series_median
from forecast_combiner.sub_service_fetcher.many_case_sub_service_fetcher import (
    ManyCaseSubServiceFetcher,
)
from tests.test_helpers import get_mock_future


class MockFloatResultModel(DataFrameModel):
    random_field: float
    case_id: str


class MockDatetimeResultModel(DataFrameModel):
    random_field: pd.Timestamp
    case_id: str


class MockMedianResultModel(DataFrameModel):
    random_field: float
    case_id: str


def _get_mock_http_client(
    case_ids: list[str],
    results: list[Any] | pd.Series,  # type: ignore
) -> tuple[<PERSON>Mock, MagicMock]:
    mock_http_client = MagicMock()
    mock_response = MagicMock()

    mock_response.json.return_value = get_mock_future(
        [{"prediction": results[i], "case_id": case_id} for i, case_id in enumerate(case_ids)]
    )

    mock_post = MagicMock()
    mock_post.__aenter__.return_value = mock_response

    mock_http_client.post.return_value = mock_post

    return mock_http_client, mock_response


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_sub_service_fetch(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(case_ids, [1, 2])

    mock_retry_client.return_value = mock_client
    now = datetime(2023, 10, 1, 12, 0, 0)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockFloatResultModel](
            entities=pd.DataFrame({"random_field": [], "case_id": []}),  # type: ignore
            bigtable_timestamps=pd.DataFrame(),
            unknown_entities=set(case_ids),
            entities_with_missing_features=set(case_ids),
            missing_features_count={"random_field": len(case_ids)},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )
    df = await fetcher.fetch(case_ids, now)

    mock_exponential_retry.assert_called_once_with(
        attempts=2,
        start_timeout=1.0,
        max_timeout=30.0,
        retry_all_server_errors=True,
        exceptions={TimeoutError, ClientError},
    )

    mock_retry_client.assert_called_once_with(
        retry_options=mock_exponential_retry.return_value, client_session=mock_session
    )

    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )

    assert mock_response.raise_for_status.call_count == 1

    assert (
        df.to_dict()
        == pd.DataFrame(
            [
                {"random_field": 1, "case_id": "A"},
                {"random_field": 2, "case_id": "B"},
            ]
        ).to_dict()
    )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_sub_service_fetch_with_dt_conversion(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(
        case_ids, ["2022-01-01T00:00:00Z", "2022-01-02T00:00:00Z"]
    )

    mock_retry_client.return_value = mock_client

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockDatetimeResultModel](
            entities=pd.DataFrame({"random_field": [], "case_id": []}),  # type: ignore
            bigtable_timestamps=pd.DataFrame(),
            unknown_entities=set(case_ids),
            entities_with_missing_features=set(case_ids),
            missing_features_count={"random_field": len(case_ids)},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockDatetimeResultModel,
            converter_function=pd.to_datetime,
        )

    df = await fetcher.fetch(case_ids, datetime.now())

    mock_exponential_retry.assert_called_once_with(
        attempts=2,
        start_timeout=1.0,
        max_timeout=30.0,
        retry_all_server_errors=True,
        exceptions={TimeoutError, ClientError},
    )

    mock_retry_client.assert_called_once_with(
        retry_options=mock_exponential_retry.return_value, client_session=mock_session
    )

    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )

    assert mock_response.raise_for_status.call_count == 1

    assert (
        df.to_dict()
        == pd.DataFrame(
            [
                {"random_field": Timestamp("2022-01-01 00:00:00+0000", tz="UTC"), "case_id": "A"},
                {"random_field": Timestamp("2022-01-02 00:00:00+0000", tz="UTC"), "case_id": "B"},
            ]
        ).to_dict()
    )


def create_mock_bigtable_timestamps(
    feature_names: list[str], case_ids: list[str], timestamp_offset: int
) -> pd.DataFrame:
    """
    Creates a mock bigtable_timestamps DataFrame.

    Args:
        feature_names (list[str]): List of feature names.
        case_ids (list[str]): List of case IDs.
        timestamp_offset (int): Seconds to subtract from current time for each timestamp.

    Returns:
        pd.DataFrame: Mocked bigtable_timestamps DataFrame.
    """
    data = {}
    now = datetime(2023, 10, 1, 12, 0, 0)
    for feature in feature_names:
        column_name = f"{feature}_bigtable_timestamp"
        # Assign the same timestamp offset for simplicity
        data[column_name] = [now.timestamp() - timestamp_offset for _ in case_ids]
    return pd.DataFrame(data, index=case_ids)


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_sub_service_fetch_with_median(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    case_ids = ["A", "B", "C", "D"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(
        case_ids, pd.Series([None, pd.Series([1, 2, 3]), pd.Series([4, 5, 6, 7]), pd.Series([])])
    )

    mock_retry_client.return_value = mock_client

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockMedianResultModel](
            entities=pd.DataFrame({"random_field": [], "case_id": []}),  # type: ignore
            bigtable_timestamps=pd.DataFrame(),
            unknown_entities=set(case_ids),
            entities_with_missing_features=set(case_ids),
            missing_features_count={"random_field": len(case_ids)},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockMedianResultModel,
            converter_function=get_series_median,
        )
    df = await fetcher.fetch(case_ids, datetime.now())

    mock_exponential_retry.assert_called_once_with(
        attempts=2,
        start_timeout=1.0,
        max_timeout=30.0,
        retry_all_server_errors=True,
        exceptions={TimeoutError, ClientError},
    )

    mock_retry_client.assert_called_once_with(
        retry_options=mock_exponential_retry.return_value, client_session=mock_session
    )

    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )

    assert mock_response.raise_for_status.call_count == 1
    assert (
        df.drop(["random_field_original"], axis=1).to_dict()
        == pd.DataFrame(
            [
                {"random_field": pd.NA, "case_id": "A"},
                {"random_field": 2.0, "case_id": "B"},
                {"random_field": 5.5, "case_id": "C"},
                {"random_field": pd.NA, "case_id": "D"},
            ]
        ).to_dict()
    )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_use_feature_store_if_results_are_recent(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    """
    Verifies that sub_serviceFetcher uses feature store results if they are recent enough,
    and does not make an HTTP request.
    """
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    # No HTTP request should be made, so no need to mock it
    mock_retry_client.return_value = MagicMock()

    now = datetime(2023, 10, 1, 12, 0, 0)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method to return recent data
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockFloatResultModel](
            entities=pd.DataFrame(  # type: ignore
                [
                    {"random_field": 1.5, "case_id": "A"},
                    {"random_field": 2.5, "case_id": "B"},
                ]
            ).set_index("case_id"),
            bigtable_timestamps=pd.DataFrame(
                {
                    "random_field_bigtable_timestamp": [
                        now.timestamp() - 30,  # Less than 60 seconds old
                        now.timestamp() - 30,
                    ]
                },
                index=case_ids,
            ),
            unknown_entities=set(),
            entities_with_missing_features=set(),
            missing_features_count={},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )
    df = await fetcher.fetch(case_ids, now)

    # Ensure that no HTTP request was made
    mock_retry_client.return_value.post.assert_not_called()

    # Ensure the data returned is from the feature store
    assert (
        df.reset_index(drop=True).to_dict()
        == pd.DataFrame(
            [
                {"random_field": 1.5, "case_id": "A"},
                {"random_field": 2.5, "case_id": "B"},
            ]
        )
        .reset_index(drop=True)
        .to_dict()
    )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_http_failure_with_sufficient_feature_store_data(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    """
    Verifies that sub_serviceFetcher uses feature store results if the HTTP request fails
    and the feature store has sufficient data.
    """
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(case_ids, [1, 2])

    mock_retry_client.return_value = mock_client

    # Current time
    now = datetime(2023, 10, 1, 12, 0, 0)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method to return sufficient and recent data
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockFloatResultModel](
            entities=pd.DataFrame(  # type: ignore
                [
                    {"random_field": 1.5, "case_id": "A"},
                    {"random_field": 2.5, "case_id": "B"},
                ]
            ).set_index("case_id"),
            bigtable_timestamps=pd.DataFrame(
                {
                    "random_field_bigtable_timestamp": [
                        now.timestamp() - 120,  # Greater than 60 seconds old
                        now.timestamp() - 120,
                    ]
                },
                index=case_ids,
            ),
            unknown_entities=set(),
            entities_with_missing_features=set(),
            missing_features_count={},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        # Configure the mock HTTP client to raise a ClientError
        mock_response.raise_for_status.side_effect = ClientError("HTTP request failed")

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )
    df = await fetcher.fetch(case_ids, now)

    # Ensure that the HTTP request was attempted
    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )

    # Ensure that despite the HTTP failure, data from feature store is used
    assert (
        df.reset_index(drop=True).to_dict()
        == pd.DataFrame(
            [
                {"random_field": 1.5, "case_id": "A"},
                {"random_field": 2.5, "case_id": "B"},
            ]
        )
        .reset_index(drop=True)
        .to_dict()
    )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_http_failure_with_incorrect_feature_store_data(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    """
    Verifies that sub_serviceFetcher uses feature store results if the HTTP request fails
    and the feature store has sufficient data.
    """
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(case_ids, [1, 2])

    mock_retry_client.return_value = mock_client

    # Current time
    now = datetime(2023, 10, 1, 12, 0, 0)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        # Mock the FeatureStore's load_features method to return sufficient and recent data
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockFloatResultModel](
            entities=pd.DataFrame(  # type: ignore
                [
                    {"random_field": 1.5, "case_id": "A"},
                    {"random_field": 2.5, "case_id": "C"},
                ]
            ).set_index("case_id"),
            bigtable_timestamps=pd.DataFrame(
                {
                    "random_field_bigtable_timestamp": [
                        now.timestamp() - 120,  # Greater than 60 seconds old
                        now.timestamp() - 120,
                    ]
                },
                index=["A", "C"],
            ),
            unknown_entities=set(),
            entities_with_missing_features=set(),
            missing_features_count={},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        # Configure the mock HTTP client to raise a ClientError
        mock_response.raise_for_status.side_effect = ClientError("HTTP request failed")

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )

    with pytest.raises(AssertionError, match="We lack the needed data to fallback"):
        await fetcher.fetch(case_ids, now)

    # Ensure that the HTTP request was attempted
    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_http_failure_with_insufficient_feature_store_data(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
) -> None:
    """
    Verifies that sub_serviceFetcher raises an error if the HTTP request fails
    and the feature store does not have sufficient data.
    """
    case_ids = ["A", "B"]
    service_url = "www.awesome-apella-forecasing.com"
    prediction_field = "random_field"

    mock_session = Mock()

    mock_client, mock_response = _get_mock_http_client(case_ids, [1, 2])

    mock_retry_client.return_value = mock_client

    # Current time
    now = datetime(2023, 10, 1, 12, 0, 0)

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
        },
    ):
        # Mock the FeatureStore's load_features method to return insufficient data
        mock_feature_store_instance = mock_feature_store_class.return_value
        mock_load_features_result = LoadFeaturesResult[MockFloatResultModel](
            entities=pd.DataFrame([{"random_field": 1.5, "case_id": "A"}]).set_index(  # type: ignore
                "case_id"
            ),  # Missing case_id "B"
            bigtable_timestamps=pd.DataFrame(
                {
                    "random_field_bigtable_timestamp": [
                        now.timestamp() - 120,  # Older than 60 seconds
                    ]
                },
                index=["A"],
            ),
            unknown_entities={"B"},  # 'B' is unknown
            entities_with_missing_features=set(),
            missing_features_count={},
        )
        mock_feature_store_instance.load_features.return_value = mock_load_features_result

        # Configure the mock HTTP client to raise a ClientError
        mock_response.raise_for_status.side_effect = ClientError("HTTP request failed")

        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )

        with pytest.raises(AssertionError, match="We lack the needed data to fallback"):
            await fetcher.fetch(case_ids, now)

        # Ensure that the HTTP request was attempted
        mock_client.post.assert_called_once_with(
            service_url,
            json=fetcher._get_request_body(case_ids),
            headers={
                "x-b3-traceid": "0",
                "x-b3-spanid": "0",
                "x-b3-sampled": "0",
                "x-apella-dagster-run-id": "",
            },
        )


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.logger")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_sub_service_fetch_with_load_features_exception(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_feature_store_class: Mock,
    mock_bt_client: Mock,
    mock_logger: Mock,
) -> None:
    """
    Verifies that sub_serviceFetcher can still succeed when an exception occurs in load_features.
    """
    # Define test inputs
    case_ids = ["X", "Y"]
    service_url = "www.test-forecast-service.com"
    prediction_field = "random_field"

    mock_session = Mock()
    mock_client, mock_response = _get_mock_http_client(case_ids, [10, 20])
    mock_retry_client.return_value = mock_client
    now = datetime(2023, 10, 1, 12, 0, 0)

    # Configure FeatureStore to raise an exception
    mock_feature_store_instance = mock_feature_store_class.return_value
    mock_feature_store_instance.load_features.side_effect = Exception("Feature store failure")

    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ):
        fetcher = ManyCaseSubServiceFetcher(
            mock_session,
            service_url,
            prediction_field,
            {"random_field": "random_field"},
            result_model=MockFloatResultModel,
        )

    # Invoke fetch
    df = await fetcher.fetch(case_ids, now)

    # Assertions
    mock_exponential_retry.assert_called_once_with(
        attempts=2,
        start_timeout=1.0,
        max_timeout=30.0,
        retry_all_server_errors=True,
        exceptions={TimeoutError, ClientError},
    )
    mock_retry_client.assert_called_once_with(
        retry_options=mock_exponential_retry.return_value, client_session=mock_session
    )
    mock_client.post.assert_called_once_with(
        service_url,
        json=fetcher._get_request_body(case_ids),
        headers={
            "x-b3-traceid": "0",
            "x-b3-spanid": "0",
            "x-b3-sampled": "0",
            "x-apella-dagster-run-id": "",
        },
    )

    # Verify the returned DataFrame matches the expected output
    expected_df = pd.DataFrame(
        [
            {"random_field": 10, "case_id": "X"},
            {"random_field": 20, "case_id": "Y"},
        ]
    )
    # Verify error was logged
    mock_logger.error.assert_called_with("Error Feature store failure fetching from Bigtable")

    mock_feature_store_instance.load_features.assert_called_once()
    assert df.to_dict() == expected_df.to_dict()


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_per_case_subservice_fetcher_limits_concurrent_workers(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_bt_client: Mock,
    mock_feature_store_class: Mock,
) -> None:
    from forecast_combiner.sub_service_fetcher.per_case_sub_service_fetcher import (
        PerCaseSubServiceFetcher,
    )

    case_ids = ["A", "B", "C", "D", "E"]
    service_url = "http://test-service.com"
    prediction_field = "prediction_field"

    mock_session = MagicMock()
    mock_result_model = MagicMock()

    # Define worker count
    worker_count = 2

    # Initialize the fetcher with a limited number of workers
    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
            "WORKER_COUNT": str(worker_count),
        },
    ):
        fetcher = PerCaseSubServiceFetcher(
            http_session=mock_session,
            service_url=service_url,
            prediction_field=prediction_field,
            feature_map={"prediction_field": "prediction_field"},
            result_model=mock_result_model,
            converter_function=None,
        )

    # Counter to track concurrent calls
    concurrent_calls = 0
    max_concurrent_calls = 0
    lock = asyncio.Lock()

    async def mock_call_submodel(case_id: str, run_id: str | None = None) -> list[dict[str, Any]]:
        nonlocal concurrent_calls, max_concurrent_calls
        async with lock:
            concurrent_calls += 1
            max_concurrent_calls = max(concurrent_calls, max_concurrent_calls)
        await asyncio.sleep(0.1)  # Simulate network delay
        async with lock:
            concurrent_calls -= 1
        return [{"prediction_field": 1, "case_id": case_id}]

    # Patch the _call_submodel method
    fetcher._call_sub_service = mock_call_submodel  # type: ignore

    # Call the fetch method
    await fetcher.fetch(case_ids, datetime.now(), run_id="test_run_id")

    # Assert that the maximum number of concurrent calls does not exceed worker_count
    assert (
        max_concurrent_calls <= worker_count
    ), f"Max concurrent calls {max_concurrent_calls} exceeded worker count {worker_count}"


@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.FeatureStore")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.BTClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.RetryClient")
@patch("forecast_combiner.sub_service_fetcher.sub_service_fetcher.ExponentialRetry")
@pytest.mark.asyncio
async def test_per_case_subservice_fetcher_executes_async_calls(
    mock_exponential_retry: Mock,
    mock_retry_client: Mock,
    mock_bt_client: Mock,
    mock_feature_store_class: Mock,
) -> None:
    from forecast_combiner.sub_service_fetcher.per_case_sub_service_fetcher import (
        PerCaseSubServiceFetcher,
    )

    case_ids = ["A", "B", "C", "D", "E"]
    service_url = "http://test-service.com"
    prediction_field = "prediction_field"

    mock_session = MagicMock()
    mock_result_model = MagicMock()

    # Define worker count
    worker_count = 5  # Set equal to number of case_ids for full concurrency

    # Initialize the fetcher with a limited number of workers

    # Initialize the fetcher with a limited number of workers
    with patch.object(
        os,
        "environ",
        {
            "PROJECT_VERSION": "forecast-combiner-v0.1.2",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
            "WORKER_COUNT": str(worker_count),
        },
    ):
        fetcher = PerCaseSubServiceFetcher(
            http_session=mock_session,
            service_url=service_url,
            prediction_field=prediction_field,
            feature_map={"prediction_field": "prediction_field"},
            result_model=mock_result_model,
            converter_function=None,
        )

    call_start_times = []
    call_end_times = []

    async def mock_call_submodel(case_id: str, run_id: str | None = None) -> list[dict[str, Any]]:
        call_start_times.append(asyncio.get_event_loop().time())
        await asyncio.sleep(0.1)  # Simulate network delay
        call_end_times.append(asyncio.get_event_loop().time())
        return [{"prediction_field": 1, "case_id": case_id}]

    # Patch the _call_submodel method
    fetcher._call_sub_service = mock_call_submodel  # type: ignore

    # Record the total time taken for fetch
    start_time = asyncio.get_event_loop().time()
    await fetcher.fetch(case_ids, datetime.now(), run_id="test_run_id")
    end_time = asyncio.get_event_loop().time()

    total_time = end_time - start_time

    # Since all calls are async and sleep for 0.1s, total_time should be slightly more than 0.1s
    assert total_time < 0.3, f"Fetch did not execute asynchronously, took {total_time} seconds"

    # Additionally, verify that all calls started before any call ended
    assert all(
        start < end for start in call_start_times for end in call_end_times if end > start
    ), "Calls did not overlap as expected for asynchronous execution"
