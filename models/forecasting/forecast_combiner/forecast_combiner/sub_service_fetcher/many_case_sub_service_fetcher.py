from typing import Any

from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.tracing import get_request_tracing_headers

from forecast_combiner.sub_service_fetcher.sub_service_fetcher import SubServiceFetcher


class ManyCaseSubServiceFetcher(SubServiceFetcher):
    """
    This class is used to fetch data from a subservice for multiple cases at once.
    It inherits from the SubServiceFetcher class and overrides the _fetch_data_from_sub_service method.
    """

    async def _fetch_data_from_sub_service(
        self,
        case_ids: list[str],
        run_id: str | None = None,
    ) -> list[dict[str, Any]]:
        """
        This function fetches data from the subservice for a list of case IDs.

        Args:
            case_ids (list[str]): The list of case IDs to fetch data for.
        Returns:
            list[dict[str, Any]]: The response from the submodel.
        """
        async with self.http_client.post(
            self.service_url,
            json=self._get_request_body(case_ids),
            headers={
                **get_request_tracing_headers(),
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id or "",
            },
        ) as response:
            response.raise_for_status()
            return await response.json()  # type: ignore
