import datetime
import os
from abc import ABC, abstractmethod
from asyncio.exceptions import Timeout<PERSON>rror
from typing import Any, Callable

import pandas as pd
import pandera as pa
from aiohttp import ClientError, ClientSession
from aiohttp_retry import ExponentialRetry, RetryClient
from feature_store import FeatureStore
from google.cloud.bigtable import Client as BTClient
from serving_utils.setup_json_logger import setup_json_logger

from forecast_combiner.features import ForecastResultSchema

logger = setup_json_logger()

# To Test, run the script for setting up the ports


class SubServiceFetcher(ABC):
    service_url: str
    prediction_column: str
    http_client: RetryClient
    feature_list_map: dict[str, str]
    feature_store: FeatureStore[ForecastResultSchema] | None
    result_model: type[pa.DataFrameModel]
    converter_function: Callable[[pd.Series], pd.Series] | None = None  # type: ignore

    def __init__(
        self,
        http_session: ClientSession,
        service_url: str,
        prediction_field: str,
        feature_map: dict[str, str],
        result_model: type[pa.DataFrameModel],
        converter_function: Callable[[pd.Series], pd.Series] | None = None,  # type: ignore
    ) -> None:
        self.service_url = service_url
        self.prediction_field = prediction_field

        self.http_client = self._get_http_client(http_session)
        self.feature_list_map = feature_map
        self.converter_function = converter_function
        self.result_model = result_model
        if (
            os.getenv("FEATURE_STORE_PROJECT") is not None
            and os.getenv("FEATURE_STORE_INSTANCE") is not None
        ):
            bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
            instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])
            self.feature_store = FeatureStore(
                instance, "results", ForecastResultSchema, prefix="ml_forecast"
            )
        else:
            self.feature_store = None

    def _get_http_client(self, http_session: ClientSession) -> RetryClient:
        retry_options = ExponentialRetry(
            attempts=2,  # Since we have a fallback let's not retry too much
            start_timeout=1.0,  # models are slow when there are many calls
            max_timeout=30.0,  # forecast combiner has a 30s timeout anyway
            retry_all_server_errors=True,
            exceptions={TimeoutError, ClientError},
        )

        return RetryClient(retry_options=retry_options, client_session=http_session)

    def _convert_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert the dataframe to the correct types based on the converter function.
        """
        try:
            if self.converter_function is not None:
                original_values = df[self.prediction_field]

                if self.converter_function is pd.to_datetime:
                    df[self.prediction_field] = pd.to_datetime(
                        df[self.prediction_field],
                        format="%Y-%m-%dT%H:%M:%SZ",
                        errors="coerce",
                        utc=True,
                    )
                else:
                    df[f"{self.prediction_field}_original"] = df[self.prediction_field]
                    df[self.prediction_field] = self.converter_function(df[self.prediction_field])

                logger.debug(
                    f"Converting {self.prediction_field} from {original_values.head(10)} to {df[self.prediction_field].head(10)}"
                )
        except Exception as e:
            logger.error(
                f"Error {e} converting {self.prediction_field} using {self.converter_function}"
            )
        return df

    def _setup_default(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[list(self.feature_list_map.keys()) + ["case_id"]].rename(
            {k: v for k, v in self.feature_list_map.items()},
            axis=1,
        )

    def _get_dataframe(self, response_json: list[dict[Any, Any]]) -> pd.DataFrame:
        df = pd.DataFrame(response_json).rename(
            {
                "prediction": self.prediction_field,
                "prediction_tag": f"{self.prediction_field}_prediction_tag",
                "version": f"{self.prediction_field}_version",
                "service_version": f"{self.prediction_field}_service_version",
                "model_version": f"{self.prediction_field}_model_version",
            },
            axis=1,
        )
        # Remove the feature timestamps if they exist
        if "feature_timestamps" in df.columns:
            df.drop(["feature_timestamps"], axis=1, inplace=True)
        return df

    def _get_request_body(self, case_ids: list[str]) -> dict[str, list[Any]]:
        return {"inputs": [{"case_id": case_id} for case_id in case_ids]}

    @abstractmethod
    async def _fetch_data_from_sub_service(
        self, case_ids: list[str], run_id: str | None = None
    ) -> list[dict[str, Any]]: ...

    async def fetch(
        self,
        case_ids: list[str],
        now: datetime.datetime,
        run_id: str | None = None,
    ) -> pd.DataFrame:
        """
        Fetch data from the subservice for a list of case IDs.
        It uses asyncio to run the requests concurrently

        It also checks if the data is already in the feature store and if it is recent enough
        to use instead of fetching from the subservice.
        If the data is not in the feature store or is too old, it fetches the data from the subservice.
        If the data is not available from the subservice, it tries to fall back to using the existing forecast results

        Args:
            case_ids (list[str]): The list of case IDs to fetch data for.
            now (datetime.datetime): The current time.
        Returns:
            pd.DataFrame: The response from the submodel.
        """
        case_ids_set = set(case_ids)
        existing_forecast_results = pd.DataFrame()
        try:
            if self.feature_store is not None:
                forecast_results = self.feature_store.load_features(
                    set(case_ids),
                    self.result_model,
                )

                # If the results are recent enough, less than 60s old, we can use the existing results
                existing_forecast_results = forecast_results.entities
                existing_forecast_results["case_id"] = existing_forecast_results.index
                existing_forecasting_ids = set(existing_forecast_results.index.to_list())
                if existing_forecasting_ids == case_ids_set:
                    timestamps = forecast_results.bigtable_timestamps
                    min_timestamp = (
                        timestamps[
                            [
                                f"{column_name}_bigtable_timestamp"
                                for column_name in self.feature_list_map.keys()
                            ]
                        ]
                        .min()
                        .min()
                    )
                    if now.timestamp() - min_timestamp < 60:
                        logger.info(
                            f"Using existing forecast results from Bigtable for {self.service_url}"
                        )
                        return self._convert_dataframe(
                            self._setup_default(existing_forecast_results)
                        )
            else:
                existing_forecasting_ids = set()
        except Exception as e:
            logger.error(f"Error {e} fetching from Bigtable")
            existing_forecasting_ids = set()

        try:
            results = await self._fetch_data_from_sub_service(case_ids, run_id)
            df = self._get_dataframe(results)

        except (ClientError, ConnectionError, TimeoutError) as e:
            logger.error(f"Error {e} fetching from {self.service_url}")

            assert existing_forecasting_ids == case_ids_set, "We lack the needed data to fallback"

            # This prevents us from totally failing to forecast if a single submodel fails
            df = self._setup_default(existing_forecast_results)

        df = self._convert_dataframe(df)
        assert len(df) == len(case_ids)

        return df
