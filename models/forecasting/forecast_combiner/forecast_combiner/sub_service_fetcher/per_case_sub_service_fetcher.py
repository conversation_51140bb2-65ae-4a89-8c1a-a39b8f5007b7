import asyncio
from asyncio import BoundedSemaphore
from typing import Any, Callable

import pandas as pd
import pandera as pa
import serving_utils.config as config
from aiohttp import ClientSession
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.tracing import get_request_tracing_headers

from forecast_combiner.sub_service_fetcher.sub_service_fetcher import SubServiceFetcher

logger = setup_json_logger()


class PerCaseSubServiceFetcher(SubServiceFetcher):
    """
    This class is used to fetch data from a subservice for a single case at a time.
    It inherits from the SubServiceFetcher class and overrides the _call_sub_service method.
    It uses a semaphore to limit the number of concurrent requests to the subservice.
    """

    semaphores: BoundedSemaphore

    def __init__(
        self,
        http_session: ClientSession,
        service_url: str,
        prediction_field: str,
        feature_map: dict[str, str],
        result_model: type[pa.DataFrameModel],
        converter_function: Callable[[pd.Series], pd.Series] | None = None,  # type: ignore
    ) -> None:
        super().__init__(
            http_session,
            service_url,
            prediction_field,
            feature_map,
            result_model,
            converter_function,
        )
        self.semaphores = asyncio.BoundedSemaphore(config.get_worker_count())

    async def _call_sub_service(
        self,
        case_id: str,
        run_id: str | None = None,
    ) -> list[dict[str, Any]]:
        """
        This function calls the subservice for a given case ID.
        It uses the aiohttp library to make an asynchronous HTTP POST request to the subservice.

        Args:
            case_id (str): The case ID to fetch data for.
        Returns:
            list[dict[str, Any]]: The response from the submodel.
        """
        async with self.http_client.post(
            self.service_url,
            json=self._get_request_body([case_id]),
            headers={
                **get_request_tracing_headers(),
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id or "",
            },
        ) as response:
            response.raise_for_status()
            return await response.json()  # type: ignore

    async def _limit_concurrency(
        self,
        case_id: str,
        run_id: str | None = None,
    ) -> list[dict[str, Any]]:
        """
        This function limits the concurrency of the requests to the subservice.
        It uses a semaphore to limit the number of concurrent requests.

        Args:
            case_id (str): The case ID to fetch data for.

        Returns:
            list[dict[str, Any]]: The response from the submodel.
        """
        async with self.semaphores:
            return await self._call_sub_service(case_id, run_id)

    async def _fetch_data_from_sub_service(
        self, case_ids: list[str], run_id: str | None = None
    ) -> list[dict[str, Any]]:
        """
        This function fetches data from the subservice for a list of case IDs.
        It uses asyncio to run the requests concurrently, limiting the number of concurrent requests
        using a semaphore.
        Args:
            case_ids (list[str]): The list of case IDs to fetch data for.
        Returns:
            list[dict[str, Any]]: The response from the submodel.
        """
        tasks = [self._limit_concurrency(case_id, run_id) for case_id in case_ids]
        results = await asyncio.gather(*tasks)
        flattened_results = []
        for result in results:
            flattened_results.extend(result)
        return flattened_results
