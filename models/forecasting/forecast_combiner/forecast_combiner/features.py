import pandera as pa
from feature_store.feature_store import FeatureStoreDateTime


class ForecastResultSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    static_start_offset_forecast: float | None = pa.Field(nullable=True)
    static_start_offset_version: str | None = pa.Field(nullable=True)
    static_start_offset_service_version: str | None = pa.Field(nullable=True)
    static_start_offset_model_version: str | None = pa.Field(nullable=True)

    turnover_duration_forecast: float | None = pa.Field(nullable=True)
    turnover_duration_version: str | None = pa.Field(nullable=True)
    turnover_duration_service_version: str | None = pa.Field(nullable=True)
    turnover_duration_model_version: str | None = pa.Field(nullable=True)

    pythia_duration_forecast: float | None = pa.Field(nullable=True)
    pythia_duration_version: str | None = pa.Field(nullable=True)
    pythia_duration_service_version: str | None = pa.Field(nullable=True)
    pythia_duration_model_version: str | None = pa.Field(nullable=True)
    pythia_forecast_tag: str | None = pa.Field(nullable=True)

    bayesian_duration_forecast: list[float] | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_version: str | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_service_version: str | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_model_version: str | None = pa.Field(
        nullable=True,
    )

    patient_wheels_out_forecast: FeatureStoreDateTime | None = pa.Field(nullable=True)
    patient_wheels_out_version: str | None = pa.Field(nullable=True)
    patient_wheels_out_service_version: str | None = pa.Field(nullable=True)
    patient_wheels_out_model_version: str | None = pa.Field(nullable=True)


class StaticStartOffsetQuerySchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    static_start_offset_forecast: float | None = pa.Field(nullable=True)
    static_start_offset_version: str | None = pa.Field(nullable=True)
    static_start_offset_service_version: str | None = pa.Field(nullable=True)
    static_start_offset_model_version: str | None = pa.Field(nullable=True)


class TurnoverQuerySchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    turnover_duration_forecast: float | None = pa.Field(nullable=True)
    turnover_duration_version: str | None = pa.Field(nullable=True)
    turnover_duration_service_version: str | None = pa.Field(nullable=True)
    turnover_duration_model_version: str | None = pa.Field(nullable=True)


class PythiaQuerySchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    pythia_duration_forecast: float | None = pa.Field(nullable=True)
    pythia_duration_version: str | None = pa.Field(nullable=True)
    pythia_duration_service_version: str | None = pa.Field(nullable=True)
    pythia_duration_model_version: str | None = pa.Field(nullable=True)
    pythia_forecast_tag: str | None = pa.Field(nullable=True)


class BayesianStaticQuerySchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    bayesian_duration_forecast: list[float] | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_version: str | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_service_version: str | None = pa.Field(
        nullable=True,
    )
    bayesian_duration_model_version: str | None = pa.Field(
        nullable=True,
    )


class EventModelQuerySchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    patient_wheels_out_forecast: FeatureStoreDateTime | None = pa.Field(nullable=True)
    patient_wheels_out_version: str | None = pa.Field(nullable=True)
    patient_wheels_out_service_version: str | None = pa.Field(nullable=True)
    patient_wheels_out_model_version: str | None = pa.Field(nullable=True)
