from datetime import datetime
from typing import cast

import apella_cloud_api
import pandas as pd
from apella_cloud_api.new_api_server_schema import (
    ApellaSchema,
    gql__Query,
)
from apella_cloud_api.new_client_schema import (
    GQLCaseMatchingStatus,
    GQLPhaseStatus,
    GQLPhaseType,
    GQLQuery,
)
from apella_cloud_api.new_input_schema import GQLPhaseQueryInput, GQLScheduledCaseQueryInput
from apella_cloud_api.new_schema_generator.schema_generator_base_classes import (
    NotFound,
    ObjectField,
)
from prometheus_client import Counter
from retry.api import retry_call
from zoneinfo import ZoneInfo

from forecast_combiner.types import PhaseInfo

# Define the Prometheus Counter at the module level
API_CALLS_COUNTER = Counter(
    "forecast_combiner_api_calls_total",
    "Total number of API calls made by the forecast combiner",
    ["target_query"],  # Label to identify the specific query
)


def _query_graphql_from_schema(
    api_server_client: apella_cloud_api.Client,
    gql_query: ObjectField[gql__Query],
    label: str | None,
) -> GQLQuery:
    # Perform the retry logic
    result: GQLQuery = retry_call(
        lambda: api_server_client.query_graphql_from_schema(
            query=gql_query,
            label=label,  # Pass label to underlying call if it uses it
        ),
        tries=3,
        delay=0.1,
        backoff=2,
    )

    # Increment the counter after a successful call, if a label is provided
    if label:
        API_CALLS_COUNTER.labels(target_query=label).inc()

    return result


def get_scheduled_cases_for_site(
    api_server_client: apella_cloud_api.Client,
    start_of_day: datetime,
    end_of_day: datetime,
    site_id: str,
) -> pd.DataFrame | None:
    """
    Get cases from the API server based on provided filters
    """
    query = GQLScheduledCaseQueryInput(
        min_start_time=start_of_day,
        max_start_time=end_of_day,
        status="scheduled",
        site_ids=[site_id],
    )
    apella_schema = ApellaSchema()
    gql_query = apella_schema.Query.cases.args(query=query).select(
        apella_schema.ScheduledCaseConnection.edges.select(
            apella_schema.ScheduledCaseEdge.node.select(
                apella_schema.ScheduledCase.id,
                apella_schema.ScheduledCase.scheduled_start_time,
                apella_schema.ScheduledCase.scheduled_end_time,
                apella_schema.ScheduledCase.site.select(
                    apella_schema.Site.id,
                ),
                apella_schema.ScheduledCase.room.select(
                    apella_schema.Room.id,
                ),
                apella_schema.ScheduledCase.status,
                apella_schema.ScheduledCase.case_matching_status,
                apella_schema.ScheduledCase.preceding_case.select(
                    apella_schema.ScheduledCase.id,
                ),
            ),
        ),
    )

    results = _query_graphql_from_schema(
        api_server_client, gql_query, label="forecast_combiner__get_scheduled_cases_for_site"
    )

    # If the customer has marked a case as manually canceled or not a real case, we'll hide them
    # from the forecasting system. For the "not a case" case, there could be strange effects on the
    # schedule view if the "not a case" is mixed in with real cases in the same room. For now, the
    # intended use case for "not a case" is for surgical holds and other schedule items that should
    # still appear in the schedule pills but won't really be considered a "case" for the purposes of
    # Apella (i.e. there won't be a Live View for it).
    scheduled_cases = [
        edge.node
        for edge in results.cases.edges
        if edge.node.case_matching_status
        not in [GQLCaseMatchingStatus.CANCELED, GQLCaseMatchingStatus.NOT_A_CASE]
    ]

    # convert it to a dataframe
    data = []
    for scheduled_case in scheduled_cases:
        data.append(
            {
                "case_id": scheduled_case.id,
                "scheduled_start_time": scheduled_case.scheduled_start_time,
                "scheduled_end_time": scheduled_case.scheduled_end_time,
                "room_id": scheduled_case.room.id,
                "site_id": scheduled_case.site.id,
                "status": scheduled_case.status,
                "preceding_case_id": scheduled_case.preceding_case.id or "",
            }
        )

    return pd.DataFrame(data) if len(data) > 0 else None


def get_predicted_phases_for_site(
    api_server_client: apella_cloud_api.Client,
    start_of_day: datetime,
    end_of_day: datetime,
    site_id: str,
) -> list[PhaseInfo]:
    """
    Returns an unordered list of predicted phases for a given time range and site_id
    """
    # Find all valid predicted case phases (i.e. those of source_type unified) that started on the
    # day in question at the site in question.
    query = GQLPhaseQueryInput(
        min_start_time=start_of_day,
        max_start_time=end_of_day,
        site_ids=[site_id],
        source_type="unified",
        type=GQLPhaseType.CASE,
        statuses=[GQLPhaseStatus.VALID],
    )
    apella_schema = ApellaSchema()
    gql_query = apella_schema.Query.phases.args(query=query).select(
        apella_schema.PhaseConnection.edges.select(
            apella_schema.PhaseEdge.node.select(
                apella_schema.Phase.case.select(
                    apella_schema.ScheduledCase.id,
                    apella_schema.ScheduledCase.room.select(apella_schema.Room.id),
                ),
                apella_schema.Phase.start_time,
                apella_schema.Phase.end_time,
            )
        )
    )

    results = _query_graphql_from_schema(
        api_server_client,
        gql_query,
        label="forecast_combiner__get_predicted_phases_for_site",
    )

    # Filter out case phases which haven't yet been matched to a case
    phases = [
        phase.node for phase in results.phases.edges if not isinstance(phase.node.case, NotFound)
    ]

    # Note: we have to cast away the possibility of `NotFound` below where we know that there is
    # definitely a value
    return [
        PhaseInfo(
            cast(str, phase.case.id),
            cast(str, phase.case.room.id),
            phase.start_time,
            None if isinstance(phase.end_time, NotFound) else phase.end_time,
        )
        for phase in phases
    ]


def get_timezone_for_site(
    api_server_client: apella_cloud_api.Client,
    site_id: str,
) -> ZoneInfo | None:
    """
    Get the timezone for a site (if it exists)
    """
    apella_schema = ApellaSchema()
    label = "forecast_combiner__get_timezone_for_site"
    gql_query = apella_schema.Query.site.args(site_id).select(
        apella_schema.Site.id,
        apella_schema.Site.timezone,
    )

    # Use the helper function to make the call and increment the counter
    results = _query_graphql_from_schema(api_server_client, gql_query, label=label)

    if not isinstance(results.site.id, NotFound):
        return ZoneInfo(results.site.timezone)
    return None
