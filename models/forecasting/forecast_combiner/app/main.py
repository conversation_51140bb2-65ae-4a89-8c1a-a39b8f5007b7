"""
FastAPI application for the forecast combiner service
© Apella Inc 2025
"""

import os

from prometheus_fastapi_instrumentator import Instrumentator

from app.endpoints.create_api import create_api

app = create_api()
Instrumentator().instrument(app).expose(app)

if __name__ == "__main__":
    import uvicorn

    port = int(os.environ.get("PORT", 3000))
    uvicorn.run(app, host="0.0.0.0", port=port)
