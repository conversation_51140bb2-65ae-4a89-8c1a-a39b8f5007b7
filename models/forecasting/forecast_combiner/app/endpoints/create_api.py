"""
Defines the API endpoints for all operations
© Apella Inc 2025
"""

import logging
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

from fastapi import FastAP<PERSON>
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from serving_utils.timeout_middleware import timeout_middleware

from .predict.predict_api import ForecastCombinerService

# Configure the OTLP exporter. It reads endpoint configuration from environment variables
# (e.g., OTEL_EXPORTER_OTLP_TRACES_ENDPOINT). Defaults to http://localhost:4318/v1/traces.
otlp_exporter = OTLPSpanExporter()

# Define the service resource. OTEL_SERVICE_NAME env var can also be used.
resource = Resource(attributes={SERVICE_NAME: "forecast_combiner"})

# Create a TracerProvider with the resource and processor
tracer_provider = TracerProvider(resource=resource)
span_processor = BatchSpanProcessor(otlp_exporter)
tracer_provider.add_span_processor(span_processor)

# Set the global TracerProvider
trace.set_tracer_provider(tracer_provider)

# --- BEGIN WORKAROUND for uvicorn.access logging level ---
# Get the uvicorn access logger
access_logger = logging.getLogger("uvicorn.access")
# Set its level programmatically, overriding any defaults or potentially failed YAML config
access_logger.setLevel(logging.WARNING)
# --- END WORKAROUND ---

RequestsInstrumentor().instrument()
AioHttpClientInstrumentor().instrument()

VERSION = "v4"

# Instantiate the service once
forecast_combiner_service = ForecastCombinerService()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Manage the lifespan of the ForecastCombinerService session."""
    # Remove logger instantiation from here as it's now module level
    try:
        logger.info("Lifespan: Starting ForecastCombinerService session...")
        await forecast_combiner_service.start_session()
        logger.info("Lifespan: ForecastCombinerService session started.")
    except Exception as e:
        logger.error(f"Failed to start ForecastCombinerService session: {e}")
        raise

    yield

    try:
        logger.info("Lifespan: Closing ForecastCombinerService session...")
        await forecast_combiner_service.close_session()
        logger.info("Lifespan: ForecastCombinerService session closed.")
    except Exception as e:
        logger.error(f"Failed to close ForecastCombinerService session: {e}")
        # Don't re-raise here to ensure graceful shutdown


def create_api() -> FastAPI:
    """Creates the FastAPI application with all routes, instrumentation, and DI."""

    api: FastAPI = FastAPI(
        title="forecast_combiner",
        description="Forecast combiner service for predicting case schedule",
        lifespan=lifespan,
    )

    FastAPIInstrumentor.instrument_app(api)

    api.middleware("http")(timeout_middleware)

    # We're using include_router directly instead of add_fastapi_routes because:
    # 1. We've pre-instantiated the service (forecast_combiner_service) at module level
    # 2. add_fastapi_routes would try to instantiate a new service instance
    # 3. We need to use the same service instance that's managed by the lifespan context
    api.include_router(forecast_combiner_service.router)

    return api


__all__ = [
    "create_api",
]
