SHELL := /bin/bash
PORT := 9992

format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-training-locally:
	poetry run python -u -m dynamic_case_end.training --train --evaluate --config-module-name dynamic_case_end.configs.experiment_config

run-async-trainer:
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml

dev-local:
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-local:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd poetry run fastapi dev --port $(PORT) --reload