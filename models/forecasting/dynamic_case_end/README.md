# Pythia (Dynamic Case End, Simple Statistical Model)

The dynamic end model is a “simple statistical” approach to predicting the end time (wheels-out) of a case, intended to be used after the case wheeled in and before the case wheels out.

In this approach, we break the case down into subphases defined by these four events, 
and predict the duration of these individual subphases:
- patient_xfer_to_or_table
- patient_draped
- patient_undraped
- patient_xfer_to_bed


# Training
To train, we calculate the median subphase duration defined by these four events, 
per (first_primary_procedure, first_primary_surgeon, procedure_count). We also use the training data to find procedures that have high prevalence of 
duplicate events, so that we won’t predict on these procedures at inference.

Our predicted case end time is the sum of predicted subphase durations:
- If the subphase has not yet started, we predict the median phase duration calculated from training data
- If the subphase has started but the running duration is lower than the median from training data, we predict the median phase duration
- If the subphase has started and the running duration exceeded the median, we predict the running duration
- If the subphase has ended, we “predict” the actual/observed phase duration 

This simple approach assumes that there are exactly one of each event, occurring in the expected order. 
This assumption is crucial because the model adds together the phase durations of the sub-case-phases between
each of the four events. 

### Impact of duplicate events
If there are two patient_draped events, the order of events might look like:

wheels-in ---- patient_xfer_to_or_table ---- patient_draped ---- patient_undraped --- patient_draped ---- (etc)

At the time of the patient_undraped, we would be predicting that the case will end very soon, because by the time
the last (and usually only) undraping occurs, the actual surgery has completed. However, if there is a second patient_draped,
we would be greatly under-predicting the duration of the case because the model would be missing the second half
of the case. 

### Impact of events out of order
Similarly, if events do not occur in the expected order, as in this example:

wheels-in ---- patient_draped ---- patient_xfer_to_or_table ---- (etc)

When patient_draped is detected, the model will assume patient_xfer_to_or_table has already occurred,
resulting in an underestimate of the time needed to complete the case. If we allow the model to continue doing inference
when patient_xfer_to_or_table occurs, it would then begin overestimating time to end of case, because it would assume
that patient_draped has not yet occurred.


We clean the training data to ensure only one event per type and correct event ordering, but want to evaluate on all cases in the test set. 

# Inference

At inference, we have a few safeguards to “detect” duplicates, missing events, and events out of order. 
When one of these is detected, the statistical dynamic end model will return a null prediction and we will 
default to a heuristic (here, we use scheduled duration, but in prod we could default to the static case duration 
prediction). The safeguards are:

- Do not make predictions on procedures that tended to have duplicate events in training data. This will also eliminate cases that did not actually have duplicates in the test data, but will also include cases that did not have duplicates in the training data -- so it's not a perfect filter for duplicates.
- For the remaining cases that still have duplicate events, once a duplicate is seen we will predict based on the heuristic
- Cap the "running duration" of a phase to a certain multiple (e.g. 3x) the median phase duration. This can help with missing events. Once the "capped running duration" is activated, all of the following predictions will be based on the heuristic
- Do not predict if we calculate negative running durations (this can result from events out of order)

## Run locally

```
make run-local
```

Then you can make a request to the service by running the following command:

```
curl -X POST "http://localhost:9992/predict" -H 'Content-Type: application/json' -d '{"inputs": {"case_id": "0f5d5fd5-85a6-4ab4-aed1-a3e1f226e5ef"}}'
```

or visit the auto-generated landing page at http://localhost:9992/ to see the list of endpoints and examples of how to use them.

## Test making a call to the service in k8s

Configure your Kubectl to connect to the correct cluster:

* dev: `gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`
* prod: `gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b`

Get the list of contexts with the following command:
```
kubectl config get-contexts

# gke_dev-internal-b2aa9f_us-central1_dev-internal-gke
# gke_prod-internal-c5ac6b_us-central1_prod-internal-gke
```

Connect to the correct cluster using the following command:
```
kubectl config use-context gke_<project-id>_<region>_<cluster-name>
```

Create a tunnel so you can call the service from your machine. In this example, it'll make the
service available on localhost:9992:

```
kubectl -n model-dynamic-case-end port-forward service/model-dynamic-case-end 9992:80
```

Now you can use curl to call the service:
```
curl -X POST "http://localhost:9992/predict" -H 'Content-Type: application/json' -d '{"inputs": {"case_id": "0f5d5fd5-85a6-4ab4-aed1-a3e1f226e5ef"}}'
```

or visit the auto-generated landing page at http://localhost:9992/ to see the list of endpoints and examples of how to use them.
