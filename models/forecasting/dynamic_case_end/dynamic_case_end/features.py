import pandera as pa
from feature_store.feature_store import FeatureStoreDateTime


class PythiaSchema(pa.DataFrameModel):
    case_id: pa.typing.Index[str]

    org_id: str | None
    site_id: str | None
    first_primary_procedure: str | None
    first_primary_surgeon: str | None
    procedure_count: float | None
    static_forecasted_duration: float | None
    scheduled_duration: int | None
    actual_start_datetime_local: FeatureStoreDateTime | None
    first_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_patient_xfer_to_or_table_in_case: int | None = pa.Field(nullable=True)
    first_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_draped_in_case: int | None = pa.Field(nullable=True)
    first_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_undraped_in_case: int | None = pa.Field(nullable=True)
    first_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_xfer_to_bed_in_case: int | None = pa.Field(nullable=True)

    class Config:
        """Config with dataframe-level data type."""

        coerce = True  # this is required, otherwise a SchemaInitError is raised

        # Ensure that no extraneous or unexpected columns are provided
        strict = True


# Query and entity schemas have to be separate
class PythiaForecastFeatureSchema(pa.DataFrameModel):
    class Config:
        """Config with dataframe-level data type."""

        coerce = True  # this is required, otherwise a SchemaInitError is raised

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    bayesian_duration_median_forecast: float | None


class PythiaForecastFeatureQuerySchema(pa.DataFrameModel):
    class Config:
        """Config with dataframe-level data type."""

        coerce = True  # this is required, otherwise a SchemaInitError is raised

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    bayesian_duration_median_forecast: float | None


class PythiaResultSchema(pa.DataFrameModel):
    class Config:
        """Config with dataframe-level data type."""

        coerce = True  # this is required, otherwise a SchemaInitError is raised

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    pythia_duration_forecast: float | None
    pythia_duration_version: str
    pythia_duration_service_version: str
    pythia_duration_model_version: str
    pythia_forecast_tag: str
    # TODO: https://linear.app/apella/issue/FORC-81/add-trace-ids-to-forecast-results-schema
