# What this model is for

This model predicts a variety of turnovers (turnover_open, turnover_clean, total turnover before and after a case) as part of the scheduling tool. In the future, it could potentially be used to replace the start offset model in our static schedule forecasts.

To reduce the complexity of features needed for the scheduling tool, it only these features:
- first_primary_surgeon
- first_primary_procedure
- (optional) flip_room

# Model Description

The turnover training pipeline is fits random forest regressors to four turnover outcome variables.
Each row in the training and test sets corresponds to turnovers associated with a single case, which
here we will call "this" case. The four outcome variables are:

1) turnover_before_case: The active duration between actual end of previous case to actual start of "this" case
2) turnover_after_case: The active duration between actual end of "this" case to actual start of next case
3) turnover_open_before_case: The active duration between back table open of "this" case and the actual 
start of "this" case
4) turnover_clean_after_case: The active duration between actual end of "this" case and the back table 
open of the next case

The durations of these turnovers have been adjusted to only include "active" time (i.e., idle time has been removed).
Idle time is identified through minute-level aggregations of object model results. If there are no scrubbed or 
unscrubbed people in the room for five consecutive minutes, the whole stretch of zero-occupancy time is labeled as 
idle, and that stretch is subtracted from the turnover duration extracted from the DWH.

The size of the training and test sets will differ with outcome variable. This is because if a case does not have 
a back table, turnover_open_before_case and turnover_clean_after_case of the previous case will be null. 
Similarly, if "this" case is a first case, turnover_before_case will be null.


# Configurations

Configurations on how to split the data:
- locations_to_fit: a list of sites or org_ids to fit. Each location will have its own model.
- location_type_to_fit: either "site" or "org_id"

Configurations on how to eliminate idle time:

There are two ways to eliminate turnovers that are clearly dominated by idle time:
1) Heuristic: eliminate scheduled turnovers higher than some duration (*scheduled_turnover_duration_limit*)
- This heuristic can be applied to only training, only test, or both training and test data: 
*eliminate_high_scheduled_turnovers_in_training* and *eliminate_high_scheduled_turnovers_in_testing*
- 
2) Idle time: eliminate idle time from turnover durations
- Turn this on by setting *eliminate_idle_time* to True and defining an integer for *idle_time_threshold*,
which represents the minimum number of consecutive minutes with zero occupancy to be considered idle time (default 5).

## Run locally

```
make run-local
```

Then you can make a request to the service by running the following command:

```
curl -X POST "http://localhost:3000/predict" -H 'Content-Type: application/json' -d '{"inputs": {"case_id": "0f5d5fd5-85a6-4ab4-aed1-a3e1f226e5ef"}}'
```

or visit the auto-generated landing page at http://localhost:3000/ to see the list of endpoints and examples of how to use them.

## Test making a call to the service in k8s

Configure your Kubectl to connect to the correct cluster:

* dev: `gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`
* prod: `gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b`

Get the list of contexts with the following command:
```
kubectl config get-contexts

# gke_dev-internal-b2aa9f_us-central1_dev-internal-gke
# gke_prod-internal-c5ac6b_us-central1_prod-internal-gke
```

Connect to the correct cluster using the following command:
```
kubectl config use-context gke_<project-id>_<region>_<cluster-name>
```

Create a tunnel so you can call the service from your machine. In this example, it'll make the
service available on localhost:9998:

```
kubectl -n model-turnover port-forward service/model-turnover 9998:80
```

Now you can use curl to call the service:

```
curl -X POST "http://localhost:9998/predict" -H 'Content-Type: application/json' -d '{"inputs": { "case_id": "0f5d5fd5-85a6-4ab4-aed1-a3e1f226e5ef"}}'
```

or visit the auto-generated landing page at http://localhost:9998/ to see the list of endpoints and examples of how to use them.
