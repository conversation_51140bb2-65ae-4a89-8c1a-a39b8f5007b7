from typing import Dict

import pandas as pd
from sklearn.pipeline import Pipeline
from training_utils.clearml_reporter import <PERSON><PERSON><PERSON><PERSON>orter

from turnover.configs.prod_config import DataSelectionConfig, TrainingConfig
from turnover.model.turnover_model import TurnoverModel


class ModelTrainer:
    def __init__(
        self,
        model: TurnoverModel,
        training_config: TrainingConfig,
        data_selection_config: DataSelectionConfig,
        dataset: pd.DataFrame,
        reporter: ClearMLReporter,
    ):
        self.training_config = training_config
        self.data_selection_config = data_selection_config
        self.dataset = dataset
        self.model = model
        self.reporter = reporter
        self.generate_train_test_datasets("case_date")

    def train(self) -> Pipeline:
        df_train = self.df_train
        custom_turnover_after_cutoffs = self.calculate_custom_turnover_after_cutoffs()
        df_train = df_train.merge(custom_turnover_after_cutoffs, on="site_id", how="left")
        df_train["custom_cutoff"] = df_train["custom_cutoff"].fillna(
            self.training_config.default_turnover_limit
        )

        pipe = self.fit(df_train)

        return pipe, custom_turnover_after_cutoffs

    def fit(self, df_train: pd.DataFrame) -> dict[str, Pipeline]:
        turnover_rf_pipe_dict: Dict[str, Dict[str, Pipeline]] = {}
        training_counts_dict: Dict[str, Dict[str, int]] = {}
        training_first_case_counts_dict: Dict[str, Dict[str, int]] = {}

        for location in self.training_config.locations_to_fit:
            turnover_rf_pipe_dict[location] = {}
            training_counts_dict[location] = {}
            training_first_case_counts_dict[location] = {}
            for outcome_variable in self.training_config.outcome_variables:
                turnover_rf_pipe = self.model.model_pipeline()

                # don't eliminate null y values before this b/c different outcome variables
                # will be null for different rows
                df_train_location = df_train[
                    (df_train[self.training_config.location_type_to_fit] == location)
                    & ~df_train[outcome_variable].isna()
                ]

                if (
                    self.training_config.eliminate_first_case_for_turnover_open
                    and outcome_variable == "turnover_open_before_case"
                ):
                    # this eliminates first cases by scheduled start time, IF it's the turnover_open_before_case
                    df_train_location = df_train_location[df_train_location["first_case"] == 0]

                if self.training_config.eliminate_high_scheduled_turnovers_in_training:
                    df_train_location = df_train_location[
                        df_train_location["scheduled_turnover_minutes"]
                        <= self.training_config.scheduled_turnover_duration_limit
                    ]

                if self.training_config.eliminate_high_actual_turnovers_in_training:
                    # If we are fitting the turnover_open, take out turnovers where the full turnover before the case
                    # was over the limit. If we are fitting hte turnover_clean, take out turnovers where the full
                    # turnover after the case was over the limit.
                    if outcome_variable == "turnover_open_before_case":
                        outcome_variable_for_duration_limit = "turnover_before_case"
                    elif outcome_variable == "turnover_clean_after_case":
                        outcome_variable_for_duration_limit = "turnover_after_case"
                    else:
                        outcome_variable_for_duration_limit = outcome_variable
                    df_train_location = df_train_location[
                        (
                            df_train_location[outcome_variable_for_duration_limit]
                            <= df_train_location["custom_cutoff"]
                        )
                        & (df_train_location[outcome_variable] >= 0)
                    ]

                training_counts_dict[location][outcome_variable] = len(df_train_location)
                training_first_case_counts_dict[location][outcome_variable] = len(
                    df_train_location[df_train_location["first_case_by_actual_start"] == 1]
                )

                if df_train_location.empty:
                    print(
                        f"Empty training data for location: {location}, outcome: {outcome_variable}. Skipping."
                    )
                    continue

                turnover_rf_pipe_dict[location][outcome_variable] = self.fit_pipeline(
                    turnover_rf_pipe, df_train_location, outcome_variable
                )

        df_training_counts = pd.DataFrame.from_dict(
            training_counts_dict, orient="index"
        ).reset_index()

        df_first_case_training_counts = pd.DataFrame.from_dict(
            training_first_case_counts_dict, orient="index"
        ).reset_index()

        print("Turnovers in training")
        print(df_training_counts)
        print(
            "First cases (by schedule) in training. May be non-zero even if eliminate first case by actual start"
        )
        print(df_first_case_training_counts)

        self.reporter.report_dataframe_as_table(
            df_training_counts,
            report_group="Validation",
            table_name="Turnover Counts in Training Set",
        )

        self.reporter.report_dataframe_as_table(
            df_first_case_training_counts,
            report_group="Validation",
            table_name="Turnover Counts for First Cases in Training Set",
        )

        return turnover_rf_pipe_dict

    def fit_pipeline(
        self, pipe: Pipeline, df_train: pd.DataFrame, outcome_variable: str
    ) -> Pipeline:
        pipe.fit(
            X=df_train[self.data_selection_config.get_features_list()],
            y=df_train[outcome_variable],
        )

        return pipe

    def calculate_custom_turnover_after_cutoffs(self) -> pd.DataFrame:
        turnover_percentiles_all = (
            self.df_train.groupby("site_id")["turnover_after_case"]
            .quantile(self.training_config.percentile_for_actual_turnover_duration_limit)
            .reset_index()
        )
        turnover_percentiles_all["default_cutoff"] = self.training_config.default_turnover_limit
        turnover_percentiles_all["custom_cutoff"] = turnover_percentiles_all[
            [
                "turnover_after_case",
                "default_cutoff",
            ]
        ].min(axis=1)

        self.reporter.report_dataframe_as_table(
            turnover_percentiles_all[["site_id", "custom_cutoff"]],
            report_group="Validation",
            table_name="Custom Max Turnover by site_id",
        )

        return turnover_percentiles_all[["site_id", "custom_cutoff"]]

    def generate_train_test_datasets(self, datetime_colname: str) -> None:
        df_data = self.dataset
        df_data["days_before_most_recent"] = (
            pd.to_datetime(self.dataset[datetime_colname])
            - pd.to_datetime(self.dataset[datetime_colname]).max()
        ).dt.days

        # convert this to new columm in df_data that's test/train boolean so you can print out the number of
        #  data per location more easily.
        df_train = df_data.query(
            f"days_before_most_recent < -{self.training_config.test_on_most_recent_x_days}"
        )
        df_test = df_data.query(
            f"days_before_most_recent >= -{self.training_config.test_on_most_recent_x_days}"
        )
        print("len df_test", len(df_test))
        print(
            "df_train date range",
            df_train["case_date"].min(),
            df_train["case_date"].max(),
        )
        print("df_test date range", df_test["case_date"].min(), df_test["case_date"].max())

        self.df_train = df_train
        self.df_test = df_test
