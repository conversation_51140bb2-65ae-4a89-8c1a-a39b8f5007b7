from collections.abc import Sequence
from typing import Any

import numpy as np
import pandas as pd
from sklearn.compose import ColumnTransformer
from sklearn.ensemble import RandomForestRegressor
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import FunctionTransformer, OrdinalEncoder

# TODO: break out the feature definition out of config
from turnover.configs.prod_config import FeatureClass, ModelConfig


def identify_procs_with_short_turnover_keywords(first_primary_procedure_name: str) -> Any:
    short_keywords = [
        "POLYPECTOMY",
        "BRANCH",
        "COLONOSCOPY",
        "ABLA<PERSON>ON",
        "LUMBAR",
        "INJECTION",
        "<PERSON>EVE<PERSON>",
        "TRA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "FACET",
        "EPIDURAL",
        "SINGLE",
        "B<PERSON><PERSON><PERSON>",
        "STEROID",
        "INTERLAMIN<PERSON>",
    ]
    is_short_keyword_present = np.any(
        [keyword in first_primary_procedure_name for keyword in short_keywords]
    )
    return int(is_short_keyword_present)


def identify_procs_with_long_turnover_keywords(first_primary_procedure_name: str) -> Any:
    long_keywords = ["PULMONARY", "ISOLA<PERSON>ON", "THORACOSCOPIC"]
    is_long_keyword_present = np.any(
        [keyword in first_primary_procedure_name for keyword in long_keywords]
    )
    return int(is_long_keyword_present)


def calculate_procedure_keyword_features(df: pd.DataFrame) -> pd.DataFrame:
    df["procedure_contains_short_turnover_keyword"] = df["first_primary_procedure"].apply(
        identify_procs_with_short_turnover_keywords
    )
    df["procedure_contains_long_turnover_keyword"] = df["first_primary_procedure"].apply(
        identify_procs_with_long_turnover_keywords
    )
    return df[
        ["procedure_contains_short_turnover_keyword", "procedure_contains_long_turnover_keyword"]
    ]


#
def get_procedure_keyword_features_names_out(
    input_features: list[str], input_features_2: list[str]
) -> list[str]:
    # The arguments aren't used in this function, but sklearn expects two otherwise it errors out
    return [
        "procedure_contains_short_turnover_keyword",
        "procedure_contains_long_turnover_keyword",
    ]


class TurnoverModel:
    def __init__(self, config: ModelConfig, data_features: Sequence[FeatureClass]):
        self.config = config
        self.data_features = data_features

    def model_pipeline(self) -> Pipeline:
        numerical_features = [
            feat.name for feat in self.data_features if feat.data_type == "numeric"
        ]
        categorical_features = [
            feat.name for feat in self.data_features if feat.data_type == "categorical"
        ]
        feature_eng_input_columns = ["first_primary_procedure"]

        numeric_transformer = Pipeline(steps=[("imputer", SimpleImputer(strategy="median"))])

        categorical_transformer = Pipeline(
            steps=[
                ("encoder", OrdinalEncoder(handle_unknown="use_encoded_value", unknown_value=-1))
            ]
        )

        preprocessor = ColumnTransformer(
            transformers=[
                (
                    "calculate_new_features",
                    FunctionTransformer(
                        calculate_procedure_keyword_features,
                        validate=False,
                        feature_names_out=get_procedure_keyword_features_names_out,
                    ),
                    feature_eng_input_columns,
                ),
                ("num", numeric_transformer, numerical_features),
                ("cat", categorical_transformer, categorical_features),
            ],
            remainder="drop",
        )
        rf_pipe = Pipeline(
            steps=[
                ("preprocessor", preprocessor),
                (
                    "rf",
                    RandomForestRegressor(
                        n_estimators=self.config.n_estimators,
                        min_samples_split=self.config.min_samples_split,
                        min_samples_leaf=self.config.min_samples_leaf,
                        max_depth=self.config.max_depth,
                    ),
                ),
            ]
        )

        return rf_pipe
