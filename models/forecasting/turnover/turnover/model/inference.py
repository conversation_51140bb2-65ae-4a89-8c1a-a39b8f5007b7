from dataclasses import dataclass
from typing import Any

import numpy as np
import numpy.typing as npt
import pandas as pd
from pydantic import BaseModel, Extra, RootModel
from sklearn.pipeline import Pipeline


class TurnoverInputs(BaseModel, extra=Extra.ignore):
    """Data model to parse from input call"""

    first_primary_surgeon: str
    first_primary_procedure: str
    # flip_block_case: int

    # @field_validator("flip_block_case")
    # def flip_block_case_correct(cls, value: int) -> int:
    #     if value not in (0, 1):
    #         raise ValueError("flip_block_case must be 0 or 1")
    #     return value

    def to_np(self) -> np.typing.NDArray[Any]:
        return np.array(list(vars(self).values())).reshape(1, 9)

    def to_df(self) -> pd.DataFrame:
        return pd.DataFrame([self.model_dump()])


class ListInputs(RootModel[list[TurnoverInputs]]):
    def to_df(self) -> pd.DataFrame:
        return pd.DataFrame(self.model_dump())


@dataclass
class ModelInference:
    model: Pipeline

    def predict_single(self, values: dict[str, Any]) -> float:
        validated_input = TurnoverInputs(**values)
        input_df = validated_input.to_df()
        return float(self.predict_with_df(input_df)[0])

    def predict_many(self, values: list[dict[str, Any]]) -> list[float]:
        validated_input_list = ListInputs.model_validate(values)
        input_df = validated_input_list.to_df()
        return [float(val) for val in self.predict_with_df(input_df)]

    def predict_with_df(self, values: pd.DataFrame) -> npt.NDArray[np.float64]:
        # line below fails linting b/c it thinks the fit_pipe doesn't have a predict function,
        # but .predict() is built into the sklearn Pipeline object

        return_vals = self.model.predict(values)
        return np.array(return_vals)
