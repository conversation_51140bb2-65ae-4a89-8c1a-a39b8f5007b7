import pandas as pd
import pandera as pa
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from serving_utils.setup_json_logger import setup_json_logger

logger = setup_json_logger(logger_name="TurnoverFeatures")


class TurnoverDurationSchema(pa.DataFrameModel):
    """This is the common set of features between the ones we get from the store and the
    ones that end up in the model.
    There are other features that we query from the store that don't end up in the model (those
    are not here).
    There are features we add to these that will end up in the model (those are also not here)
    """

    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    first_primary_procedure: str | None
    first_primary_surgeon: str | None
    org_id: str | None


class TurnoverDurationResultSchema(pa.DataFrameModel):
    """This is the common set of features between the ones we get from the store and the
    ones that end up in the model.
    There are other features that we query from the store that don't end up in the model (those
    are not here).
    There are features we add to these that will end up in the model (those are also not here)
    """

    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    turnover_duration_forecast: float | None
    turnover_duration_version: str
    turnover_duration_service_version: str
    turnover_duration_model_version: str
    # TODO: https://linear.app/apella/issue/FORC-81/add-trace-ids-to-forecast-results-schema


def get_features_for_cases(
    feature_store: FeatureStore[CaseSchema], case_ids: set[str]
) -> tuple[pa.typing.DataFrame[TurnoverDurationSchema], pd.DataFrame]:
    features_result = feature_store.load_features(case_ids, TurnoverDurationSchema)
    if len(features_result.entities_with_missing_features) > 0:
        logger.warning(
            "Missing features for cases",
            extra={
                "num_cases_with_missing": len(features_result.entities_with_missing_features),
                "missing_features_counts": features_result.missing_features_count,
                "total_cases": len(case_ids),
            },
        )
    return features_result.entities, features_result.bigtable_timestamps
