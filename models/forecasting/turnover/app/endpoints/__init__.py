"""
Defines the API endpoints for all operations
© Apella Inc 2025
"""

from fastapi import FastAPI
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from serving_utils.add_fastapi_routes import add_fastapi_routes
from serving_utils.timeout_middleware import timeout_middleware

from .predict.predict_api import TurnoverService

# Configure the OTLP exporter
otlp_exporter = OTLPSpanExporter()

# Define the service resource
resource = Resource(attributes={SERVICE_NAME: "turnover"})

# Create a TracerProvider with the resource and processor
tracer_provider = TracerProvider(resource=resource)
span_processor = BatchSpanProcessor(otlp_exporter)
tracer_provider.add_span_processor(span_processor)

# Set the global TracerProvider
trace.set_tracer_provider(tracer_provider)


def create_api() -> FastAPI:
    """Creates the FastAPI application with all routes, instrumentation, and DI."""

    api: FastAPI = FastAPI(
        title="turnover",
        description="Turnover duration model service for predicting case turnover duration",
    )

    FastAPIInstrumentor.instrument_app(api)

    api.middleware("http")(timeout_middleware)

    add_fastapi_routes(api, TurnoverService.__name__)
    return api


__all__ = [
    "create_api",
]
