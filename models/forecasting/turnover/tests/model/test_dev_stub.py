import os
from unittest.mock import MagicMock, patch

from app.endpoints.predict.predict_api import TurnoverService


def test_dev_stub_prevents_model_load() -> None:
    with patch(
        "app.endpoints.predict.predict_api.ModelStorage", MagicMock()
    ) as mock_model_storage, patch(
        "app.endpoints.predict.predict_api.GCSClient", MagicMock(autospec=True)
    ), patch.object(
        os,
        "environ",
        {
            "DEV_STUB": "1",
            "FEATURE_STORE_PROJECT": "test_project",
            "FEATURE_STORE_INSTANCE": "test_instance",
        },
    ), patch("app.endpoints.predict.predict_api.BTClient", MagicMock(autospec=True)):
        app = TurnoverService()
        assert app.is_dev_stub
        assert app.models_by_location == {}
        assert mock_model_storage.load_nested_models.call_count == 0
