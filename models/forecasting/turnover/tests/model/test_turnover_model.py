import pytest

from turnover.configs.prod_config import DataSelectionConfig, ModelConfig
from turnover.model.turnover_model import TurnoverModel


@pytest.fixture
def default_turnover_model() -> TurnoverModel:
    turnover_model = TurnoverModel(
        config=ModelConfig(), data_features=DataSelectionConfig().features
    )
    return turnover_model


def test_model_pipeline(default_turnover_model: TurnoverModel) -> None:
    pipeline = default_turnover_model.model_pipeline()

    assert len(pipeline.steps) == 2
    assert pipeline.steps[0][0] == "preprocessor"
    assert pipeline.steps[1][0] == "rf"
