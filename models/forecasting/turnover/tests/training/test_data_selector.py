from unittest.mock import <PERSON><PERSON>ock

import numpy as np
import pandas as pd
import pytest
from google.cloud.bigquery import <PERSON><PERSON> as BQ<PERSON>lient
from pandasql import sqldf  # noqa
from training_utils.clearml_reporter import <PERSON><PERSON><PERSON>eporter

from turnover.configs.prod_config import DataSelectionConfig
from turnover.training.data_selector import DataSelector


@pytest.fixture
def mock_bq_client() -> BQClient:
    """
    Fixture that simulates BQ client
    """
    bq_client = MagicMock(BQClient)
    return bq_client


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    """
    Fixture to simulate the reporter
    """
    reporter = MagicMock(ClearMLReporter)
    return reporter


@pytest.fixture
def default_data_selection_config_idle_time_on() -> DataSelectionConfig:
    data_selection_config = DataSelectionConfig()
    data_selection_config.eliminate_idle_time = True
    data_selection_config.idle_time_threshold = 5
    data_selection_config.min_date = "2023-12-01"
    return data_selection_config


@pytest.fixture
def default_data_selection_config_idle_time_off() -> DataSelectionConfig:
    data_selection_config = DataSelectionConfig()
    data_selection_config.eliminate_idle_time = False
    data_selection_config.min_date = "2023-12-01"

    return data_selection_config


@pytest.fixture
def default_data_selector_idle_time_on(
    mock_bq_client: BQClient,
    default_data_selection_config_idle_time_on: DataSelectionConfig,
    mock_reporter: ClearMLReporter,
) -> DataSelector:
    data_selector = DataSelector(
        mock_bq_client, default_data_selection_config_idle_time_on, mock_reporter
    )

    # lines below fail linting b/c get "cannot assign to a method" linting error
    data_selector.query_for_object_data = MagicMock(return_value=df_obj_data)  # type: ignore
    data_selector.query_for_turnover_data = MagicMock(return_value=df_turnovers)  # type: ignore
    return data_selector


@pytest.fixture
def default_data_selector_idle_time_off(
    mock_bq_client: BQClient,
    default_data_selection_config_idle_time_off: DataSelectionConfig,
    mock_reporter: ClearMLReporter,
) -> DataSelector:
    data_selector = DataSelector(
        mock_bq_client, default_data_selection_config_idle_time_off, mock_reporter
    )
    data_selector.query_for_turnover_data = MagicMock(return_value=df_turnovers)  # type: ignore
    return data_selector


# ------------------------ section to generate sample dataframes; all based on HF-VH02-OR01, 2023-12-01 -----------

df_turnovers_list = [
    {
        "case_id": "e2be8a6a-dbc1-4139-974d-2531f07d210a",
        "prev_case_id": "",
        "first_case_by_actual_start": 1,
        "site_id": "VH02",
        "org_id": "health_first",
        "room_id": "HF-VH02-OR01",
        "case_date": "2023-12-01",
        "scheduled_start_datetime_local": "2023-12-01 09:45:00",
        "first_primary_procedure": "SHOULDER",
        "first_primary_surgeon": "STEPHENS, BRENT",
        "prev_first_primary_procedure": "",
        "prev_first_primary_surgeon": "",
        "back_table_open_datetime_local": "2023-12-01 08:43:36.031",
        "actual_start_datetime_local": "2023-12-01 10:20:56.000",
        "actual_end_datetime_local": "2023-12-01 11:53:12.431",
        "prev_case_actual_end_datetime_local": "",
        "prev_case_scheduled_end_datetime_local": "",
        "turnover_clean_minutes": np.nan,
        "turnover_open_minutes": 97,
        "turnover_minutes": np.nan,
        "scheduled_turnover_minutes": np.nan,
        "flip_block_case": 1,
        "case_surgeon_changed_room": 1,
        "first_case": 0,
    },
    {
        "case_id": "7c4eb22f-d2d1-4269-8ede-cf6f3c9dd5df",
        "prev_case_id": "e2be8a6a-dbc1-4139-974d-2531f07d210a",
        "first_case_by_actual_start": 0,
        "site_id": "VH02",
        "org_id": "health_first",
        "room_id": "HF-VH02-OR01",
        "case_date": "2023-12-01",
        "scheduled_start_datetime_local": "2023-12-01 11:15:00",
        "first_primary_procedure": "SHOULDER",
        "first_primary_surgeon": "STEPHENS, BRENT",
        "prev_first_primary_procedure": "SHOULDER",
        "prev_first_primary_surgeon": "STEPHENS, BRENT",
        "back_table_open_datetime_local": "2023-12-01 12:00:13.166",
        "actual_start_datetime_local": "2023-12-01 12:23:32.336",
        "actual_end_datetime_local": "2023-12-01 13:36:33.000",
        "prev_case_actual_end_datetime_local": "2023-12-01 11:53:12.431",
        "prev_case_scheduled_end_datetime_local": "2023-12-01 11:00:00",
        "turnover_clean_minutes": 7,
        "turnover_open_minutes": 23,
        "turnover_minutes": 30,
        "scheduled_turnover_minutes": 15,
        "flip_block_case": 1,
        "case_surgeon_changed_room": 1,
        "first_case": 0,
    },
    {
        "case_id": "f63a626e-5224-47ee-b88a-a7774061c22f",
        "prev_case_id": "7c4eb22f-d2d1-4269-8ede-cf6f3c9dd5df",
        "first_case_by_actual_start": 0,
        "site_id": "VH02",
        "org_id": "health_first",
        "room_id": "HF-VH02-OR01",
        "case_date": "2023-12-01",
        "scheduled_start_datetime_local": "2023-12-01 12:45:00",
        "first_primary_procedure": "BICEPS",
        "first_primary_surgeon": "STEPHENS, BRENT",
        "prev_first_primary_procedure": "SHOULDER",
        "prev_first_primary_surgeon": "STEPHENS, BRENT",
        "back_table_open_datetime_local": "2023-12-01 13:49:34.353",
        "actual_start_datetime_local": "2023-12-01 14:12:58.000",
        "actual_end_datetime_local": "2023-12-01 16:56:24.500",
        "prev_case_actual_end_datetime_local": "2023-12-01 13:36:33.000",
        "prev_case_scheduled_end_datetime_local": "2023-12-01 12:30:00",
        "turnover_clean_minutes": 13,
        "turnover_open_minutes": 23,
        "turnover_minutes": 36,
        "scheduled_turnover_minutes": 15,
        "flip_block_case": 1,
        "case_surgeon_changed_room": 1,
        "first_case": 0,
    },
]

df_turnovers = pd.DataFrame(df_turnovers_list)

for col in [
    "back_table_open_datetime_local",
    "actual_start_datetime_local",
    "prev_case_actual_end_datetime_local",
    "case_date",
    "scheduled_start_datetime_local",
]:
    df_turnovers[col] = pd.to_datetime(df_turnovers[col])

df_turnover_stretches = pd.DataFrame(
    [
        {
            "site_id": "VH02",
            "org_id": "health_first",
            "room_id": "HF-VH02-OR01",
            "case_date": pd.to_datetime("2023-12-01"),
            "turnovers_start_timestamp": pd.to_datetime("2023-12-01 08:44:00"),
            "turnovers_end_timestamp": pd.to_datetime("2023-12-01 14:13:00"),
        }
    ]
)


def generate_intervals_for_test(row: pd.DataFrame) -> pd.DatetimeIndex:
    return pd.date_range(
        start=pd.to_datetime(row["test_interval_start"]),  # type: ignore
        end=pd.to_datetime(row["test_interval_end"]),  # type: ignore
        freq="1min",
    )


# from this "base" dataframe, explode two other dataframes that simulate results of object model and
# the idle stretches calculated from it: df_obj_data and df_occupancy_stretches_exploded

df_base = pd.DataFrame(
    {
        "org_id": "health_first",
        "site_id": "VH02",
        "room_id": "HF-VH02-OR01",
        "case_date": "2023-12-01",
        "test_interval_start": pd.to_datetime("2023-12-01 08:44:00"),
        "test_interval_end": pd.to_datetime("2023-12-01 14:13:00"),
    },
    index=[0],
)
df_base_2 = df_base.copy()  # make a copy b/c want to explode this in other ways later

# create df_obj_data from df_base
df_base["frame_time_local_minute_trunc"] = df_base.apply(generate_intervals_for_test, axis=1)
df_obj_data = df_base.explode("frame_time_local_minute_trunc")

df_obj_data["frame_hour_local"] = df_obj_data["frame_time_local_minute_trunc"].dt.hour
df_obj_data["frame_date_local"] = df_obj_data["frame_time_local_minute_trunc"].dt.date
df_obj_data["frame_date_local"] = pd.to_datetime(df_obj_data["frame_date_local"])
del df_obj_data["test_interval_start"]
del df_obj_data["test_interval_end"]
df_obj_data["rolling_avg_total_occupancy_rounded"] = np.where(
    (
        (df_obj_data["frame_time_local_minute_trunc"] >= pd.to_datetime("2023-12-01 10:15:00"))
        & (df_obj_data["frame_time_local_minute_trunc"] <= pd.to_datetime("2023-12-01 10:20:00"))
    )
    | (
        (df_obj_data["frame_time_local_minute_trunc"] >= pd.to_datetime("2023-12-01 09:35:00"))
        & (df_obj_data["frame_time_local_minute_trunc"] <= pd.to_datetime("2023-12-01 09:40:00"))
    ),
    0,
    5,
)

# create df_occupancy_stretches_exploded from df_base_2
df_base_2["time_interval"] = df_base_2.apply(generate_intervals_for_test, axis=1)
df_occupancy_stretches_exploded = df_base_2.explode("time_interval")

del df_occupancy_stretches_exploded["test_interval_start"]
del df_occupancy_stretches_exploded["test_interval_end"]
df_occupancy_stretches_exploded["is_idle_based_on_occupancy_stretch"] = np.where(
    (
        (df_occupancy_stretches_exploded["time_interval"] >= pd.to_datetime("2023-12-01 10:15:00"))
        & (
            df_occupancy_stretches_exploded["time_interval"]
            <= pd.to_datetime("2023-12-01 10:20:00")
        )
    )
    | (
        (df_occupancy_stretches_exploded["time_interval"] >= pd.to_datetime("2023-12-01 09:35:00"))
        & (
            df_occupancy_stretches_exploded["time_interval"]
            <= pd.to_datetime("2023-12-01 09:40:00")
        )
    ),
    1,
    0,
)

# create df_turnover_and_idle_exploded from df_base_3
df_base_3 = df_turnovers.copy()

# add in total turnover time bounds
df_base_3["test_interval_start"] = [
    "2023-12-01 08:44:00.000000",
    "2023-12-01 11:54:00.000000",
    "2023-12-01 13:37:00.000000",
]
df_base_3["test_interval_end"] = [
    "2023-12-01 10:20:00.000000",
    "2023-12-01 12:23:00.000000",
    "2023-12-01 14:12:00.000000",
]

df_base_3["idle_time_local"] = df_base_3.apply(generate_intervals_for_test, axis=1)
df_turnover_and_idle_exploded = df_base_3.explode("idle_time_local")

del df_turnover_and_idle_exploded["test_interval_start"]
del df_turnover_and_idle_exploded["test_interval_end"]
df_turnover_and_idle_exploded["is_idle_based_on_occupancy_stretch"] = np.where(
    (
        (df_turnover_and_idle_exploded["idle_time_local"] >= pd.to_datetime("2023-12-01 10:15:00"))
        & (
            df_turnover_and_idle_exploded["idle_time_local"]
            <= pd.to_datetime("2023-12-01 10:20:00")
        )
    )
    | (
        (df_turnover_and_idle_exploded["idle_time_local"] >= pd.to_datetime("2023-12-01 09:35:00"))
        & (
            df_turnover_and_idle_exploded["idle_time_local"]
            <= pd.to_datetime("2023-12-01 09:40:00")
        )
    ),
    1,
    0,
)

# in the chosen OR-day, all idle time was during turnover_open
df_turnover_and_idle_exploded["is_idle_during_turnover_open"] = df_turnover_and_idle_exploded[
    "is_idle_based_on_occupancy_stretch"
]
df_turnover_and_idle_exploded["is_idle_during_turnover_clean"] = 0


def test_generate_data_for_fit_idle_time_off(
    default_data_selector_idle_time_off: DataSelector,
) -> None:
    df = default_data_selector_idle_time_off.generate_data_for_fit()

    assert max(df["turnover_open_minutes"]) == 97  # if idle time is off, max turnover is 97


def test_bq_query_string_for_turnover(default_data_selector_idle_time_on: DataSelector) -> None:
    query_string = default_data_selector_idle_time_on.bq_query_string_for_turnover(
        default_data_selector_idle_time_on.config.min_date,
    )

    assert default_data_selector_idle_time_on.config.min_date in query_string
