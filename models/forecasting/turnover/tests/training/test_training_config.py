import pytest

from turnover.configs.prod_config import DataSelectionConfig, FeatureClass


@pytest.fixture
def default_data_selection_config() -> DataSelectionConfig:
    data_selection_config = DataSelectionConfig()
    data_selection_config.features = [
        FeatureClass(name="first_primary_surgeon", data_type="categorical"),
        FeatureClass(name="first_primary_procedure", data_type="categorical"),
        FeatureClass(name="some_numerical_feature", data_type="numeric"),
    ]
    return data_selection_config


def test_get_features_works(default_data_selection_config: DataSelectionConfig) -> None:
    cat_features = default_data_selection_config.get_features_list("categorical")
    num_features = default_data_selection_config.get_features_list("numeric")

    assert len(cat_features) == 2
    assert len(num_features) == 1
