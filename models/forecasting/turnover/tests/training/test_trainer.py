from unittest.mock import MagicMock

import pandas as pd
import pytest
from training_utils.clearml_reporter import <PERSON><PERSON><PERSON><PERSON>orter

from turnover.configs.prod_config import DataSelectionConfig, ModelConfig, TrainingConfig
from turnover.model.turnover_model import TurnoverModel
from turnover.training.trainer import ModelTrainer


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    """
    Fixture to simulate the reporter
    """
    reporter = MagicMock(ClearMLReporter)
    return reporter


@pytest.fixture
def default_training_config() -> TrainingConfig:
    training_config = TrainingConfig()
    training_config.test_on_most_recent_x_days = 10
    return training_config


@pytest.fixture
def default_turnover_model() -> TurnoverModel:
    turnover_model = TurnoverModel(
        config=ModelConfig(), data_features=DataSelectionConfig().features
    )
    return turnover_model


df_features = pd.DataFrame(
    [
        {"case_date": pd.to_datetime("2024-01-01")},
        {"case_date": pd.to_datetime("2023-01-01")},
    ]
    * 200
)


@pytest.fixture
def default_trainer(
    default_turnover_model: TurnoverModel,
    mock_reporter: ClearMLReporter,
    default_training_config: TrainingConfig,
) -> ModelTrainer:
    default_trainer = ModelTrainer(
        model=default_turnover_model,
        training_config=default_training_config,
        data_selection_config=DataSelectionConfig(),
        dataset=df_features,
        reporter=mock_reporter,
    )
    return default_trainer


def test_generate_train_test_datasets(default_trainer: ModelTrainer) -> None:
    default_trainer.generate_train_test_datasets(datetime_colname="case_date")

    assert len(default_trainer.df_train) == 200
    assert len(default_trainer.df_test) == 200
