## Run locally

```
make run-local
```

Then you can make a request to the service by running the following command:

```
curl -X POST "http://localhost:3000/predict" -H 'Content-Type: application/json' -d '{"inputs": {"org_id": "HMH-O<PERSON>19", "first_primary_surgeon": "<PERSON>. <PERSON>", "first_primary_procedure": "Procedure A"}}'
```

or visit the auto-generated landing page at http://localhost:3000/ to see the list of endpoints and examples of how to use them.


## Test making a call to the service in k8s

Configure your Kubectl to connect to the correct cluster:

* dev: `gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`
* prod: `gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b`

Get the list of contexts with the following command:
```
kubectl config get-contexts

# gke_dev-internal-b2aa9f_us-central1_dev-internal-gke
# gke_prod-internal-c5ac6b_us-central1_prod-internal-gke
```

Connect to the correct cluster using the following command:
```
kubectl config use-context gke_<project-id>_<region>_<cluster-name>
```

Create a tunnel so you can call the service from your machine. In this example, it'll make the
service available on localhost:3000:

```
kubectl -n model-bayesian-case-duration port-forward service/model-bayesian-case-duration 3000:80
```

Now you can use curl to call the service:
```
curl -X POST "http://localhost:3000/predict" -H 'Content-Type: application/json' -d '{"inputs": {"org_id": "HMH-OPC19", "first_primary_surgeon": "Dr. John Doe", "first_primary_procedure": "Procedure A"}}'
```

or visit the auto-generated landing page at http://localhost:3000/ to see the list of endpoints and examples of how to use them.