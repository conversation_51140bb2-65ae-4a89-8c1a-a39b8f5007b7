from typing import cast

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns  # type: ignore
from google.cloud.bigquery import Client as BQClient
from google.cloud.bigquery import <PERSON><PERSON>aField, Table, TimePartitioning, TimePartitioningType
from plotly import express as px  # type: ignore
from sklearn.metrics import mean_absolute_error, mean_absolute_percentage_error, r2_score
from training_utils.clearml_reporter import ClearMLReporter
from training_utils.model_protocol import BayesianModelProtocol


def evaluate(y_hat_test: np.typing.NDArray[np.float64], test_data: pd.DataFrame) -> pd.DataFrame:
    """
    Evaluate predictions against test data for the static use case, with all features available.
    """
    df = test_data.copy()
    df["y_true"] = df["actual_duration"].values
    df["y_hat"] = np.median(y_hat_test, axis=1)
    df["error"] = df["y_true"] - df["y_hat"]
    df = add_RBE_column_to_df(df)
    df = add_actual_duration_category_to_df(df)
    return df


def report_evaluation(
    reporter: <PERSON>MLReporter,
    evaluate_df: pd.DataFrame,
    model: BayesianModelProtocol,
) -> pd.DataFrame:
    report_predictions_scatterplot_seaborn(reporter, evaluate_df)
    report_accuracy_metrics_overall(
        reporter, evaluate_df, table_name="Accuracy Metrics Overall (Static)"
    )
    report_accuracy_metrics_by_org_id(reporter, evaluate_df)
    report_accuracy_metrics_by_site_id(reporter, evaluate_df)
    report_accuracy_metrics_by_procedure_count(reporter, evaluate_df)
    report_accuracy_metrics_by_actual_duration_category(reporter, evaluate_df)
    output_df = report_evaluate_df(
        reporter, model, evaluate_df, table_name="Static Model Evaluation Dataset"
    )
    return output_df


def report_evaluation_standalone(
    reporter: ClearMLReporter,
    evaluate_df: pd.DataFrame,
    model: BayesianModelProtocol,
) -> pd.DataFrame:
    report_accuracy_metrics_overall(
        reporter, evaluate_df, table_name="Accuracy Metrics Overall (Standalone)"
    )
    output_df = report_evaluate_df(
        reporter, model, evaluate_df, table_name="Standalone Model Evaluation Dataset"
    )
    return output_df


def report_predictions_scatterplot(reporter: ClearMLReporter, evaluate_df: pd.DataFrame) -> None:
    """
    Report a scatterplot of actual vs predicted durations.
    """
    fig = px.scatter(
        evaluate_df,
        x="y_true",
        y="y_hat",
        color="org_id",
        hover_data=[
            "first_primary_surgeon",
            "first_primary_procedure",
            "site_id",
            "org_id",
            "scheduled_start_datetime_local",
        ],
    )
    fig.update_layout(xaxis_title="Actual", yaxis_title="Predicted")
    reporter.clearml_task.get_logger().report_plotly(
        "Actual vs Predicted",
        series="Validation",
        figure=fig,
    )


def report_predictions_scatterplot_seaborn(
    reporter: ClearMLReporter, evaluate_df: pd.DataFrame
) -> None:
    """TODO: Why do we have 2 of these?"""
    fig, ax = plt.subplots()
    sns.scatterplot(
        data=evaluate_df,
        x="y_true",
        y="y_hat",
        hue="org_id",
        size=1,
        alpha=0.9,
        legend=True,
        ax=ax,
    )
    reporter.clearml_task.get_logger().report_matplotlib_figure(
        "Predictions vs. Actuals",
        series="Evaluation",
        figure=fig,
    )


def report_accuracy_metrics_by_org_id(
    reporter: ClearMLReporter,
    evaluate_df: pd.DataFrame,
) -> None:
    """
    Report accuracy metrics aggregated by org_id.
    """
    r_squared = evaluate_df.groupby("org_id", sort=True).apply(
        calculate_r_squared, include_groups=False
    )
    mae = evaluate_df.groupby("org_id", sort=True).apply(calculate_MAE, include_groups=False)
    mape = evaluate_df.groupby("org_id", sort=True).apply(calculate_MAPE, include_groups=False)
    rbe = evaluate_df.groupby("org_id", sort=True).apply(calculate_RBE, include_groups=False)
    report_df = pd.concat([r_squared, mae, mape, rbe], axis=1)
    report_df = report_df.set_axis(["R_Squared", "MAE", "MAPE", "RBE"], axis=1)
    reporter.report_dataframe_as_table(
        report_df,
        report_group="Validation",
        table_name="Accuracy Metrics by Org",
    )


def report_accuracy_metrics_by_site_id(
    reporter: ClearMLReporter, evaluate_df: pd.DataFrame
) -> None:
    """
    Report accuracy metrics aggregated by site_id.
    """
    r_squared = evaluate_df.groupby("site_id", sort=True).apply(
        calculate_r_squared, include_groups=False
    )
    mae = evaluate_df.groupby("site_id", sort=True).apply(calculate_MAE, include_groups=False)
    mape = evaluate_df.groupby("site_id", sort=True).apply(calculate_MAPE, include_groups=False)
    rbe = evaluate_df.groupby("site_id", sort=True).apply(calculate_RBE, include_groups=False)
    report_df = pd.concat([r_squared, mae, mape, rbe], axis=1)
    report_df = report_df.set_axis(["R_Squared", "MAE", "MAPE", "RBE"], axis=1)
    reporter.report_dataframe_as_table(
        report_df,
        report_group="Validation",
        table_name="Accuracy Metrics by Site",
    )


def report_accuracy_metrics_by_procedure_count(
    reporter: ClearMLReporter, evaluate_df: pd.DataFrame
) -> None:
    """
    Report accuracy metrics aggregated by procedure_count.
    """
    r_squared = evaluate_df.groupby("procedure_count", sort=True).apply(
        calculate_r_squared, include_groups=False
    )
    mae = evaluate_df.groupby("procedure_count", sort=True).apply(
        calculate_MAE, include_groups=False
    )
    mape = evaluate_df.groupby("procedure_count", sort=True).apply(
        calculate_MAPE, include_groups=False
    )
    rbe = evaluate_df.groupby("procedure_count", sort=True).apply(calculate_RBE)
    report_df = pd.concat([r_squared, mae, mape, rbe], axis=1)
    report_df = report_df.set_axis(["R_Squared", "MAE", "MAPE", "RBE"], axis=1)
    reporter.report_dataframe_as_table(
        report_df,
        report_group="Validation",
        table_name="Accuracy Metrics by Procedure Count",
    )


def report_accuracy_metrics_by_actual_duration_category(
    reporter: ClearMLReporter, evaluate_df: pd.DataFrame
) -> None:
    """
    Report accuracy metrics aggregated by actual_duration_category.
    """
    r_squared = evaluate_df.groupby("actual_duration_category").apply(
        calculate_r_squared, include_groups=False
    )
    mae = evaluate_df.groupby("actual_duration_category").apply(calculate_MAE, include_groups=False)
    mape = evaluate_df.groupby("actual_duration_category").apply(
        calculate_MAPE, include_groups=False
    )
    rbe = evaluate_df.groupby("actual_duration_category").apply(calculate_RBE)
    report_df = pd.concat([r_squared, mae, mape, rbe], axis=1)
    report_df = report_df.set_axis(["R_Squared", "MAE", "MAPE", "RBE"], axis=1)
    reporter.report_dataframe_as_table(
        report_df,
        report_group="Validation",
        table_name="Accuracy Metrics by Actual Duration Category",
    )


def report_accuracy_metrics_overall(
    reporter: ClearMLReporter, evaluate_df: pd.DataFrame, table_name: str
) -> None:
    """
    Report accuracy metrics across the entire testing dataset.
    """
    report_df = pd.DataFrame()
    r_squared, mae, mape, rbe = (
        calculate_r_squared(evaluate_df),
        calculate_MAE(evaluate_df),
        calculate_MAPE(evaluate_df),
        calculate_RBE(evaluate_df),
    )
    report_df["R_Squared"] = [r_squared]
    report_df["MAE"] = mae
    report_df["MAPE"] = mape
    report_df["RBE"] = rbe
    reporter.report_dataframe_as_table(
        report_df,
        report_group="Validation",
        table_name=table_name,
    )


def report_evaluate_df(
    reporter: ClearMLReporter,
    model: BayesianModelProtocol,
    evaluate_df: pd.DataFrame,
    table_name: str,
) -> pd.DataFrame:
    """
    Report entire evaluation dataframe.
    """
    evaluate_df = evaluate_df.copy()
    evaluate_df.reset_index(inplace=True)
    cols_to_keep = [
        "case_id",
        "org_id",
        "site_id",
        "scheduled_duration",
        "actual_duration",
        "y_hat",
        "error",
        "absolute_error",
        "absolute_percentage_error",
        "really_bad_error",
    ]
    evaluate_df = evaluate_df[cols_to_keep]
    reporter.report_dataframe_as_table(
        evaluate_df,
        report_group="Validation",
        table_name=table_name,
    )
    return evaluate_df


def calculate_r_squared(
    df: pd.DataFrame, y_true_col: str = "y_true", y_pred_col: str = "y_hat"
) -> float:
    result = r2_score(df[y_true_col], df[y_pred_col])
    return cast(float, round(result, 3))


def calculate_MAE(df: pd.DataFrame, y_true_col: str = "y_true", y_pred_col: str = "y_hat") -> float:
    result = mean_absolute_error(df[y_true_col], df[y_pred_col])
    return cast(float, round(result, 2))


def calculate_MAPE(
    df: pd.DataFrame, y_true_col: str = "y_true", y_pred_col: str = "y_hat"
) -> float:
    result = mean_absolute_percentage_error(df[y_true_col], df[y_pred_col])
    return cast(float, round(result, 3))


def calculate_RBE(df: pd.DataFrame) -> float:
    df = add_RBE_column_to_df(df)
    result = df["really_bad_error"].mean()
    return round(result, 2)


def add_RBE_column_to_df(
    df: pd.DataFrame,
    y_true_col: str = "y_true",
    y_pred_col: str = "y_hat",
    rbe_threshold_absolute_error: int = 120,
    rbe_threshold_absolute_percentage_error: float = 1.0,
) -> pd.DataFrame:
    # Calculate Really Bad Error (RBE). A prediction is considered
    # an RBE if it is either >= 120 minutes wrong OR >= 100% wrong.
    df = df.copy()
    df["error"] = df[y_true_col] - df[y_pred_col]
    df["absolute_error"] = np.abs(df["error"])
    df["absolute_percentage_error"] = df["absolute_error"] / df[y_true_col]
    df["really_bad_error"] = (
        (df["absolute_error"] >= rbe_threshold_absolute_error)
        | (df["absolute_percentage_error"] >= rbe_threshold_absolute_percentage_error)
    ).astype(int)
    return df


def add_actual_duration_category_to_df(
    df: pd.DataFrame, y_true_col: str = "y_true"
) -> pd.DataFrame:
    df = df.copy()

    # Define custom bins (in minutes) and labels
    bins = [0, 30, 60, 120, 240, float("inf")]
    labels = [
        "Very Short (0-30m]",
        "Short (30-60m]",
        "Medium (1-2h]",
        "Long (2-4h]",
        "Very Long (4h+)",
    ]

    # Create new column with categorized durations
    df["actual_duration_category"] = pd.cut(df[y_true_col], bins=bins, labels=labels).astype(str)
    return df


def bayesian_evaluation_schema() -> list[SchemaField]:
    return [
        SchemaField("case_id", "STRING", mode="REQUIRED"),
        SchemaField("org_id", "STRING", mode="REQUIRED"),
        SchemaField("site_id", "STRING", mode="REQUIRED"),
        SchemaField("scheduled_duration", "INTEGER", mode="REQUIRED"),
        SchemaField("actual_duration", "INTEGER", mode="REQUIRED"),
        SchemaField("y_hat", "FLOAT", mode="REQUIRED"),
        SchemaField("error", "FLOAT", mode="REQUIRED"),
        SchemaField("absolute_error", "FLOAT", mode="REQUIRED"),
        SchemaField("absolute_percentage_error", "FLOAT", mode="REQUIRED"),
        SchemaField("really_bad_error", "FLOAT", mode="REQUIRED"),
        SchemaField("model_type", "STRING", mode="REQUIRED"),
        SchemaField("model_identifier", "STRING", mode="REQUIRED"),
        SchemaField("ds", "DATE", mode="REQUIRED"),
        SchemaField("training_run_time", "TIMESTAMP", mode="REQUIRED"),
    ]


def upload_evaluation_data(bq_client: BQClient, df: pd.DataFrame) -> None:
    table_id = "prod-data-platform-027529.case_forecasting.bayesian_case_duration_evaluation"
    table = Table(
        table_id,
        schema=bayesian_evaluation_schema(),
    )
    table.time_partitioning = TimePartitioning(
        type_=TimePartitioningType.DAY,
        field="ds",
    )
    table = bq_client.create_table(table, exists_ok=True)
    bq_client.insert_rows_from_dataframe(table, df)
