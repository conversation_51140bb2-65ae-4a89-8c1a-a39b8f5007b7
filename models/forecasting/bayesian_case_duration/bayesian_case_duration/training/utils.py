import matplotlib.pyplot as plt
import numpy as np
import pymc as pm  # type: ignore
import pytensor
from arviz import InferenceData
from pymc.variational.callbacks import Callback, Tracker  # type: ignore
from pytensor.compile.sharedvalue import SharedVariable  # Import SharedVariable type
from training_utils.clearml_reporter import ClearMLReporter


class FitModel:
    """
    This class contains all of the fitting logic. It lives here in utils rather than
    in the model itself, because it reports the live loss plot to ClearML during fitting,
    and we don't want any ClearML dependencies in BayesianCaseDurationModel, because this causes
    problems in the inference pipeline.
    """

    def __init__(
        self,
        model: pm.model.core.Model,
        reporter: ClearMLReporter,
        n: int = 10_000,
        learning_rate: float = 1e-1,
        use_gpu: bool = False,
    ):
        self.model = model
        self.reporter = reporter
        self.n = n
        self.learning_rate = learning_rate
        self.use_gpu = use_gpu

        # Note: GPU configuration should be done before importing PyTensor
        # using the config.py module, not here

    def fit(self) -> InferenceData:
        with self.model:
            learning_rate_tensor = pytensor.shared(self.learning_rate, "learning_rate")  # type: ignore
            optimizer = pm.adam(learning_rate=learning_rate_tensor)
            tracker = Tracker(lr=lambda: optimizer.keywords["learning_rate"].get_value())
            mean_field = pm.fit(
                method="advi",
                n=self.n,
                random_seed=123,
                obj_optimizer=optimizer,
                callbacks=[
                    tracker,
                    PlotLoss(reporter=self.reporter),
                    CheckParametersConvergence(
                        every=50,
                        tolerance=1e-10,
                        debug=True,
                    ),
                    ReduceLROnPlateau(
                        initial_learning_rate=learning_rate_tensor,
                        factor=0.8,
                        patience=100,
                        min_lr=1e-9,
                        cooldown=10,
                        verbose=True,
                    ),
                ],
            )
            idata: InferenceData = mean_field.sample()
            return idata


class ReduceLROnPlateau(Callback):  # type: ignore
    """Reduce learning rate when the loss has stopped improving.

    This is inspired by Keras' homonymous callback:
    https://github.com/keras-team/keras/blob/v2.14.0/keras/callbacks.py

    Code from:
    https://github.com/pymc-devs/pymc/issues/7010

    Parameters
    ----------
    learning_rate: pytensor.shared
        shared variable containing the learning rate
    factor: float
        factor by which the learning rate will be reduced: `new_lr = lr * factor`
    patience: int
        number of epochs with no improvement after which learning rate will be reduced
    min_lr: float
        lower bound on the learning rate
    cooldown: int
        number of iterations to wait before resuming normal operation after lr has been reduced
    verbose: bool
        False: quiet, True: update messages
    """

    def __init__(
        self,
        initial_learning_rate: SharedVariable,
        factor: float = 0.1,
        patience: float = 10,
        min_lr: float = 1e-6,
        cooldown: int = 0,
        verbose: bool = True,
    ):
        self.learning_rate = initial_learning_rate
        self.factor = factor
        self.patience = patience
        self.min_lr = min_lr
        self.cooldown = cooldown
        self.verbose = verbose
        self.cooldown_counter = 0
        self.wait = 0
        self.best = float("inf")
        self.old_lr = None

    def __call__(self, approx, loss_hist, i):  # type: ignore
        current = loss_hist[-1]

        if np.isinf(current):
            return

        if self.in_cooldown():
            self.cooldown_counter -= 1
            self.wait = 0
            return

        if current < self.best:
            self.best = current
            self.wait = 0
        elif not np.isinf(self.best):
            self.wait += 1
            if self.wait >= self.patience:
                self.reduce_lr()
                self.cooldown_counter = self.cooldown
                self.wait = 0

    def reduce_lr(self) -> None:
        old_lr = float(self.learning_rate.get_value())  # type: ignore
        if old_lr > self.min_lr:
            new_lr = max(old_lr * self.factor, self.min_lr)
            self.learning_rate.set_value(new_lr)  # type: ignore
            if self.verbose:
                print(
                    f"Reduced learning rate to {new_lr} after {self.patience} iterations without improvement."
                )

    def in_cooldown(self) -> bool:
        return self.cooldown_counter > 0


class CheckParametersConvergence(Callback):  # type: ignore
    """Convergence stopping check.
    Modified from https://github.com/pymc-devs/pymc/blob/268e13bde3e4863370e3b418e37f63023c123b20/pymc/variational/callbacks.py
    Added stopping rule based on average of parameter difference from current to previous iteration.

    Parameters
    ----------
    every: int
        check frequency
    tolerance: float
        if diff norm < tolerance: break
    diff: str
        difference type one of {'absolute', 'relative'}

    Examples
    --------
    >>> with model:
    ...     approx = pm.fit(
    ...         n=10000,
    ...         callbacks=[CheckParametersConvergence(every=50, tolerance=1e-4)],
    ...     )
    """

    def __init__(self, every: float = 100, tolerance: float = 1e-3, debug: bool = False):
        self.every: float = every
        self.prev: np.typing.NDArray[np.float64] = None  # type: ignore
        self.tolerance: float = tolerance
        self.debug: bool = debug

    def absolute(
        self, current: np.typing.NDArray[np.float64], prev: np.typing.NDArray[np.float64]
    ) -> np.typing.NDArray[np.float64]:
        diff = current - prev
        return np.array(np.abs(diff), dtype=np.float64)

    def __call__(self, approx, _, i) -> None:  # type: ignore
        if self.prev is None:
            self.prev = self.flatten_shared(approx.params)
            return
        if i % self.every or i < self.every:
            return
        current = self.flatten_shared(approx.params)
        prev = self.prev
        delta: np.typing.NDArray[np.float64] = self.absolute(current, prev)
        self.prev = current
        if self.debug:
            print(" ")
            print("iteration:", i)
            print("mean delta:", np.mean(delta))
            print("tolerance:", self.tolerance)
            print(" ")
        if np.mean(delta) < self.tolerance:
            raise StopIteration(f"Convergence achieved at {i}")

    @staticmethod
    def flatten_shared(shared_list: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.float64]:
        return np.concatenate([sh.get_value().flatten() for sh in shared_list])


class PlotLoss(Callback):  # type: ignore
    def __init__(
        self,
        reporter: ClearMLReporter,
    ):
        self.reporter = reporter

    def __call__(self, approx, loss_hist, i):  # type: ignore
        # Every fifth iteration, report loss plot to ClearML
        if i % 5 == 0:
            fig, ax = plt.subplots()
            plt.plot(loss_hist)
            self.reporter.clearml_task.get_logger().report_matplotlib_figure(
                "Loss Function",
                series="Training",
                figure=fig,
            )
            plt.close()
