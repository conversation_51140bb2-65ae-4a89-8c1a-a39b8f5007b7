from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class TrainingConfig(BaseModel):
    test_on_most_recent_x_days: int = 1


class DataSelectionConfig(BaseModel):
    min_date_in_dataset: str = "2024-11-10"
    max_date_in_dataset: str = "2025-01-15"


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "Bayesian Case Duration Model"
    task_name: str = "Experiment Training - Single Procedure Remove ClearML from model"
    tags: dict[str, str] = {"label": "label"}
    offline_mode: bool = False


class ModelTrainingConfig(BaseModel):
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "dev"
    model_file_name: str = "model.pkl"
