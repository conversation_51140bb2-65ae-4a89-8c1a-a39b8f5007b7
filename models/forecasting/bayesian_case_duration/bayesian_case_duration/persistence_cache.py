import hashlib
import json
from typing import Any, Iterator

import numpy as np
import pandas as pd
import redis
import serving_utils.config as config
from serving_utils.setup_json_logger import setup_json_logger

from bayesian_case_duration.types import APIInputs

STANDALONE_TTL = 60 * 60 * 24 * 7  # 1 week
SCHEDULE_TTL = 60 * 60 * 24 * 4  # 4 days
PREDICTION_FIELD = "prediction_median"
PREDICTION_LIST_FIELD = "prediction_list"


logger = setup_json_logger(
    logger_name="bayesian_case_duration.persistence_cache",
)


class PersistenceCache:
    redis_client: redis.Redis | None = None
    is_ready: bool = False

    def __init__(self, prefix: str = "bayesian") -> None:
        self.prefix = prefix
        try:
            self.redis_client = redis.Redis(
                host=config.get_redis_host(), port=config.get_redis_port(), socket_timeout=1
            )
            self.is_ready = bool(self.redis_client.ping())
        except Exception as e:
            logger.warning(f"Redis initialization failed: {e}")
            self.is_ready = False
            self.redis_client = None

    def __filter_none_values(self, dictionary: dict[str, Any]) -> str:
        filtered_dict = {k: v for k, v in dictionary.items() if v is not None}
        serialized = json.dumps(filtered_dict)
        return hashlib.md5(serialized.encode()).hexdigest()

    def __generate_standalone_key(self, case_features: APIInputs) -> str:
        case_dict = case_features.model_dump()

        # Extract the fields we want to use directly in the key
        org_id = case_dict.pop("org_id", None)
        first_primary_surgeon = case_dict.pop("first_primary_surgeon", None)
        first_primary_procedure = case_dict.pop("first_primary_procedure", None)

        # This accomplishes two things:
        # 1. Provide discoverability by using the org_id, surgeon, and procedure
        # 2. Avoid automatic namespacing in Redis
        return f"{self.prefix}:{org_id}:{first_primary_surgeon}:{first_primary_procedure}:{self.__filter_none_values(case_dict)}"

    def __generate_case_keys(self, features: pd.DataFrame) -> dict[str, str]:
        cache_keys = {
            case_id: f"{self.prefix}:{case_id}:{self.__filter_none_values(row.to_dict())}"
            for case_id, row in features.iterrows()
        }
        return cache_keys  # type: ignore

    def get_standalone_prediction(
        self, case_features: APIInputs
    ) -> tuple[float, list[float]] | None:
        if not self.is_ready or self.redis_client is None:
            return None

        try:
            prediction_json = self.redis_client.get(
                name=self.__generate_standalone_key(case_features)
            )
            if prediction_json is None:
                return None
            prediction = json.loads(prediction_json)  # type: ignore
            return prediction[PREDICTION_FIELD], prediction[PREDICTION_LIST_FIELD]
        except redis.RedisError as e:
            logger.warning(f"Redis get_standalone_prediction operation failed: {e}")
            return None

    def set_standalone_prediction(
        self,
        case_features: APIInputs,
        prediction_median: float,
        prediction_list: list[float],
        ttl: int = STANDALONE_TTL,
    ) -> None:
        if not self.is_ready or self.redis_client is None:
            return

        try:
            self.redis_client.set(
                name=self.__generate_standalone_key(case_features),
                value=json.dumps(
                    {PREDICTION_FIELD: prediction_median, PREDICTION_LIST_FIELD: prediction_list}
                ),
                ex=ttl,
            )
        except redis.RedisError as e:
            logger.warning(f"Redis set_standalone_prediction operation failed: {e}")

    def get_many_predictions(
        self, features: pd.DataFrame
    ) -> tuple[dict[str, dict[str, Any]], list[str]]:
        """Get predictions for multiple cases from cache.

        Args:
            features: DataFrame containing case features

        Returns:
            Tuple of (cached_predictions, case_ids_to_predict) where:
            - cached_predictions: Dictionary mapping case_id to prediction data
            - case_ids_to_predict: List of indices that need prediction
        """
        if not self.is_ready or self.redis_client is None:
            return {}, features.index.to_list()

        try:
            cache_keys_dict = self.__generate_case_keys(features)
            sorted_case_ids = sorted(features.index.to_list())

            cached_results = self.redis_client.mget(
                keys=[cache_keys_dict[case_id] for case_id in sorted_case_ids]
            )

            cached_predictions = {}
            case_ids_to_predict = []

            for idx, (case_id, cache_result) in enumerate(
                zip(sorted_case_ids, cached_results)  # type: ignore
            ):
                if cache_result is not None:
                    cached_predictions[case_id] = json.loads(cache_result)
                else:
                    case_ids_to_predict.append(case_id)

            return cached_predictions, case_ids_to_predict
        except redis.RedisError as e:
            logger.warning(f"Redis get_many operation failed: {e}")
            return {}, features.index.to_list()

    def set_many_predictions(
        self,
        features: pd.DataFrame,
        ttl: int = SCHEDULE_TTL,
    ) -> None:
        """Store multiple predictions in cache.

        Args:
            features: DataFrame containing case features and predictions
        """
        if not self.is_ready or self.redis_client is None:
            return

        try:
            # Don't include the predictions in the cache key
            cache_keys_dict = self.__generate_case_keys(
                features.drop(columns=["predictions"], axis=1)
            )
            cache_updates = {}

            for case_id, row in features.iterrows():
                cache_key = cache_keys_dict[case_id]  # type: ignore
                prediction = row["predictions"]
                prediction_data = {
                    PREDICTION_LIST_FIELD: prediction,
                    PREDICTION_FIELD: float(np.median(prediction)),
                }
                cache_updates[cache_key] = json.dumps(prediction_data)

            if cache_updates:
                pipeline = self.redis_client.pipeline()
                for key, value in cache_updates.items():
                    pipeline.set(name=key, value=value, ex=ttl)
                pipeline.execute()  # type: ignore
        except redis.RedisError as e:
            logger.warning(f"Redis set_many operation failed: {e}")

    def clear_cache(self, batch_size: int = 500) -> None:
        """Clear all cache keys matching the prefix."""
        if not self.is_ready or self.redis_client is None:
            logger.warning("Redis client not ready, skipping cache clear.")
            return

        logger.info(f"Clearing cache for prefix: '{self.prefix}:*'")
        try:
            keys_deleted_count = 0
            pipeline = self.redis_client.pipeline()
            key_generator: Iterator[bytes] = self.redis_client.scan_iter(match=f"{self.prefix}:*")

            for i, key in enumerate(key_generator):
                pipeline.delete(key)
                if (i + 1) % batch_size == 0:
                    results = pipeline.execute()  # type: ignore
                    keys_deleted_count += sum(results)
                    pipeline = self.redis_client.pipeline()

            # Delete any remaining keys in the last batch
            results = pipeline.execute()  # type: ignore
            keys_deleted_count += sum(results)

            logger.info(
                f"Successfully deleted {keys_deleted_count} keys matching '{self.prefix}:*'"
            )

        except redis.RedisError as e:
            logger.error(f"Redis clear_cache operation failed: {e}")
