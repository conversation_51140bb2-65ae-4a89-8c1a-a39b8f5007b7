import json
from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest
import redis

from bayesian_case_duration.persistence_cache import (
    PREDICTION_FIELD,
    PREDICTION_LIST_FIELD,
    SCHEDULE_TTL,
    STANDALONE_TTL,
    PersistenceCache,
)
from bayesian_case_duration.types import APIInputs


@pytest.fixture
def mock_redis_client() -> MagicMock:
    mock_client = MagicMock(spec=redis.Redis)
    mock_client.ping.return_value = True
    return mock_client


@pytest.fixture
def mock_redis_client_not_ready() -> MagicMock:
    mock_client = MagicMock(spec=redis.Redis)
    mock_client.ping.return_value = False
    return mock_client


@pytest.fixture
def persistence_cache(mock_redis_client: MagicMock) -> PersistenceCache:
    with patch(
        "bayesian_case_duration.persistence_cache.redis.Redis", return_value=mock_redis_client
    ):
        cache = PersistenceCache()
        return cache


@pytest.fixture
def persistence_cache_not_ready(mock_redis_client_not_ready: MagicMock) -> PersistenceCache:
    with patch(
        "bayesian_case_duration.persistence_cache.redis.Redis",
        return_value=mock_redis_client_not_ready,
    ):
        cache = PersistenceCache()
        return cache


class TestPersistenceCache:
    def test_init_connects_to_redis(self) -> None:
        with patch("bayesian_case_duration.persistence_cache.redis.Redis") as mock_redis:
            mock_client = MagicMock()
            mock_redis.return_value = mock_client

            cache = PersistenceCache()

            assert cache.redis_client == mock_client
            mock_redis.assert_called_once()

    def test_get_standalone_prediction_cache_hit(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        case_features = APIInputs(
            org_id="org1",
            first_primary_surgeon="surgeon1",
            first_primary_procedure="procedure1",
            site_id="site1",
        )
        mock_redis_client.get.return_value = json.dumps(
            {PREDICTION_FIELD: 42.5, PREDICTION_LIST_FIELD: [41.0, 42.0, 43.0, 44.0]}
        )

        # Execute
        result = persistence_cache.get_standalone_prediction(case_features)

        # Assert
        assert result is not None
        median, samples = result
        assert median == 42.5
        assert samples == [41.0, 42.0, 43.0, 44.0]

        mock_redis_client.get.assert_called_once()
        key = mock_redis_client.get.call_args.kwargs["name"]
        assert key.startswith("bayesian:org1:surgeon1:procedure1:")
        assert len(key.split(":")) == 5  # Ensure key has the correct format

    def test_get_standalone_prediction_cache_miss(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        case_features = APIInputs(
            org_id="org1",
            first_primary_surgeon="surgeon1",
            first_primary_procedure="procedure1",
            site_id="site1",
            surgeon_count=5,
        )
        mock_redis_client.get.return_value = None

        # Execute
        result = persistence_cache.get_standalone_prediction(case_features)

        # Assert
        assert result is None
        mock_redis_client.get.assert_called_once()

    def test_set_standalone_prediction(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        case_features = APIInputs(
            org_id="org1",
            first_primary_surgeon="surgeon1",
            first_primary_procedure="procedure1",
            site_id="site1",
        )
        median = 42.5
        samples = [41.0, 42.0, 43.0, 44.0]

        # Execute
        persistence_cache.set_standalone_prediction(case_features, median, samples)

        # Assert
        mock_redis_client.set.assert_called_once()

        key = mock_redis_client.set.call_args.kwargs["name"]
        assert key.startswith("bayesian:org1:surgeon1:procedure1:")
        assert len(key.split(":")) == 5  # Ensure key has the correct format
        assert mock_redis_client.set.call_args.kwargs["ex"] == STANDALONE_TTL
        prediction = json.loads(mock_redis_client.set.call_args.kwargs["value"])
        assert prediction[PREDICTION_FIELD] == median
        assert prediction[PREDICTION_LIST_FIELD] == samples

    def test_get_many_predictions_all_cached(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        features = pd.DataFrame(
            {"feature1": [1, 2], "feature2": ["a", "b"]}, index=["case1", "case2"]
        )

        mock_redis_client.mget.return_value = [
            json.dumps({PREDICTION_FIELD: 41.5, PREDICTION_LIST_FIELD: [41.0, 42.0]}),
            json.dumps({PREDICTION_FIELD: 43.5, PREDICTION_LIST_FIELD: [43.0, 44.0]}),
        ]

        # Execute
        cached_predictions, case_ids_to_predict = persistence_cache.get_many_predictions(features)

        # Assert
        assert len(cached_predictions) == 2
        assert "case1" in cached_predictions
        assert "case2" in cached_predictions
        assert cached_predictions["case1"][PREDICTION_FIELD] == 41.5
        assert cached_predictions["case1"][PREDICTION_LIST_FIELD] == [41.0, 42.0]
        assert cached_predictions["case2"][PREDICTION_FIELD] == 43.5
        assert cached_predictions["case2"][PREDICTION_LIST_FIELD] == [43.0, 44.0]
        assert case_ids_to_predict == []

    def test_get_many_predictions_partial_cache(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        features = pd.DataFrame(
            {"feature1": [1, 2, 3], "feature2": ["a", "b", "c"]}, index=["case1", "case2", "case3"]
        )

        mock_redis_client.mget.return_value = [
            json.dumps({PREDICTION_FIELD: 41.5, PREDICTION_LIST_FIELD: [41.0, 42.0]}),
            None,
            json.dumps({PREDICTION_FIELD: 45.5, PREDICTION_LIST_FIELD: [45.0, 46.0]}),
        ]

        # Execute
        cached_predictions, case_ids_to_predict = persistence_cache.get_many_predictions(features)

        # Assert
        assert len(cached_predictions) == 2
        assert "case1" in cached_predictions
        assert "case3" in cached_predictions
        assert "case2" not in cached_predictions
        assert case_ids_to_predict == ["case2"]  # Index of case2

    def test_set_many_predictions(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        features = pd.DataFrame(
            {
                "case_type_short": ["ELECTIVE", "ELECTIVE"],
                "patient_class": ["INPATIENT", "INPATIENT"],
                "add_on": [0, 0],
                "case_procedure_list": [
                    ["HYSTERECTOMY ABDOMINAL TOTAL", "SALPINGO OOPHORECTOMY"],
                    [
                        "CRANIOTOMY/CRANIECTOMY FOR TUMOR",
                        "MICROSURGICAL TECHNIQUES; USE OF OPERATING MICROSCOPE",
                        "STEREOTACTIC COMPUTER ASSISTED PROCEDURE; CRANIAL, SPINAL",
                    ],
                ],
                "day_of_week": ["Tuesday", "Tuesday"],
                "first_primary_procedure": [
                    "HYSTERECTOMY ABDOMINAL TOTAL",
                    "CRANIOTOMY/CRANIECTOMY FOR TUMOR",
                ],
                "first_primary_surgeon": ["JOHN, DOE", "JANE, DOE"],
                "is_flip_room": [False, False],
                "last_case": [0, 1],
                "num_scheduled_cases": [3, 3],
                "procedure_count": [2.0, 3.0],
                "org_id": ["org1", "org2"],
                "outpatient": [0, 0],
                "running_during_lunch": [0, 1],
                "scheduled_starting_hour": [7, 10],
                "surgeon_count": [1.0, 1.0],
                "to_follow_case": [1, 0],
                "site_id": ["site1", "site2"],
            },
            index=["50570630-case-0001-af78-135f367c8590", "b5d72d1f-case-0002-a5d5-d813d46fb1cf"],
        )

        predictions = np.array([[41.0, 42.0], [43.0, 44.0]])
        features["predictions"] = predictions.tolist()

        # Execute
        persistence_cache.set_many_predictions(features)

        # Assert
        assert mock_redis_client.pipeline.called
        assert mock_redis_client.pipeline().set.called
        assert mock_redis_client.pipeline().execute.called
        # Verify the pipeline was used correctly
        call_args = mock_redis_client.pipeline().set.call_args_list

        assert len(call_args) == 2

        # Check that keys are properly formatted and values are JSON strings
        assert (
            call_args[0].kwargs["name"]
            == "bayesian:50570630-case-0001-af78-135f367c8590:3f7505f61039cdcd6278722dda28f73e"
        )
        assert call_args[0].kwargs["ex"] == SCHEDULE_TTL
        data = json.loads(call_args[0].kwargs["value"])
        assert PREDICTION_LIST_FIELD in data
        assert PREDICTION_FIELD in data
        assert data[PREDICTION_LIST_FIELD] == predictions[0].tolist()
        assert data[PREDICTION_FIELD] == float(np.median(predictions[0]))

        assert (
            call_args[1].kwargs["name"]
            == "bayesian:b5d72d1f-case-0002-a5d5-d813d46fb1cf:35a1be7f21d919b64bf55a50828ba0e7"
        )
        assert call_args[1].kwargs["ex"] == SCHEDULE_TTL
        data = json.loads(call_args[1].kwargs["value"])
        assert PREDICTION_LIST_FIELD in data
        assert PREDICTION_FIELD in data
        assert data[PREDICTION_LIST_FIELD] == predictions[1].tolist()
        assert data[PREDICTION_FIELD] == float(np.median(predictions[1]))

    def test_get_many_predictions_not_ready(
        self, persistence_cache_not_ready: PersistenceCache
    ) -> None:
        # Setup
        features = pd.DataFrame(
            {"feature1": [1, 2], "feature2": ["a", "b"]}, index=["case1", "case2"]
        )

        # Execute
        cached_predictions, case_ids_to_predict = persistence_cache_not_ready.get_many_predictions(
            features
        )

        # Assert
        assert cached_predictions == {}
        assert case_ids_to_predict == ["case1", "case2"]

    def test_get_standalone_prediction_redis_exception(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup: simulate Redis exception on get operation
        case_features = APIInputs(
            org_id="org1",
            first_primary_surgeon="surgeon1",
            first_primary_procedure="procedure1",
        )
        mock_redis_client.get.side_effect = redis.RedisError("Connection error")

        # Execute
        result = persistence_cache.get_standalone_prediction(case_features)

        # Assert that the exception is gracefully handled (e.g., returning None)
        assert result is None
        mock_redis_client.get.assert_called_once()

    def test_clear_cache_success(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        keys_to_delete = [b"bayesian:key1", b"bayesian:key2", b"bayesian:key3"]
        mock_redis_client.scan_iter.return_value = iter(keys_to_delete)
        mock_pipeline = MagicMock()
        mock_pipeline.execute.return_value = [1, 1, 1]  # Simulate 1 key deleted per call
        mock_redis_client.pipeline.return_value = mock_pipeline

        # Execute
        persistence_cache.clear_cache()

        # Assert
        mock_redis_client.scan_iter.assert_called_once_with(match="bayesian:*")
        mock_redis_client.pipeline.assert_called()
        assert mock_pipeline.delete.call_count == len(keys_to_delete)
        mock_pipeline.delete.assert_any_call(b"bayesian:key1")
        mock_pipeline.delete.assert_any_call(b"bayesian:key2")
        mock_pipeline.delete.assert_any_call(b"bayesian:key3")
        # Should execute once at the end for the remaining keys
        mock_pipeline.execute.assert_called_once()

    def test_clear_cache_batching(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup - 7 keys, batch size 3
        keys_to_delete = [f"bayesian:key{i}".encode() for i in range(7)]
        mock_redis_client.scan_iter.return_value = iter(keys_to_delete)
        mock_pipeline = MagicMock()
        # Simulate pipeline execution results
        mock_pipeline.execute.side_effect = [[1, 1, 1], [1, 1, 1], [1]]
        mock_redis_client.pipeline.return_value = mock_pipeline

        # Execute with a smaller batch size for testing
        persistence_cache.clear_cache(batch_size=3)

        # Assert
        mock_redis_client.scan_iter.assert_called_once_with(match="bayesian:*")
        assert mock_pipeline.delete.call_count == 7
        # Pipeline executes after 3 deletes, then after 6, then once at the end
        assert mock_pipeline.execute.call_count == 3

    def test_clear_cache_no_keys(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        mock_redis_client.scan_iter.return_value = iter([])  # No keys found
        mock_pipeline = MagicMock()
        mock_pipeline.execute.return_value = []
        mock_redis_client.pipeline.return_value = mock_pipeline

        # Execute
        persistence_cache.clear_cache()

        # Assert
        mock_redis_client.scan_iter.assert_called_once_with(match="bayesian:*")
        mock_pipeline.delete.assert_not_called()
        # Should execute once at the end even if no keys were added
        mock_pipeline.execute.assert_called_once()

    def test_clear_cache_redis_exception(
        self, persistence_cache: PersistenceCache, mock_redis_client: MagicMock
    ) -> None:
        # Setup
        mock_redis_client.scan_iter.side_effect = redis.RedisError("Scan failed")
        mock_pipeline = MagicMock()
        mock_redis_client.pipeline.return_value = mock_pipeline

        # Execute
        persistence_cache.clear_cache()

        # Assert
        mock_redis_client.scan_iter.assert_called_once_with(match="bayesian:*")
        # Pipeline methods should not be called if scan fails
        mock_pipeline.delete.assert_not_called()
        mock_pipeline.execute.assert_not_called()

    def test_clear_cache_not_ready(
        self,
        persistence_cache_not_ready: PersistenceCache,
        mock_redis_client_not_ready: MagicMock,
    ) -> None:
        # Execute
        persistence_cache_not_ready.clear_cache()

        # Assert
        # No Redis methods should be called if the client is not ready
        mock_redis_client_not_ready.scan_iter.assert_not_called()
        mock_redis_client_not_ready.pipeline.assert_not_called()
