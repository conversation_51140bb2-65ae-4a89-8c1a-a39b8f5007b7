from contextlib import nullcontext as does_not_raise
from datetime import date, datetime
from typing import Any

import numpy.typing  # noqa
import pytest

from static_start_offset.model.inference import StaticStartOffsetInputs


@pytest.fixture
def sample_valid_values() -> dict[str, Any]:
    valid_input_values = {
        "scheduled_starting_hour": 10,
        "cumsum_scheduled_case_duration_so_far": 117,
        "minutes_after_previous_case_scheduled_end": 3,
        "number_first_cases_started_at_same_time": -1,
        "to_follow_case": 1,
        "first_primary_surgeon": "BROWN, TIMOTHY",
        "case_date": date(2023, 10, 4),
        "scheduled_start_datetime_local": datetime(2023, 10, 4, 10, 0, 0),
        "site_id": "HMH-OPC19",
    }
    return valid_input_values


def test_pydantic_model_for_various_inputs(sample_valid_values: dict[str, Any]) -> None:
    # check values are valid
    with does_not_raise():
        StaticStartOffsetInputs(**sample_valid_values)

    # eliminated site checking b/c site is no longer part of the basemodel
    # # check site not supported
    # sample_valid_values["site"] = "Tampa is not supported"
    # with pytest.raises(ValueError):
    #     StaticStartOffsetInputs(**sample_valid_values)

    # check starting hour validator
    sample_valid_values["site_id"] = "HMH-OPC19"
    sample_valid_values["scheduled_starting_hour"] = 30
    with pytest.raises(ValueError):
        StaticStartOffsetInputs(**sample_valid_values)
