import pandas as pd
import pytest
from sklearn.pipeline import Pipeline

from static_start_offset.configs.prod_config import ModelTrainingConfig
from static_start_offset.model.static_start_offset_model import StaticStartOffsetModel


@pytest.fixture
def default_static_start_offset_model() -> StaticStartOffsetModel:
    model_training_config = ModelTrainingConfig()
    model = StaticStartOffsetModel(
        model_training_config.forecast_model_config,
        model_training_config.data_selection_config.features,
    )

    return model


features_list = [
    {
        "scheduled_starting_hour": 5,
        "cumsum_scheduled_case_duration_so_far": 50,
        "minutes_after_previous_case_scheduled_end": None,
        "to_follow_case": 1,
        "first_primary_surgeon": "A, SURGEON",
        "site_id": "a_site",
    }
]

df_features = pd.DataFrame(features_list)


def test_filler_transformations(
    default_static_start_offset_model: StaticStartOffsetModel,
) -> None:
    full_pipeline = default_static_start_offset_model.model_pipeline()
    preprocessor_tuple, model = full_pipeline.steps
    preprocessor_name, preprocessor = preprocessor_tuple

    preprocess_pipe = Pipeline(
        # hyperparameters found by tuning previous model
        steps=[
            ("preprocessor", preprocessor),
        ]
    )
    # df_features_transformed is now an array of size num_df_rows x num_features
    array_features_transformed = preprocess_pipe.fit_transform(df_features)

    # check no duplication of columns; should have only the 6 original columns
    assert len(array_features_transformed[0]) == 6

    # Nones should be replaced with filler values 1000
    assert 1000 in array_features_transformed[0]
