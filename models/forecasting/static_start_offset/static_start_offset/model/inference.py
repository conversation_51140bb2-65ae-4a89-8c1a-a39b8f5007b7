from dataclasses import dataclass
from typing import Any

import numpy as np
import pandas as pd
from pydantic import BaseModel, Extra, RootModel, field_validator
from sklearn.pipeline import Pipeline


class StaticStartOffsetInputs(BaseModel, extra=Extra.ignore):
    """Data model to parse from input call"""

    scheduled_starting_hour: int
    cumsum_scheduled_case_duration_so_far: int
    to_follow_case: int
    first_primary_surgeon: str
    site_id: str
    minutes_after_previous_case_scheduled_end: int | None

    @field_validator("scheduled_starting_hour")
    def scheduled_starting_hour_must_be_within_a_day(cls, value: int) -> int:
        if value < 0 or value > 23:
            raise ValueError(
                "scheduled_starting_hour must be an integer value between 0 and 23, inclusive"
            )
        return value

    def to_np(self) -> np.typing.NDArray[Any]:
        return np.array(list(vars(self).values())).reshape(1, 9)

    def to_df(self) -> pd.DataFrame:
        return pd.DataFrame([self.model_dump()])


class ListInputs(RootModel[list[StaticStartOffsetInputs]]):
    def to_df(self) -> pd.DataFrame:
        return pd.DataFrame(self.model_dump())


@dataclass
class ModelInference:
    model: Pipeline

    def predict_single(self, values: dict[str, Any]) -> float:
        return self.predict_many([values])[0]

    def predict_many(self, values: list[dict[str, Any]]) -> list[float]:
        validated_input_list = ListInputs.model_validate(values)
        return [float(val) for val in self.model.predict(validated_input_list.to_df())]
