from collections.abc import Sequence

import numpy as np
import pandas as pd
from catboost import CatBoostRegressor  # type: ignore
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import FunctionTransformer

# TODO: break out the feature definition out of config
from static_start_offset.configs.prod_config import FeatureClass, ModelConfig


class StaticStartOffsetModel:
    def __init__(self, config: ModelConfig, data_features: Sequence[FeatureClass]):
        self.config = config
        self.data_features = data_features

    def model_pipeline(self) -> Pipeline:
        def add_turnover_filler(df: pd.DataFrame) -> pd.DataFrame:
            # ideally this would look for is_first_case instead of null values of minutes_after_previous_case_scheduled_end
            # but is_first_case is not available at this point in the pipeline
            df["minutes_after_previous_case_scheduled_end_filled"] = np.where(
                pd.isna(df["minutes_after_previous_case_scheduled_end"]),
                [1000] * len(df),
                df["minutes_after_previous_case_scheduled_end"],
            )

            return df[["minutes_after_previous_case_scheduled_end_filled"]]

        # numerical_features that don't need filler values
        # if e.g. minutes_after_previous_case_scheduled_end is passed through into numeric_transformer,
        # it will create duplicate columns -- one with the filler values, the other with imputed median
        numerical_features = [
            feat.name
            for feat in self.data_features
            if feat.data_type == "numeric"
            and feat.name != "minutes_after_previous_case_scheduled_end"
        ]
        categorical_features = [
            feat.name for feat in self.data_features if feat.data_type == "categorical"
        ]

        numeric_transformer = Pipeline(steps=[("imputer", SimpleImputer(strategy="median"))])

        def feature_names_out_with_filled(_: None, input_features: list[str]) -> list[str]:
            return ["minutes_after_previous_case_scheduled_end_filled"]

        preprocessor = ColumnTransformer(
            transformers=[
                (
                    "filler_for_turnover",
                    FunctionTransformer(
                        func=add_turnover_filler,
                        validate=False,
                        feature_names_out=feature_names_out_with_filled,
                    ),
                    ["minutes_after_previous_case_scheduled_end"],
                ),
                ("num", numeric_transformer, numerical_features),
                ("cat", "passthrough", categorical_features),
            ],
        )

        rf_pipe = Pipeline(
            # hyperparameters found by tuning previous model
            steps=[
                ("preprocessor", preprocessor),
                (
                    "catboost",
                    CatBoostRegressor(
                        iterations=self.config.iterations,
                        learning_rate=self.config.learning_rate,
                        depth=self.config.depth,
                        loss_function="MAE",
                        logging_level="Silent",
                        # 3 numerical features (idx 0,1,2) , followed by 3 categorical features idx 3,4,5)
                        cat_features=[3, 4, 5],
                    ),
                ),
            ]
        )

        return rf_pipe
