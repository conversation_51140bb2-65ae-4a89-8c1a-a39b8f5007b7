import pandas as pd
from sklearn.pipeline import Pipeline
from training_utils.clearml_reporter import Clear<PERSON><PERSON>eporter

from static_start_offset.configs.prod_config import TrainingConfig
from static_start_offset.model.static_start_offset_model import StaticStartOffsetModel


class ModelTrainer:
    def __init__(
        self,
        model: StaticStartOffsetModel,
        training_config: TrainingConfig,
        dataset: pd.DataFrame,
        reporter: ClearMLReporter,
    ):
        self.training_config = training_config
        self.dataset = dataset
        self.model = model
        self.reporter = reporter
        self.generate_train_test_datasets("case_date")

    def train(self) -> Pipeline:
        df_train = self.df_train

        pipe = self.fit(df_train)

        return pipe

    def fit(self, df_train: pd.DataFrame) -> dict[str, Pipeline]:
        start_rf_pipe_dict = {}

        if self.training_config.fit_one_model_for_all_sites is True:
            print("fitting for all sites")
            start_rf_pipe = self.model.model_pipeline()
            start_rf_pipe_dict["all"] = self.fit_pipeline(start_rf_pipe, df_train)
        else:
            for location in self.training_config.locations_to_fit:
                start_rf_pipe = (
                    self.model.model_pipeline()
                )  # need to re-instantiate a pipeline for each site
                df_train_location = df_train[
                    df_train[self.training_config.location_type_to_fit] == location
                ]

                if df_train_location.empty:
                    print(f"Skipping fit for {location} as the training set is empty.")
                    continue

                print(
                    "fitting for " + location + " with " + str(len(df_train_location)) + " points"
                )
                start_rf_pipe_dict[location] = self.fit_pipeline(start_rf_pipe, df_train_location)

        return start_rf_pipe_dict

    def fit_pipeline(self, pipe: Pipeline, df_train: pd.DataFrame) -> Pipeline:
        columns_for_train = list(df_train.columns)
        columns_for_train.remove(self.training_config.outcome_column)

        for column in [
            "case_date",
            "scheduled_start_datetime_local",
            "business_unit",
            "org_id",
            "days_before_most_recent",
        ]:
            columns_for_train.remove(column)

        pipe.fit(
            X=df_train[columns_for_train],
            y=df_train[self.training_config.outcome_column],
        )

        return pipe

    def generate_train_test_datasets(self, datetime_colname: str) -> None:
        df_data = self.dataset
        df_data["days_before_most_recent"] = (
            pd.to_datetime(self.dataset[datetime_colname])
            - pd.to_datetime(self.dataset[datetime_colname]).max()
        ).dt.days

        # convert this to new columm in df_data taht's test/train boolean so you can print out the number of
        #  data per site more easily.
        df_train = df_data.query(
            f"days_before_most_recent < -{self.training_config.test_on_most_recent_x_days}"
        )
        df_test = df_data.query(
            f"days_before_most_recent >= -{self.training_config.test_on_most_recent_x_days}"
        )
        print("len df_test", len(df_test))
        print(
            "df_train date range",
            df_train["case_date"].min(),
            df_train["case_date"].max(),
        )
        print("df_test date range", df_test["case_date"].min(), df_test["case_date"].max())

        # print the number of training and testing points per site.

        # for categorical features, ensure that there are no new categories in df_test
        # This is done to ensure surgeon names aren't in test set that weren't in training.
        # for cat_feature in cat_features:
        #     df_test = df_test[df_test[cat_feature].isin(df_train[cat_feature].unique())]

        self.df_train = df_train
        # remember to remove the columns we don't need for the fit.
        self.df_test = df_test
        return
