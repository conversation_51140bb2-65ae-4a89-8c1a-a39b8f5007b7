import pandera as pa


class StaticStartOffsetFeatureSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    cumsum_scheduled_case_duration_so_far: int | None
    first_primary_surgeon: str | None
    minutes_after_previous_case_scheduled_end: float | None = pa.Field(nullable=True)
    org_id: str | None
    scheduled_starting_hour: int | None
    to_follow_case: int | None
    site_id: str | None


class StaticStartOffsetResultSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    static_start_offset_forecast: float | None
    static_start_offset_version: str
    static_start_offset_service_version: str
    static_start_offset_model_version: str
    # TODO: https://linear.app/apella/issue/FORC-81/add-trace-ids-to-forecast-results-schema
