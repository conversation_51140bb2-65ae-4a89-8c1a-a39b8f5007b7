SHELL := /bin/bash
PORT := 9997

format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-training-locally:
	poetry run python -u -m static_start_offset.training --train --evaluate --config-module-name static_start_offset.configs.experiment_config

run-async-trainer:
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml

run-local:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

.PHONY: format lint test test-cov run-training-locally run-async-trainer dev-local run-local run-uvicorn
