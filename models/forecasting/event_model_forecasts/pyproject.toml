[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
strict = true

[[tool.mypy.overrides]]
module = [
    "google.*",
    "joblib",
]
ignore_missing_imports = true

[tool.poetry]
name = "event_model_forecasts"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "event_model_forecasts"}]

[tool.poetry.dependencies]
python = "~3.10"
pydantic = "^2.5.2"
feature-store = { path="../../../feature_store", develop = true }

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.0"
pytest = "^7.4.2"
mypy = "^1.6.0"
pytest-cov = "^4.1.0"
gitpython = "^3.1.40"
pandas-stubs = "^2.1.4.231227"

[tool.poetry.group.serving.dependencies]
types-cachetools = "^*******"
serving_utils = {path = "../../../serving_utils", develop = true }

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
   # Trust ruff format to get line length right. Without this, there are cases where ruff won't
   # reflow a line that's too long (e.g. comments) and ruff check complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
