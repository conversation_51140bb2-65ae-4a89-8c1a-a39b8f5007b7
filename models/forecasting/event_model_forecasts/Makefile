SHELL := /bin/bash
PORT := 9995

format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest -vv

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

dev-local:
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-local:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

.PHONY: format lint test test-cov dev-local run-local run-uvicorn
