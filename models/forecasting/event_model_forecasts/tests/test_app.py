import os
from datetime import datetime, timed<PERSON>ta
from typing import Any
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd
import pandera as pa
from feature_store.entity_catalog.case import CaseSchema
from feature_store.entity_catalog.event_model_forecast import EventModelForecastSchema

from app.endpoints.predict.predict_api import EventModelForecastService
from utils.types import APIInputs, ManyAPIRequests


@patch.object(
    os,
    "environ",
    {
        "DEV_STUB": "0",
        "FEATURE_STORE_PROJECT": "test_project",
        "FEATURE_STORE_INSTANCE": "test_instance",
    },
)
@patch("app.endpoints.predict.predict_api.BTClient")
@patch("app.endpoints.predict.predict_api.FeatureStore")
@patch("app.endpoints.predict.predict_api.get_features_for_rooms")
@patch("app.endpoints.predict.predict_api.get_features_for_cases")
class TestRoomTimestamps(TestCase):
    event_model_forecast_columns = [
        "inference_timestamp",
        "patient_wheels_out_timestamp",
    ]

    case_columns = ["room"]

    @staticmethod
    @pa.check_types
    def create_event_model_feature_df(
        data: dict[str, list[Any]], *, columns: list[str]
    ) -> pa.typing.DataFrame[EventModelForecastSchema]:
        df = pd.DataFrame.from_dict(data, orient="index", columns=list(columns))
        return pa.typing.DataFrame[EventModelForecastSchema](df)

    @staticmethod
    @pa.check_types
    def create_case_feature_df(
        data: dict[str, list[Any]], *, columns: list[str]
    ) -> pa.typing.DataFrame[CaseSchema]:
        df = pd.DataFrame.from_dict(data, orient="index", columns=list(columns))
        return pa.typing.DataFrame[CaseSchema](df)

    def test_get_event_model_forecasts_valids(
        self,
        get_features_for_cases_mock: MagicMock,
        get_features_for_rooms_mock: MagicMock,
        feature_store_mock: MagicMock,
        mock_bt_client: MagicMock,
    ) -> None:
        service = EventModelForecastService()
        current_time = datetime.fromisoformat("2022-01-01T00:00:00+00:00")
        data = {
            "room1": [current_time, current_time + timedelta(minutes=10)],
            "room2": [current_time, current_time + timedelta(minutes=20)],
        }
        get_features_for_rooms_mock.return_value = self.create_event_model_feature_df(
            data, columns=self.event_model_forecast_columns
        )
        room_timestamps = service._get_validated_event_model_forecasts(data.keys(), current_time)

        # Both rooms should be in the result
        assert room_timestamps.index.tolist() == ["room1", "room2"]
        # The timestamps should be the same as the input
        room1 = room_timestamps.loc["room1"]
        self.assertEqual(room1["inference_timestamp"], current_time)
        self.assertEqual(
            room1["patient_wheels_out_timestamp"], current_time + timedelta(minutes=10)
        )

    def test_get_event_model_forecasts_features_are_none(
        self,
        get_features_for_cases_mock: MagicMock,
        get_features_for_rooms_mock: MagicMock,
        feature_store_mock: MagicMock,
        mock_bt_client: MagicMock,
    ) -> None:
        service = EventModelForecastService()
        current_time = datetime.fromisoformat("2022-01-01T00:00:00+00:00")
        data = {
            "room1": [current_time, None],
            "room2": [current_time, None],
        }
        get_features_for_rooms_mock.return_value = self.create_event_model_feature_df(
            data, columns=self.event_model_forecast_columns
        )
        room_timestamps = service._get_validated_event_model_forecasts(data.keys(), current_time)

        assert room_timestamps.index.tolist() == []

    def test_get_event_model_forecasts_old_inference(
        self,
        get_features_for_cases_mock: MagicMock,
        get_features_for_rooms_mock: MagicMock,
        feature_store_mock: MagicMock,
        mock_bt_client: MagicMock,
    ) -> None:
        service = EventModelForecastService()
        current_time = datetime.fromisoformat("2022-01-01T00:00:00+00:00")
        data = {
            "room1": [current_time, current_time + timedelta(minutes=10)],
            "room2": [current_time - timedelta(minutes=20), current_time + timedelta(minutes=20)],
        }
        get_features_for_rooms_mock.return_value = self.create_event_model_feature_df(
            data, columns=self.event_model_forecast_columns
        )
        room_timestamps = service._get_validated_event_model_forecasts(data.keys(), current_time)

        # Both rooms should be in the result
        assert room_timestamps.index.tolist() == ["room1"]

    def test_get_event_model_forecasts_old_prediction(
        self,
        get_features_for_cases_mock: MagicMock,
        get_features_for_rooms_mock: MagicMock,
        feature_store_mock: MagicMock,
        mock_bt_client: MagicMock,
    ) -> None:
        service = EventModelForecastService()
        current_time = datetime.fromisoformat("2022-01-01T00:00:00+00:00")
        data = {
            "room1": [current_time, current_time + timedelta(minutes=10)],
            "room2": [current_time, current_time - timedelta(minutes=20)],
        }
        get_features_for_rooms_mock.return_value = self.create_event_model_feature_df(
            data, columns=self.event_model_forecast_columns
        )
        room_timestamps = service._get_validated_event_model_forecasts(data.keys(), current_time)

        # Both rooms should be in the result
        assert room_timestamps.index.tolist() == ["room1"]

    def test_predict_many(
        self,
        get_features_for_cases_mock: MagicMock,
        get_features_for_rooms_mock: MagicMock,
        feature_store_mock: MagicMock,
        mock_bt_client: MagicMock,
    ) -> None:
        service = EventModelForecastService()

        # First , setup rooms data
        current_time = datetime.fromisoformat("2022-01-01T00:00:00+00:00")
        event_model_forecast_data: dict[str, list[Any]] = {
            "room1": [current_time, current_time + timedelta(minutes=10)],
            "room2": [current_time, current_time + timedelta(minutes=20)],
            "room_with_no_forecast": [current_time, None],
        }

        get_features_for_rooms_mock.return_value = self.create_event_model_feature_df(
            event_model_forecast_data, columns=self.event_model_forecast_columns
        )

        room_timestamps = service._get_validated_event_model_forecasts(
            event_model_forecast_data.keys(), current_time
        )
        assert room_timestamps.index.tolist() == ["room1", "room2"]

        # Now setup cases data
        case_data = {
            "case1": ["room1"],
            "case2": ["room2"],
            "case3": ["no_room_mapping"],
            "case4": ["room_with_no_forecast"],
        }
        get_features_for_cases_mock.return_value = self.create_case_feature_df(
            case_data, columns=self.case_columns
        )
        inputs = [APIInputs(case_id=case_id) for case_id in case_data.keys()]

        # Now we can predict
        with patch("app.endpoints.predict.predict_api.datetime") as mock_datetime:
            mock_datetime.now.return_value = current_time
            predictions = service.predict_many(ManyAPIRequests(inputs=inputs))

        # get list of case_ids that have predictions
        non_naive_case_ids = [
            pred.case_id for pred in predictions.root if pred.prediction is not None
        ]
        assert set(non_naive_case_ids) == set(["case1", "case2"])

        # get a list of case_ids that got navie predictions
        naive_case_ids = [pred.case_id for pred in predictions.root if pred.prediction is None]
        assert set(naive_case_ids) == set(["case3", "case4"])
