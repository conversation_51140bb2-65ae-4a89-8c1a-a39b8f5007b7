import logging

from google.cloud.bigquery import Client as BQ<PERSON>lient

from training_utils.clearml_reporter import ClearMLReporter

logger = logging.getLogger(__name__)


class DataSelector:
    # Change this class as needed to add the relevant code for selecting data for fit
    def __init__(self, bq_client: BQClient, reporter: ClearMLReporter):
        self.bq_client = bq_client
        self.reporter = reporter

    def generate_data_for_fit(self):
        raise("Method not implemented")
