import argparse
import json
import logging
from datetime import datetime
from importlib import import_module

import joblib
from google.cloud.bigquery import Client as BQClient
from google.cloud.secretmanager import SecretManagerServiceClient
from google.cloud.storage import Client as GCSClient
from training_utils.clearml_reporter import (
    ClearMLReporter,
    get_clearml_task,
)
from training_utils.model_storage import ModelStorage, CONFIG_FILE_NAME
from training_utils.utils import ensure_gcp_auth_is_ready, get_repo_info

from <your_project>.training.data_selector import DataSelector
from <your_project>.training.trainer import ModelTrainer

logger = logging.getLogger(__name__)

DEFAULT_CONFIG_MODULE = "INSERT_PROJECT_NAME_HERE.configs.experiment_config"
MODEL_TYPE = "INSERT_PROJECT_NAME_HERE"

def main() -> None:
    # Initialize logging
    parser = argparse.ArgumentParser()

    parser.add_argument("--train", help="Run training", action="store_true")
    parser.add_argument("--evaluate", help="Evaluate prior run", action="store_true")
    parser.add_argument(
        "--config-module-name",
        type=str,
        help="absolute module name, starting from PACKAGE_ROOT and using '.' as delimiter, not '/'",
        default=DEFAULT_CONFIG_MODULE,
    )
    parser.add_argument("--is-automated-training", default=False, action="store_true")

    args = parser.parse_args()
    if args.train:
        logger.info("We will run the training")
    else:
        logger.info("Will not train")

    # Load utilities
    ensure_gcp_auth_is_ready()
    bq_client = BQClient()
    gcs_client = GCSClient()

    # Load the TrainingConfig
    # SKELETON_TODO: Create your model config and load it.
    # Load the TrainingConfig
    model_training_config = import_module(args.config_module_name).ModelTrainingConfig()
    if args.is_automated_training:
        # set the variables for an automated fit
        fit_time = datetime.now()
        repo = get_repo_info()
        model_identifier = f"automatic_production_training/{fit_time.date()}"
        model_training_config.clearml_config.project_name = (
            f"{model_training_config.clearml_config.project_name}: Automated Fit"
        )
        model_training_config.clearml_config.task_name = f"{fit_time.date()} Production Fit"
        model_training_config.clearml_config.tags = {
            "date": str(fit_time),
            "branch_name": repo.active_branch.name,
            "sha": repo.head.object.hexsha,
            "short_sha": repo.git.rev_parse(repo.head.object.hexsha, short=7),
            "model_type": MODEL_TYPE,
            "model_identifier": model_identifier,
        }
        model_training_config.model_identifier = model_identifier

    # Load the ClearML credentials
    secret_manager_client = SecretManagerServiceClient()
    clearml_task = get_clearml_task(secret_manager_client, model_training_config.clearml_config)
    reporter = ClearMLReporter(model_training_config.model_dump(), clearml_task)

    # Load the data
    print("loading the data")
    # SKELETON_TODO: load your data loader here.
    data_selector = DataSelector(bq_client, model_training_config.data_selection_config, reporter)
    data = data_selector.generate_data_for_fit()
    print("Data loading complete")

    # Define our model
    # SKELETON_TODO: Define and load your model
    # model = YOURMODELHERE()

    # Set up the model storage
    model_storage = ModelStorage(
        gcs_client,
        model_type=MODEL_TYPE,
        model_identifier=model_training_config.model_identifier,
    )
    trainer = ModelTrainer(model, model_training_config.training_config, data, reporter)  # noqa

    if args.train:
        print("Training model")
        # Train our model
        fit_models = trainer.train()

        # Use this template for storing your model -- but you will have to change it.
        for location in model_training_config.training_config.locations_to_fit:
            filename = f"{model_storage.model_type}_{location}.joblib"
            local_path = model_storage._get_local_data_file_location(filename)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            joblib.dump(fit_models[location], local_path)
        # write our config out as well
        config_local_file_path = model_storage._get_local_data_file_location(CONFIG_FILE_NAME)
        json.dump(model_training_config.dict(), open(config_local_file_path, "w"))
        model_storage.upload_to_google_storage()

    if args.evaluate:
        print("Evaluating model")
        # Load the saved model
        fit_models = model_storage.load_models()

        # Evaluate results
        # SKELETON_TODO: create an evaluator class in /models/
        evaluator = Evaluator(  # noqa
            fit_model,  # noqa
            trainer.test_data,
            model_training_config.training_config,
            reporter,
        )
        evaluator.evaluate_models()


if __name__ == "__main__":
    main()
