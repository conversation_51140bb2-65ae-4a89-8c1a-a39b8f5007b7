SHELL := /bin/bash

format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run python -m pytest

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-training-locally:
	poetry run python -u -m $project_name.training --train --evaluate --config-module-name $project_name.configs.experiment_config


run-async-trainer:
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml
