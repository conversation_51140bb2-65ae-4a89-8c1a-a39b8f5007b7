from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class TrainingConfig(BaseModel):
    training_config_input: str = "default"


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "CHANGE ME"
    task_name: str = "Experiment Training"
    tags: dict[str, str] = {"label": "fun label"}


class ModelTrainingConfig(BaseModel):
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "experiment_model_saving_bucket"

