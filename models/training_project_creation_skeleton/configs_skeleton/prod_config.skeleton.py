from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class TrainingConfig(BaseModel):
    training_config_input: str = "default"


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "CHANGE ME"
    task_name: str = "Production Training"
    tags: dict[str, str] = {"params": "200"}


class ModelTrainingConfig(BaseModel):
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "production_model_saving_bucket"

