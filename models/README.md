# Model Development

## How to set up a new ML model project

1. Pick a project name (i.e., "case_duration")
2. Determine if your project should live in a sub-directory or the models directory of this repo (i.e, models/forecasting/ vs models/)
3. Determine if your training needs a GPU. If you don't know for sure, the answer is no.

Type:
python create_training_project

From there, you will be prompted a few questions (on the above). And then your project will be created.

Note: you will then have to go into your new project directory (`cd {directory}/{project_name}`) and add whatever libraries you need using `poetry add ...`

VOILA!


Conversely, you could set it up without entering the interactive mode, but that's beyond the scope here.
`python create_training_project --no-gpu --no-parent-dir --project-name my_project_name`


## What do I do next?

The new project has created some rudimentary files related to training, namely:
- training/__main__.py
- training/trainer.py
- training/data_selector.py

Code within those files to make it match what you need.

To run your training locally, you will then need to do:
`make run-training-locally`

To run it asynchronously, do:
`make run-async-trainer`


## FAQ

### I followed these directions for setting up my model training project, and my model is awesome and is now in production - How do I set it to train automa(gi)cally?

Our automated training runs on a schedule in dagster. If you have been
able to run a training asynchronously in dagster, that means your
docker image is built properly and that you can easily port your model
over to be trained automatically.  This does require the model
training executable to have a `is-automated-training` argument that
will update some clearml and model saver parameters. You can look in
`standalone-case-duration/training/__main__.py` to see how those are
set.  If those conditions are met, to add this model to our retraining
list, all you have to do is go to the `mlops-dags` repo and set up a
schedule in `generic_ml_training/dags/pipeline_definitions.py`.

Go into that file, and modify it to add your schedule as:
```
@schedule(job=run_training_job, cron_schedule="0 0 * * SUN", execution_timezone="UTC")
def $MY_MODEL_NAME_retraining_schedule() -> RunRequest:
    run_config = RunTrainingConfig(
        image_name=$MY_MODEL_IMAGE_LOCATION, # i.e. "ml-services/models/forecasting/case_duration/training",
        image_tag="latest-training",
        gpu_type=None,
        k8s_job_service_account=get_deployment_job_service_account(),
        container_args=["--is-automated-training"],
    )
    run_request = RunRequest(
        run_config=RunConfig(ops={"run_generic_training": run_config}),
        tags={"training": "automatic", "model": "STRING REPRENTING SOME INFO ON MY MODEL THAT I WANT TO SEE IN A CLEARML TAG"},
    )
    return run_request
```

Create a PR with that new job, and once you deploy that repo, turn the
schedule on. That's all you should have to do to get your models
retraining on a regular basis.
