# As of v462.0.1, `gcloud builds submit` does not respect .gitignore files that are embedded in
# various tool cache directories, which is apparently becoming a trend in tool design. In order to
# avoid needlessly uploading hundreds of megabytes of cache, we explictly ignore those caches here.
# We could also have just added to tool caches directories to our .gitignore, but it seems a little
# silly to do that in order to work around a particular tool's deficiency.

# Ignore everything from .gitignore and .gitignore file itself
#!include:.gitignore
.gitignore

# Tool caches
.mypy_cache
.ruff_cache
.pytest_cache

# Git
.git

# yolo models
yolo*.pt