# ml-services

This repo contains various ML-related projects (i.e. it will not be used for a single project),
including both models and services which use those models for product purposes.

The development and training environment for individual ML models can be found in the `models/`
directory. See [models/README.md](models/README.md) for more information.

Other top-level directories in this repository include services which consume the ML models and shared tools.

## Setting Up Git Hooks

After cloning the repository, run the following command to install the pre-commit hooks:

```bash
pip install pre-commit 
pre-commit install
```
